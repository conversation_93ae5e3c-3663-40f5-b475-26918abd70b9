# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
*.egg-info/
*.egg
.Python
pip-log.txt
pip-delete-this-directory.txt

# Django specific
*.log
db.sqlite3
db.sqlite3-journal
media/
staticfiles/
local_settings.py
.env
*.env

# Pytest / Coverage
htmlcov/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# MyPy
.mypy_cache/
.dmypy.json
.dmypy/

# VSCode
.vscode/

# JetBrains (PyCharm)
.idea/

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
