# PhotoFish Application

## Overview
PhotoFish is an application that demonstrates automated photo distribution using facial recognition. The application allows users to receive their event photos automatically by matching their facial data with photos uploaded by photographers.

## Technology Stack
- **Backend**: Django REST Framework
- **Database**: PostgreSQL
- **Storage**: AWS S3
- **Face Recognition**: face-recognition library
- **Authentication**: Django REST Framework JWT

## Installation

### 1. <PERSON>lone the Repository
```bash
git clone https://github.com/musnet/PhotoFish-Backend.git
cd photofish-backend
```

### 2. Set Up Virtual Environment
```bash
Create virtual environment
python -m venv venv

Activate virtual environment
On Windows:
venv\Scripts\activate
On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt

4. Run Development Server
```bash
python manage.py runserver
```

## Development Guidelines

### Code Style
- Follow PEP 8 guidelines
- Use meaningful variable and function names
- Include docstrings for all functions and classes
- Write unit tests for new features

### Git Workflow
1. Create a new branch for each feature
2. Follow conventional commits
3. Submit pull requests for review
4. Squash commits before merging

### Testing
```bash
# Run tests
python manage.py test

# Run with coverage
coverage run manage.py test
coverage report
```

## Deployment

### Using Docker
```bash
# Build image
docker build -t photofish .

# Run container
docker run -p 8000:8000 photofish
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License
This project is licensed under the MIT License - see the LICENSE.md file for details.

## Acknowledgments
- Face Recognition library
- Django REST Framework
- AWS S3 for storage solution
