from django.contrib import admin
from .models import Event, EventUserRole, EventPhoto, EventAttendance


class EventPhotoInline(admin.TabularInline):
    model = EventPhoto
    extra = 0
    readonly_fields = ['id', 'uploaded_at', 'processed_for_faces']
    fields = ['id', 'image', 'photographer', 'price', 'uploaded_at', 'processed_for_faces']


class EventAttendanceInline(admin.TabularInline):
    model = EventAttendance
    extra = 0
    readonly_fields = ['id', 'joined_at']
    fields = ['id', 'user', 'is_attending', 'attendance_method', 'joined_at', 'jersey_number']


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'creator', 'event_type', 'start_date', 'end_date', 'visibility', 'total_photos']
    list_filter = ['event_type', 'visibility', 'start_date']
    search_fields = ['name', 'description', 'location']
    readonly_fields = ['id', 'created_at', 'updated_at', 'total_photos', 'tagged_photos']
    filter_horizontal = ['photographers']
    inlines = [EventAttendanceInline, EventPhotoInline]
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'name', 'creator', 'description', 'event_type', 'visibility')
        }),
        ('Dates and Location', {
            'fields': ('start_date', 'end_date', 'location', 'latitude', 'longitude')
        }),
        ('Settings', {
            'fields': ('subscription', 'photographers', 'image_price_limit', 'allow_user_uploads', 'requires_jersey_number')
        }),
        ('Media', {
            'fields': ('banner_image', 'qr_code')
        }),
        ('Statistics', {
            'fields': ('total_photos', 'tagged_photos', 'created_at', 'updated_at')
        }),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.prefetch_related('photographers', 'attendances')


@admin.register(EventAttendance)
class EventAttendanceAdmin(admin.ModelAdmin):
    list_display = ['id', 'event', 'user', 'is_attending', 'attendance_method', 'joined_at']
    list_filter = ['is_attending', 'attendance_method', 'joined_at']
    search_fields = ['event__name', 'user__email', 'user__username']
    readonly_fields = ['id', 'joined_at']


@admin.register(EventPhoto)
class EventPhotoAdmin(admin.ModelAdmin):
    list_display = ['id', 'event', 'photographer', 'uploaded_at', 'processed_for_faces']
    list_filter = ['uploaded_at', 'processed_for_faces']
    search_fields = ['event__name', 'photographer__email', 'photographer__username']
    readonly_fields = ['id', 'uploaded_at', 'detected_faces']
    fieldsets = (
        ('Photo Information', {
            'fields': ('id', 'event', 'photographer', 'image', 'price')
        }),
        ('Processing Status', {
            'fields': ('processed_for_faces', 'detected_faces', 'uploaded_at')
        }),
    )