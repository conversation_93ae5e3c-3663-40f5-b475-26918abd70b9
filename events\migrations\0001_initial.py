# Generated by Django 5.1.5 on 2025-06-30 01:18

import uuid
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('pricing_mode', models.CharField(choices=[('FIXED', 'Fixed Price (Organizer Sets Single Price)'), ('CAPPED', 'Price Cap (Photographers Price Below Cap)'), ('FREE', 'Free Pricing (All Photos Free)')], default='FREE', help_text='How photo pricing is controlled for this event', max_length=10)),
                ('fixed_photo_price', models.DecimalField(blank=True, decimal_places=2, help_text='Fixed price for all photos when pricing_mode=FIXED (0-100)', max_digits=5, null=True)),
                ('price_cap', models.DecimalField(blank=True, decimal_places=2, help_text='Maximum price photographers can set when pricing_mode=CAPPED', max_digits=5, null=True)),
                ('freemium_enabled', models.BooleanField(default=False, help_text='Allow attendees to download free images with limitations')),
                ('freemium_type', models.CharField(choices=[('WATERMARK', 'Free Downloads with Watermark'), ('DOWNSCALE', 'Free Downloads with Lower Quality')], default='WATERMARK', help_text='Type of limitation for free downloads', max_length=15)),
                ('watermark_text', models.CharField(blank=True, default='PhotoFish - Event Photo', help_text='Text to use for watermark when freemium_type=WATERMARK', max_length=100)),
                ('downscale_quality', models.IntegerField(default=50, help_text='Image quality percentage for downscaled images (10-80)')),
                ('downscale_max_width', models.IntegerField(default=800, help_text='Maximum width in pixels for downscaled images')),
                ('explicit_content_filter', models.BooleanField(default=True, help_text='Filter out explicit content from event photos')),
                ('children_photos_filter', models.BooleanField(default=False, help_text='Filter out photos containing children')),
                ('ai_color_grading', models.BooleanField(default=False, help_text='Apply AI color grading to event photos')),
                ('best_selection_only', models.BooleanField(default=False, help_text='Show only AI-selected best photos')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('banner_image', models.ImageField(upload_to='event_banners/')),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('description', models.TextField(blank=True)),
                ('location', models.CharField(max_length=255)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('event_type', models.CharField(choices=[('WEDDING', 'Wedding'), ('SPORTS', 'Sports'), ('EXPO', 'Expo'), ('OTHERS', 'Others')], default='OTHERS', max_length=50)),
                ('image_price_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('allow_user_uploads', models.BooleanField(default=False)),
                ('qr_code', models.ImageField(blank=True, null=True, upload_to='event_qr_codes/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_photos', models.IntegerField(default=0)),
                ('tagged_photos', models.IntegerField(default=0)),
                ('visibility', models.CharField(choices=[('PUBLIC', 'Public'), ('PRIVATE', 'Private')], default='PUBLIC', max_length=10)),
                ('requires_jersey_number', models.BooleanField(default=False)),
                ('paying_type', models.CharField(choices=[('Per Hour', 'Per Hour'), ('Fixed Rate', 'Fixed Rate')], default='Per Hour', max_length=50)),
                ('photographer_slots', models.IntegerField(default=5)),
                ('filled_slots', models.IntegerField(default=0)),
            ],
            options={
                'db_table': 'event',
            },
        ),
        migrations.CreateModel(
            name='EventAttendance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('is_attending', models.BooleanField(default=True)),
                ('attendance_method', models.CharField(choices=[('QR_CODE', 'QR Code'), ('INVITE', 'Invitation'), ('SELF_JOIN', 'Self Join')], default='SELF_JOIN', max_length=50)),
                ('jersey_number', models.CharField(blank=True, max_length=20, null=True)),
            ],
            options={
                'db_table': 'event_attendance',
            },
        ),
        migrations.CreateModel(
            name='EventPhoto',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(upload_to='event_photos/')),
                ('price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('detected_faces', models.JSONField(blank=True, null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('processed_for_faces', models.BooleanField(default=False)),
                ('photographer_price', models.DecimalField(blank=True, decimal_places=2, help_text='Price set by photographer', max_digits=8, null=True)),
                ('final_price', models.DecimalField(blank=True, decimal_places=2, help_text='Final price based on event pricing mode', max_digits=8, null=True)),
                ('photo_title', models.CharField(blank=True, help_text='Photo title', max_length=200)),
                ('photo_description', models.TextField(blank=True, help_text='Photo description')),
                ('is_featured', models.BooleanField(default=False, help_text='Featured photo for event')),
            ],
            options={
                'db_table': 'event_photo',
            },
        ),
        migrations.CreateModel(
            name='EventUserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('ATTENDEE', 'Event Attendee'), ('ORGANIZER', 'Event Organizer'), ('PHOTOGRAPHER', 'Event Photographer')], default='ATTENDEE', max_length=15)),
                ('assigned_via', models.CharField(choices=[('USER_APP', 'User App Join'), ('QR_CODE', 'QR Code Scan'), ('INVITE', 'Invitation Link'), ('SELF_JOIN', 'Self Join'), ('CREATOR', 'Event Creator')], default='USER_APP', max_length=20)),
                ('joined_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('jersey_number', models.CharField(blank=True, max_length=20, null=True)),
                ('can_view_tagged_photos', models.BooleanField(default=True)),
                ('can_purchase_photos', models.BooleanField(default=True)),
                ('can_manage_event', models.BooleanField(default=False)),
                ('can_view_analytics', models.BooleanField(default=False)),
                ('can_assign_roles', models.BooleanField(default=False)),
                ('can_download_hd_free', models.BooleanField(default=False)),
                ('can_view_all_photos', models.BooleanField(default=False)),
                ('can_remove_content', models.BooleanField(default=False)),
                ('can_invite_photographers', models.BooleanField(default=False)),
                ('can_set_filters', models.BooleanField(default=False)),
                ('can_upload_photos', models.BooleanField(default=False)),
                ('can_set_photo_prices', models.BooleanField(default=False)),
                ('can_view_earnings', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'event_user_role',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('notification_type', models.CharField(choices=[('payment_received', 'Payment Received'), ('payment_pending', 'Payment Pending'), ('payment_disbursed', 'Payment Disbursed'), ('event_accepted', 'Event Accepted'), ('event_invitation', 'Event Invitation'), ('photo_processed', 'Photo Processed')], max_length=50)),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('event_name', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'photographer_notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PhotographerEarnings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo_price', models.DecimalField(decimal_places=2, help_text='Final price of photo', max_digits=8)),
                ('platform_commission', models.DecimalField(decimal_places=2, help_text="Platform's commission", max_digits=8)),
                ('photographer_share', models.DecimalField(decimal_places=2, help_text="Photographer's earnings", max_digits=8)),
                ('commission_rate', models.DecimalField(decimal_places=2, default=30.0, help_text='Platform commission rate %', max_digits=5)),
                ('transaction_date', models.DateTimeField(auto_now_add=True)),
                ('payout_status', models.CharField(choices=[('PENDING', 'Pending Payout'), ('PROCESSED', 'Payout Processed'), ('FAILED', 'Payout Failed')], default='PENDING', max_length=20)),
                ('payout_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'photographer_earnings',
            },
        ),
        migrations.CreateModel(
            name='PhotographerEventApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(help_text='Why photographer wants to photograph this event')),
                ('equipment_list', models.TextField(blank=True, help_text="Equipment they'll bring to event")),
                ('proposed_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Proposed hourly rate', max_digits=8, null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('WITHDRAWN', 'Withdrawn by Photographer')], default='PENDING', max_length=20)),
                ('applied_at', models.DateTimeField(auto_now_add=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('review_notes', models.TextField(blank=True, help_text="Organizer's review notes")),
            ],
            options={
                'db_table': 'photographer_event_application',
            },
        ),
        migrations.CreateModel(
            name='PhotographerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bio', models.TextField(blank=True, help_text='Professional bio', max_length=500)),
                ('specializations', models.JSONField(default=list, help_text="Photography specializations like ['wedding', 'sports', 'portrait']")),
                ('equipment', models.TextField(blank=True, help_text='Camera equipment description')),
                ('hourly_rate', models.DecimalField(blank=True, decimal_places=2, help_text='Hourly rate in USD', max_digits=8, null=True)),
                ('portfolio_images', models.JSONField(default=list, help_text='List of portfolio image URLs')),
                ('is_verified', models.BooleanField(default=False, help_text='Whether photographer is verified')),
                ('verification_notes', models.TextField(blank=True, help_text='Admin notes about verification')),
                ('total_events', models.IntegerField(default=0, help_text='Total events photographed')),
                ('total_photos_uploaded', models.IntegerField(default=0, help_text='Total photos uploaded')),
                ('total_earnings', models.DecimalField(decimal_places=2, default=0.0, help_text='Total earnings', max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'photographer_profile',
            },
        ),
        migrations.CreateModel(
            name='PhotoSale',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sale_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('photographer_commission', models.DecimalField(decimal_places=2, max_digits=10)),
                ('platform_fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('payment_status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('REFUNDED', 'Refunded')], default='PENDING', max_length=20)),
                ('is_paid_to_photographer', models.BooleanField(default=False)),
                ('sold_at', models.DateTimeField(auto_now_add=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'photo_sales_simple',
                'ordering': ['-sold_at'],
            },
        ),
    ]
