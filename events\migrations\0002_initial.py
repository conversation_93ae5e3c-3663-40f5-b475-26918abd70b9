# Generated by Django 5.1.5 on 2025-06-30 01:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('events', '0001_initial'),
        ('subscriptions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='event',
            name='creator',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='event',
            name='photographers',
            field=models.ManyToManyField(blank=True, related_name='assigned_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='event',
            name='subscription',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='subscriptions.subscription'),
        ),
        migrations.AddField(
            model_name='eventattendance',
            name='event',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='events.event'),
        ),
        migrations.AddField(
            model_name='eventattendance',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attended_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='eventphoto',
            name='event',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photos', to='events.event'),
        ),
        migrations.AddField(
            model_name='eventphoto',
            name='photographer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_photos', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='event',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='events.event'),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_roles', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='notification',
            name='event',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications', to='events.event'),
        ),
        migrations.AddField(
            model_name='notification',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photographerearnings',
            name='event',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photographer_earnings', to='events.event'),
        ),
        migrations.AddField(
            model_name='photographerearnings',
            name='photo',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earnings', to='events.eventphoto'),
        ),
        migrations.AddField(
            model_name='photographerearnings',
            name='photographer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='earnings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photographerearnings',
            name='purchased_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='photo_purchases', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photographereventapplication',
            name='event',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='photographer_applications', to='events.event'),
        ),
        migrations.AddField(
            model_name='photographereventapplication',
            name='photographer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_applications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photographereventapplication',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_applications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photographerprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='photographer_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photosale',
            name='buyer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchases', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='photosale',
            name='event',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='photo_sales', to='events.event'),
        ),
        migrations.AddField(
            model_name='photosale',
            name='photo',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to='events.eventphoto'),
        ),
        migrations.AddField(
            model_name='photosale',
            name='photographer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='photo_sales', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='eventattendance',
            unique_together={('event', 'user')},
        ),
        migrations.AddIndex(
            model_name='eventuserrole',
            index=models.Index(fields=['event', 'role'], name='event_user__event_i_f26475_idx'),
        ),
        migrations.AddIndex(
            model_name='eventuserrole',
            index=models.Index(fields=['user', 'role'], name='event_user__user_id_423116_idx'),
        ),
        migrations.AddIndex(
            model_name='eventuserrole',
            index=models.Index(fields=['user', 'event'], name='event_user__user_id_503003_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='eventuserrole',
            unique_together={('user', 'event', 'role')},
        ),
        migrations.AddIndex(
            model_name='photographerearnings',
            index=models.Index(fields=['photographer', 'transaction_date'], name='photographe_photogr_a86750_idx'),
        ),
        migrations.AddIndex(
            model_name='photographerearnings',
            index=models.Index(fields=['event', 'transaction_date'], name='photographe_event_i_c1b1b9_idx'),
        ),
        migrations.AddIndex(
            model_name='photographerearnings',
            index=models.Index(fields=['payout_status'], name='photographe_payout__aab73f_idx'),
        ),
        migrations.AddIndex(
            model_name='photographereventapplication',
            index=models.Index(fields=['event', 'status'], name='photographe_event_i_ebc5a7_idx'),
        ),
        migrations.AddIndex(
            model_name='photographereventapplication',
            index=models.Index(fields=['photographer', 'status'], name='photographe_photogr_58eeb4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='photographereventapplication',
            unique_together={('photographer', 'event')},
        ),
    ]
