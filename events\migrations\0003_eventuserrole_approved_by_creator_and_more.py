# Generated by Django 5.1.5 on 2025-06-30 14:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('events', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='eventuserrole',
            name='approved_by_creator',
            field=models.Bo<PERSON>anField(default=True, help_text='Whether photographer is approved by event creator'),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='can_delete_own_photos',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='can_edit_own_photos',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='portfolio_link',
            field=models.URLField(blank=True, help_text="Photographer's portfolio link"),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='specialization',
            field=models.Char<PERSON>ield(blank=True, help_text='Photography specialization', max_length=100),
        ),
    ]
