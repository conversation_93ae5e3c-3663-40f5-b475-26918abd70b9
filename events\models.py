# At the top of events/models.py, after existing imports
from django.db import models
import uuid
from django.conf import settings
import math
from django.core.validators import MinValueValidator, MaxValueValidator
from datetime import datetime
from django.utils import timezone
# At the top of events/models.py, make sure you have:
from decimal import Decimal



class Event(models.Model):
    # Event type choices
    class EventType(models.TextChoices):
        WEDDING = 'WEDDING', 'Wedding'
        SPORTS = 'SPORTS', 'Sports'
        EXPO = 'EXPO', 'Expo'
        OTHERS = 'OTHERS', 'Others'
    
    # Event visibility choices
    class EventVisibility(models.TextChoices):
        PUBLIC = 'PUBLIC', 'Public'
        PRIVATE = 'PRIVATE', 'Private'
    
    # Pricing scenario choices
    class PricingMode(models.TextChoices):
        FIXED = 'FIXED', 'Fixed Price (Organizer Sets Single Price)'
        CAPPED = 'CAPPED', 'Price Cap (Photographers Price Below Cap)'  
        FREE = 'FREE', 'Free Pricing (All Photos Free)'
    
    # Freemium options
    class FreemiumType(models.TextChoices):
        WATERMARK = 'WATERMARK', 'Free Downloads with Watermark'
        DOWNSCALE = 'DOWNSCALE', 'Free Downloads with Lower Quality'
    
    # Organizer pricing controls
    pricing_mode = models.CharField(
        max_length=10,
        choices=PricingMode.choices, 
        default=PricingMode.FREE,
        help_text="How photo pricing is controlled for this event"
    )
    
    # For FIXED mode - organizer sets exact price ($0-$100)
    fixed_photo_price = models.DecimalField(
        max_digits=5, decimal_places=2,
        null=True, blank=True,
        help_text="Fixed price for all photos when pricing_mode=FIXED (0-100)"
    )
    
    # For CAPPED mode - organizer sets maximum price
    price_cap = models.DecimalField(
        max_digits=5, decimal_places=2,
        null=True, blank=True,
        help_text="Maximum price photographers can set when pricing_mode=CAPPED"
    )
    
    # Freemium controls
    freemium_enabled = models.BooleanField(
        default=False,
        help_text="Allow attendees to download free images with limitations"
    )
    
    freemium_type = models.CharField(
        max_length=15,
        choices=FreemiumType.choices,
        default=FreemiumType.WATERMARK,
        help_text="Type of limitation for free downloads"
    )
    
    # Freemium settings
    watermark_text = models.CharField(
        max_length=100,
        default="PhotoFish - Event Photo",
        blank=True,
        help_text="Text to use for watermark when freemium_type=WATERMARK"
    )
    
    downscale_quality = models.IntegerField(
        default=50,
        help_text="Image quality percentage for downscaled images (10-80)"
    )
    
    downscale_max_width = models.IntegerField(
        default=800,
        help_text="Maximum width in pixels for downscaled images"
    )
    
    # Organizer AI filter controls
    explicit_content_filter = models.BooleanField(
        default=True,
        help_text="Filter out explicit content from event photos"
    )
    children_photos_filter = models.BooleanField(
        default=False,
        help_text="Filter out photos containing children"
    )
    ai_color_grading = models.BooleanField(
        default=False,
        help_text="Apply AI color grading to event photos"
    )
    best_selection_only = models.BooleanField(
        default=False,
        help_text="Show only AI-selected best photos"
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_events')
    name = models.CharField(max_length=255)
    subscription = models.ForeignKey('subscriptions.Subscription', on_delete=models.SET_NULL, null=True, blank=True)
    banner_image = models.ImageField(upload_to='event_banners/')
    start_date = models.DateField(null=True, blank=True)  # Optional start date
    end_date = models.DateField(null=True, blank=True)    # Optional end date
    description = models.TextField(blank=True)
    location = models.CharField(max_length=255)
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    event_type = models.CharField(max_length=50, choices=EventType.choices, default=EventType.OTHERS)
    photographers = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='assigned_events', blank=True)
    image_price_limit = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    allow_user_uploads = models.BooleanField(default=False)
    qr_code = models.ImageField(upload_to='event_qr_codes/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    total_photos = models.IntegerField(default=0)
    tagged_photos = models.IntegerField(default=0)
    visibility = models.CharField(max_length=10, choices=EventVisibility.choices, default=EventVisibility.PUBLIC)
    requires_jersey_number = models.BooleanField(default=False)  # For sports events
    paying_type = models.CharField(max_length=50, default='Per Hour', 
        choices=[('Per Hour', 'Per Hour'), ('Fixed Rate', 'Fixed Rate')])
    photographer_slots = models.IntegerField(default=5)
    filled_slots = models.IntegerField(default=0)
    
    # Helper methods for organizer functionality
    def get_organizer(self):
        """Get the organizer of this event (should be only one)"""
        try:
            return self.user_roles.get(role=EventUserRole.RoleChoices.ORGANIZER, is_active=True)
        except EventUserRole.DoesNotExist:
            return None
    
    def is_user_organizer(self, user):
        """Check if user is the organizer of this event"""
        return self.user_roles.filter(
            user=user, 
            role=EventUserRole.RoleChoices.ORGANIZER,
            is_active=True
        ).exists()
    
    def add_organizer(self, user, assigned_via='CREATOR'):
        """Add user as organizer to this event"""
        # Check if there's already an organizer
        existing_organizer = self.get_organizer()
        if existing_organizer and existing_organizer.user != user:
            raise ValueError("Event already has an organizer")
        
        role, created = EventUserRole.objects.get_or_create(
            user=user,
            event=self,
            role=EventUserRole.RoleChoices.ORGANIZER,
            defaults={
                'assigned_via': assigned_via,
                'can_manage_event': True,
                'can_view_analytics': True,
                'can_assign_roles': True,
                'can_download_hd_free': True,
                'can_view_all_photos': True,
                'can_remove_content': True,
                'can_invite_photographers': True,
                'can_set_filters': True,
                'can_view_tagged_photos': True,
                'can_purchase_photos': False,  # Organizers get free HD access
            }
        )
        return role
    
    # Freemium validation methods
    def validate_freemium_settings(self):
        """Validate freemium settings"""
        if self.freemium_enabled:
            if self.freemium_type == self.FreemiumType.WATERMARK:
                if not self.watermark_text or len(self.watermark_text.strip()) == 0:
                    raise ValueError("Watermark text required when freemium_type is WATERMARK")
            
            elif self.freemium_type == self.FreemiumType.DOWNSCALE:
                if not (10 <= self.downscale_quality <= 80):
                    raise ValueError("Downscale quality must be between 10-80%")
                if not (200 <= self.downscale_max_width <= 1920):
                    raise ValueError("Downscale max width must be between 200-1920 pixels")
    
    def get_download_options_for_user(self, user):
        """Get available download options for a user"""
        is_organizer = self.is_user_organizer(user)
        
        options = {
            'hd_free': is_organizer,  # Only organizers get free HD
            'freemium_available': self.freemium_enabled and not is_organizer,
            'freemium_type': self.freemium_type if self.freemium_enabled else None,
            'requires_payment': not is_organizer and (not self.freemium_enabled or self.pricing_mode != self.PricingMode.FREE)
        }
        
        return options
    
    def validate_pricing(self):
        """Validate pricing settings based on mode"""
        if self.pricing_mode == self.PricingMode.FIXED:
            if not self.fixed_photo_price:
                raise ValueError("Fixed price required for FIXED pricing mode")
            if not (0 <= self.fixed_photo_price <= 100):
                raise ValueError("Fixed price must be between $0-$100")
                
        elif self.pricing_mode == self.PricingMode.CAPPED:
            if not self.price_cap:
                raise ValueError("Price cap required for CAPPED pricing mode")
            if not (0 < self.price_cap <= 100):
                raise ValueError("Price cap must be between $0.01-$100")
    
    def save(self, *args, **kwargs):
        """Override save to validate settings and auto-assign creator as organizer"""
        self.validate_pricing()
        self.validate_freemium_settings()

        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            self.add_organizer(self.creator, assigned_via='CREATOR')
    
    # Existing helper methods
    def get_user_role(self, user):
        """Get user's role in this event (returns EventUserRole object or None)"""
        try:
            return EventUserRole.objects.get(
                user=user,
                event=self,
                is_active=True
            )
        except EventUserRole.DoesNotExist:
            return None
    
    def is_user_attendee(self, user):
        """Check if user is an attendee of this event"""
        return EventUserRole.objects.filter(
            user=user,
            event=self,
            role='ATTENDEE',
            is_active=True
        ).exists()
    
    def add_attendee(self, user, assigned_via='USER_APP', jersey_number=None):
        """Add user as attendee to this event"""
        attendee_role, created = EventUserRole.objects.get_or_create(
            user=user,
            event=self,
            role='ATTENDEE',
            defaults={
                'assigned_via': assigned_via,
                'jersey_number': jersey_number,
                'is_active': True
            }
        )
        return attendee_role, created
    
    def get_attendees(self):
        """Get all attendees of this event"""
        return EventUserRole.objects.filter(
            event=self,
            role='ATTENDEE',
            is_active=True
        ).select_related('user')

    class Meta:
        db_table = 'event'
    
    def __str__(self):
        return self.name
    
    def is_paid_subscription(self):
        """Check if the event has a paid subscription"""
        if not self.subscription:
            return False
        return self.subscription.name != 'FREE'
    
    def update_photo_counts(self):
        """Update the photo counts for this event"""
        try:
            # Count total photos
            self.total_photos = EventPhoto.objects.filter(event=self).count()
            
            # Count photos with matched users (using FaceMatchResult)
            from facial_recognition.models import FaceMatchResult
            matched_photo_ids = FaceMatchResult.objects.filter(
                event_photo__event=self.id  # Use self.id instead of self
            ).values_list('event_photo_id', flat=True).distinct()
            
            self.tagged_photos = len(matched_photo_ids)
            self.save(update_fields=['total_photos', 'tagged_photos'])
        except Exception as e:
            # If photo count update fails, just log and continue
            import logging
            logger = logging.getLogger('events')
            logger.error(f"Error updating photo counts for event {self.id}: {str(e)}")
    
    @staticmethod
    def calculate_distance(lat1, lon1, lat2, lon2):
        """Calculate the Haversine distance between two points in km"""
        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Haversine formula
        dlon = lon2_rad - lon1_rad
        dlat = lat2_rad - lat1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        # Earth's radius in kilometers
        radius = 6371
        
        # Calculate the distance
        distance = radius * c
        return distance
        
    @classmethod
    def get_events_nearby(cls, lat, lng, radius_km=10):
        """Simplified nearby event search"""
        # This is a very simplified approach using a basic square bounding box
        # ~1 degree is roughly 111km at the equator, adjust based on location
        degree_adjustment = radius_km / 111.0
        
        # Get events within rough bounding box
        potential_events = cls.objects.filter(
            latitude__isnull=False,
            longitude__isnull=False,
            latitude__gte=lat - degree_adjustment,
            latitude__lte=lat + degree_adjustment,
            longitude__gte=lng - degree_adjustment,
            longitude__lte=lng + degree_adjustment,
            visibility=cls.EventVisibility.PUBLIC  # Only public events
        )
        
        # Filter by actual distance using Haversine formula
        nearby_events = []
        for event in potential_events:
            distance = cls.calculate_distance(lat, lng, event.latitude, event.longitude)
            if distance <= radius_km:
                event.distance = distance  # Attach the distance to the event object
                nearby_events.append(event)
        
        # Sort by start date
        return sorted(nearby_events, key=lambda e: e.start_date if e.start_date else datetime.max.date())

    # Photographer helper methods
    def add_photographer(self, user, assigned_via='PHOTOGRAPHER_APP'):
        """Add user as photographer to this event"""
        photographer_role, created = EventUserRole.objects.get_or_create(
            user=user,
            event=self,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER,
            defaults={
                'assigned_via': assigned_via,
                'can_upload_photos': True,
                'can_set_photo_prices': True,
                'can_view_earnings': True,
                'can_view_tagged_photos': True,
                'can_purchase_photos': True,
            }
        )
        return photographer_role

    def is_user_photographer(self, user):
        """Check if user is a photographer for this event"""
        return EventUserRole.objects.filter(
            user=user,
            event=self,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER,
            is_active=True
        ).exists()

    def get_photographers(self):
        """Get all photographers for this event"""
        return EventUserRole.objects.filter(
            event=self,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER,
            is_active=True
        )

    def remove_photographer(self, user):
        """Remove photographer role from user for this event"""
        try:
            photographer_role = EventUserRole.objects.get(
                user=user,
                event=self,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER
            )
            photographer_role.delete()
            return True
        except EventUserRole.DoesNotExist:
            return False

    def can_user_upload_photos(self, user):
        """Check if user can upload photos to this event"""
        try:
            user_role = EventUserRole.objects.get(
                user=user,
                event=self,
                is_active=True
            )
            return user_role.can_upload_photos
        except EventUserRole.DoesNotExist:
            return False

    def validate_photo_price(self, price, photographer=None):
        """Validate photo price based on event pricing mode"""
        if self.pricing_mode == self.PricingMode.FIXED:
            return self.fixed_photo_price  # Ignore photographer input
        elif self.pricing_mode == self.PricingMode.CAPPED:
            if price is None or price > self.price_cap:
                return self.price_cap
            return price
        elif self.pricing_mode == self.PricingMode.FREE:
            return price if price is not None else 0.00

        return 0.00


class EventAttendance(models.Model):
    """Model to track user attendance at events"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='attendances')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='attended_events')
    joined_at = models.DateTimeField(auto_now_add=True)
    is_attending = models.BooleanField(default=True)  # To handle cancellations
    attendance_method = models.CharField(max_length=50, choices=[
        ('QR_CODE', 'QR Code'),
        ('INVITE', 'Invitation'),
        ('SELF_JOIN', 'Self Join'),
    ], default='SELF_JOIN')
    jersey_number = models.CharField(max_length=20, blank=True, null=True)  # For sports events
    
    class Meta:
        db_table = 'event_attendance'
        unique_together = ('event', 'user')
    
    def __str__(self):
        return f"{self.user.email} at {self.event.name}"


class EventPhoto(models.Model):
    """Model for photos associated with events (moved from photos app)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='photos')
    photographer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='uploaded_photos')
    image = models.ImageField(upload_to='event_photos/')
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    detected_faces = models.JSONField(null=True, blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed_for_faces = models.BooleanField(default=False)  # Track if we've processed this for faces

    # Photographer pricing fields
    photographer_price = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Price set by photographer")
    final_price = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Final price based on event pricing mode")

    # Photo metadata
    photo_title = models.CharField(max_length=200, blank=True, help_text="Photo title")
    photo_description = models.TextField(blank=True, help_text="Photo description")
    is_featured = models.BooleanField(default=False, help_text="Featured photo for event")

    class Meta:
        db_table = 'event_photo'

    def __str__(self):
        return f"Photo {self.id} from {self.event.name}"

    def calculate_final_price(self):
        """Calculate final price based on event pricing mode and photographer price"""
        if self.event.pricing_mode == Event.PricingMode.FIXED:
            # Organizer sets fixed price, photographer cannot change
            self.final_price = self.event.fixed_photo_price
        elif self.event.pricing_mode == Event.PricingMode.CAPPED:
            # Photographer can set price up to cap
            if self.photographer_price and self.photographer_price <= self.event.price_cap:
                self.final_price = self.photographer_price
            else:
                self.final_price = self.event.price_cap
        elif self.event.pricing_mode == Event.PricingMode.FREE:
            # Photographer has complete pricing freedom
            self.final_price = self.photographer_price if self.photographer_price else 0.00
        else:
            self.final_price = 0.00

        return self.final_price

    def save(self, *args, **kwargs):
        """Override save to calculate final price automatically"""
        self.calculate_final_price()
        super().save(*args, **kwargs)
    

class EventUserRole(models.Model):
    """Event-specific role assignment - Now includes ORGANIZER role"""

    class RoleChoices(models.TextChoices):
        ATTENDEE = 'ATTENDEE', 'Event Attendee'
        ORGANIZER = 'ORGANIZER', 'Event Organizer'
        PHOTOGRAPHER = 'PHOTOGRAPHER', 'Event Photographer'  # ADD THIS LINE
        # Future: ADMIN

    class AssignmentMethod(models.TextChoices):
        USER_APP = 'USER_APP', 'User App Join'
        QR_CODE = 'QR_CODE', 'QR Code Scan'
        INVITE = 'INVITE', 'Invitation Link'
        SELF_JOIN = 'SELF_JOIN', 'Self Join'
        CREATOR = 'CREATOR', 'Event Creator'  # ADDED CREATOR METHOD

    # Core fields - Use BigAutoField to match User model (which uses bigint in database)
    # Note: Even though Event uses UUID, EventUserRole connects User (bigint) to Event (UUID)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='event_roles'
    )
    event = models.ForeignKey(
        'Event',
        on_delete=models.CASCADE,
        related_name='user_roles'
    )
    role = models.CharField(
        max_length=15,
        choices=RoleChoices.choices,
        default=RoleChoices.ATTENDEE
    )
    assigned_via = models.CharField(
        max_length=20,
        choices=AssignmentMethod.choices,
        default=AssignmentMethod.USER_APP
    )


    # Additional photographer fields
    portfolio_link = models.URLField(blank=True, help_text="Photographer's portfolio link")
    specialization = models.CharField(max_length=100, blank=True, help_text="Photography specialization")
    approved_by_creator = models.BooleanField(default=True, help_text="Whether photographer is approved by event creator")
    
    # Photo management permissions
    can_upload_photos = models.BooleanField(default=False)
    can_set_photo_prices = models.BooleanField(default=False)
    can_delete_own_photos = models.BooleanField(default=False)
    can_edit_own_photos = models.BooleanField(default=False)
    can_view_earnings = models.BooleanField(default=False)

    # Event participation details
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    jersey_number = models.CharField(max_length=20, blank=True, null=True)
    
    # Basic permissions (existing)
    can_view_tagged_photos = models.BooleanField(default=True)
    can_purchase_photos = models.BooleanField(default=True)
    
    # Enhanced organizer permissions
    can_manage_event = models.BooleanField(default=False)
    can_view_analytics = models.BooleanField(default=False)
    can_assign_roles = models.BooleanField(default=False)
    can_download_hd_free = models.BooleanField(default=False)  # Free HD access for organizers
    can_view_all_photos = models.BooleanField(default=False)   # See all event photos
    can_remove_content = models.BooleanField(default=False)    # Remove images/users
    can_invite_photographers = models.BooleanField(default=False)
    can_set_filters = models.BooleanField(default=False)       # AI filter controls

    # Photographer-specific permissions
    can_upload_photos = models.BooleanField(default=False)
    can_set_photo_prices = models.BooleanField(default=False)
    can_view_earnings = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['user', 'event', 'role']
        indexes = [
            models.Index(fields=['event', 'role']),
            models.Index(fields=['user', 'role']),
            models.Index(fields=['user', 'event']),
        ]
        db_table = 'event_user_role'
    
    def __str__(self):
        return f"{self.user.username} - {self.role} in {self.event.name}"
    
    def save(self, *args, **kwargs):
        # Set default permissions for photographers
        if self.role == self.RoleChoices.PHOTOGRAPHER:
            self.can_upload_photos = True
            self.can_set_photo_prices = True
            self.can_delete_own_photos = True
            self.can_edit_own_photos = True
            self.can_view_earnings = True
        
        super().save(*args, **kwargs)


class PhotographerProfile(models.Model):
    """Basic photographer profile and portfolio"""
    # Use default BigAutoField to match User model (which uses bigint in database)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='photographer_profile')

    # Basic Professional Info
    bio = models.TextField(max_length=500, blank=True, help_text="Professional bio")
    specializations = models.JSONField(default=list, help_text="Photography specializations like ['wedding', 'sports', 'portrait']")
    equipment = models.TextField(blank=True, help_text="Camera equipment description")
    hourly_rate = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Hourly rate in USD")

    # Simple Portfolio
    portfolio_images = models.JSONField(default=list, help_text="List of portfolio image URLs")

    # Verification Status
    is_verified = models.BooleanField(default=False, help_text="Whether photographer is verified")
    verification_notes = models.TextField(blank=True, help_text="Admin notes about verification")

    # Basic Statistics
    total_events = models.IntegerField(default=0, help_text="Total events photographed")
    total_photos_uploaded = models.IntegerField(default=0, help_text="Total photos uploaded")
    total_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, help_text="Total earnings")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'photographer_profile'

    def __str__(self):
        return f"Photographer Profile: {self.user.email}"

    def update_stats(self):
        """Update photographer statistics"""
        # Count events where user is photographer
        self.total_events = EventUserRole.objects.filter(
            user=self.user,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER,
            is_active=True
        ).count()

        # Count total photos uploaded
        self.total_photos_uploaded = EventPhoto.objects.filter(
            photographer=self.user
        ).count()

        self.save()

    def add_portfolio_image(self, image_url):
        """Add image to portfolio"""
        if image_url not in self.portfolio_images:
            self.portfolio_images.append(image_url)
            self.save()

    def remove_portfolio_image(self, image_url):
        """Remove image from portfolio"""
        if image_url in self.portfolio_images:
            self.portfolio_images.remove(image_url)
            self.save()


class PhotographerEventApplication(models.Model):
    """Photographer applications to events"""
    # Use default BigAutoField to match User model (which uses bigint in database)
    photographer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='event_applications')
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='photographer_applications')

    # Application Details
    message = models.TextField(help_text="Why photographer wants to photograph this event")
    equipment_list = models.TextField(blank=True, help_text="Equipment they'll bring to event")
    proposed_rate = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Proposed hourly rate")

    # Application Status
    class ApplicationStatus(models.TextChoices):
        PENDING = 'PENDING', 'Pending Review'
        APPROVED = 'APPROVED', 'Approved'
        REJECTED = 'REJECTED', 'Rejected'
        WITHDRAWN = 'WITHDRAWN', 'Withdrawn by Photographer'

    status = models.CharField(max_length=20, choices=ApplicationStatus.choices, default=ApplicationStatus.PENDING)

    # Timestamps
    applied_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    reviewed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_applications')

    # Notes
    review_notes = models.TextField(blank=True, help_text="Organizer's review notes")

    class Meta:
        db_table = 'photographer_event_application'
        unique_together = ['photographer', 'event']  # One application per photographer per event
        indexes = [
            models.Index(fields=['event', 'status']),
            models.Index(fields=['photographer', 'status']),
        ]

    def __str__(self):
        return f"{self.photographer.email} -> {self.event.name} ({self.status})"

    def approve(self, approved_by):
        """Approve application and add photographer to event"""
        self.status = self.ApplicationStatus.APPROVED
        self.reviewed_by = approved_by
        self.reviewed_at = timezone.now()
        self.save()

        # Add photographer to event
        self.event.add_photographer(self.photographer, assigned_via='APPLICATION_APPROVED')
        return True

    def reject(self, rejected_by, reason=""):
        """Reject application"""
        self.status = self.ApplicationStatus.REJECTED
        self.reviewed_by = rejected_by
        self.reviewed_at = timezone.now()
        self.review_notes = reason
        self.save()
        return True

    def withdraw(self):
        """Photographer withdraws application"""
        if self.status == self.ApplicationStatus.PENDING:
            self.status = self.ApplicationStatus.WITHDRAWN
            self.save()
            return True
        return False


class PhotographerEarnings(models.Model):
    """Track photographer earnings per photo sale"""
    # Use default BigAutoField to match User model (which uses bigint in database)
    photographer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='earnings')
    event = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='photographer_earnings')
    photo = models.ForeignKey(EventPhoto, on_delete=models.CASCADE, related_name='earnings')

    # Financial Details
    photo_price = models.DecimalField(max_digits=8, decimal_places=2, help_text="Final price of photo")
    platform_commission = models.DecimalField(max_digits=8, decimal_places=2, help_text="Platform's commission")
    photographer_share = models.DecimalField(max_digits=8, decimal_places=2, help_text="Photographer's earnings")
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2, default=30.00, help_text="Platform commission rate %")

    # Purchase Info
    purchased_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='photo_purchases')
    transaction_date = models.DateTimeField(auto_now_add=True)

    # Payout Status
    payout_status = models.CharField(max_length=20, choices=[
        ('PENDING', 'Pending Payout'),
        ('PROCESSED', 'Payout Processed'),
        ('FAILED', 'Payout Failed')
    ], default='PENDING')

    payout_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'photographer_earnings'
        indexes = [
            models.Index(fields=['photographer', 'transaction_date']),
            models.Index(fields=['event', 'transaction_date']),
            models.Index(fields=['payout_status']),
        ]

    def __str__(self):
        return f"{self.photographer.email} earned ${self.photographer_share} from {self.photo.id}"

    @classmethod
    def create_earning(cls, photo, purchased_by, commission_rate=30.0):
        """Create earning record when photo is purchased"""
        photo_price = photo.final_price or 0.00
        platform_commission = (photo_price * commission_rate) / 100
        photographer_share = photo_price - platform_commission

        earning = cls.objects.create(
            photographer=photo.photographer,
            event=photo.event,
            photo=photo,
            photo_price=photo_price,
            platform_commission=platform_commission,
            photographer_share=photographer_share,
            commission_rate=commission_rate,
            purchased_by=purchased_by
        )

        # Update photographer profile stats
        if hasattr(photo.photographer, 'photographer_profile'):
            profile = photo.photographer.photographer_profile
            profile.total_earnings += photographer_share
            profile.save()

        return earning
    
class Notification(models.Model):
    """Simple notifications for photographers"""
    
    NOTIFICATION_TYPES = [
        ('payment_received', 'Payment Received'),
        ('payment_pending', 'Payment Pending'), 
        ('payment_disbursed', 'Payment Disbursed'),
        ('event_accepted', 'Event Accepted'),
        ('event_invitation', 'Event Invitation'),
        ('photo_processed', 'Photo Processed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    
    # Notification Content
    notification_type = models.CharField(max_length=50, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=255)
    message = models.TextField()
    
    # Status
    is_read = models.BooleanField(default=False)
    
    # Optional Data
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    event_name = models.CharField(max_length=255, null=True, blank=True)
    event = models.ForeignKey(Event, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'photographer_notifications'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.title} - {self.user.username}"

class PhotoSale(models.Model):
    """Track photo sales for revenue calculations"""
    
    PAYMENT_STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('REFUNDED', 'Refunded'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Foreign Keys (using your existing models)
    photo = models.ForeignKey(EventPhoto, on_delete=models.CASCADE, related_name='sales')
    photographer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='photo_sales')
    buyer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='purchases')
    event = models.ForeignKey(Event, on_delete=models.SET_NULL, null=True, related_name='photo_sales')
    
    # Sale Details
    sale_amount = models.DecimalField(max_digits=10, decimal_places=2)
    photographer_commission = models.DecimalField(max_digits=10, decimal_places=2)
    platform_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Status
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='PENDING')
    is_paid_to_photographer = models.BooleanField(default=False)
    
    # Timestamps
    sold_at = models.DateTimeField(auto_now_add=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'photo_sales_simple'
        ordering = ['-sold_at']
        
    def __str__(self):
        photographer_name = self.photographer.username if self.photographer else 'Unknown'
        return f"Sale ${self.sale_amount} - {photographer_name}"