from rest_framework import permissions


class IsEventOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an event to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the event creator
        return obj.creator == request.user


class IsEventCreatorOrPhotographer(permissions.BasePermission):
    """
    Custom permission to allow only event creator or assigned photographers
    to perform actions like uploading photos.
    """
    
    def has_object_permission(self, request, view, obj):
        # Check if user is the creator
        if obj.creator == request.user:
            return True
            
        # Check if user is an assigned photographer
        return obj.photographers.filter(id=request.user.id).exists()
   
    
class CanJoinLeaveEvent(permissions.BasePermission):
    """Allow any authenticated user to join or leave an event."""
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
        
    def has_object_permission(self, request, view, obj):
        # For join/leave actions, any authenticated user can perform
        if view.action in ['join_event', 'leave_event']:
            return True
        # For other actions, defer to the default permission checks
        return False    
    
class CanManageOwnPhotos(permissions.BasePermission):
    """
    Allow photographers to manage only their own uploaded photos
    """
    
    def has_object_permission(self, request, view, obj):
        # Event creators can manage all photos
        if obj.event.creator == request.user:
            return True
        
        # Photographers can only manage their own photos
        if hasattr(obj, 'uploaded_by') and obj.uploaded_by == request.user:
            # Check if user is still a photographer for this event
            return obj.event.is_user_photographer(request.user)
        
        return False


