import logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from .models import EventPhoto, Event

logger = logging.getLogger('events')


@receiver(post_save, sender=EventPhoto)
def update_event_photo_count_on_create(sender, instance, created, **kwargs):
    """When a photo is created or updated, update the event's photo count"""
    if created:
        try:
            event = instance.event
            # Update asynchronously to avoid performance impact
            Event.objects.filter(id=event.id).update(
                total_photos=Event.objects.filter(id=event.id).values('total_photos')[0]['total_photos'] + 1
            )
            logger.info(f"Photo {instance.id} created for event {event.id}, updated photo count")
        except Exception as e:
            logger.error(f"Error updating photo count: {str(e)}")


@receiver(post_delete, sender=EventPhoto)
def update_event_photo_count_on_delete(sender, instance, **kwargs):
    """When a photo is deleted, update the event's photo count"""
    try:
        event = instance.event
        # Update the total photo count
        Event.objects.filter(id=event.id).update(
            total_photos=Event.objects.filter(id=event.id).values('total_photos')[0]['total_photos'] - 1
        )
        logger.info(f"Photo {instance.id} deleted from event {event.id}, updated photo count")
        
        # Recalculate tagged photos count
        # This is a more expensive operation, but necessary to keep counts accurate
        event.update_photo_counts()
    except Exception as e:
        logger.error(f"Error updating photo count on delete: {str(e)}")