from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import EventViewSet

from .photographer_views import (
    photographer_dashboard_stats,
    quick_upload_check,
    photographer_notifications,
    recent_events,
    nearby_events,
    complete_dashboard
)

router = DefaultRouter()
router.register(r'', EventViewSet, basename='event')

urlpatterns = [
    path('', include(router.urls)),
    
    # Home screen endpoints
    path('home/events/', EventViewSet.as_view({'get': 'home_events'}), name='home-events'),
    path('home/nearby/', EventViewSet.as_view({'post': 'nearby'}), name='nearby-events'),
    path('search/', EventViewSet.as_view({'post': 'search'}), name='search-events'),
    
    # Event list endpoints
    path('attended/', EventViewSet.as_view({'get': 'attended_events'}), name='attended-events'),
    path('created/', EventViewSet.as_view({'get': 'created_events'}), name='created-events'),
    
    # Event interaction endpoints
    path('<uuid:pk>/join/', EventViewSet.as_view({'post': 'join_event'}), name='join-event'),
    path('<uuid:pk>/leave/', EventViewSet.as_view({'post': 'leave_event'}), name='leave-event'),
    path('<uuid:pk>/photographers/', EventViewSet.as_view({'post': 'add_photographer', 'delete': 'remove_photographer'}), name='event-photographers'),
    path('<uuid:pk>/photos/', EventViewSet.as_view({'get': 'photos'}), name='event-photos'),
    path('<uuid:pk>/details/', EventViewSet.as_view({'get': 'event_details'}), name='event-details'),
    path('<uuid:pk>/update-price/', EventViewSet.as_view({'post': 'update_price'}), name='update-price'),
    path('<uuid:pk>/toggle-visibility/', EventViewSet.as_view({'post': 'toggle_visibility'}), name='toggle-visibility'),
    
    # Photo upload endpoints (previously in photos app)
    path('<uuid:pk>/upload-photo/', EventViewSet.as_view({'post': 'upload_photo'}), name='upload-photo'),
    path('<uuid:pk>/batch-upload-photos/', EventViewSet.as_view({'post': 'batch_upload_photos'}), name='batch-upload-photos'),

    # Photographer Dashboard APIs
    path('photographer/dashboard/', complete_dashboard, name='photographer-dashboard'),
    path('photographer/dashboard/stats/', photographer_dashboard_stats, name='photographer-stats'),
    path('photographer/quick-upload/check/', quick_upload_check, name='quick-upload-check'),
    path('photographer/notifications/', photographer_notifications, name='photographer-notifications'),
    path('photographer/recent-events/', recent_events, name='photographer-recent-events'),
    path('photographer/nearby-events/', nearby_events, name='photographer-nearby-events'),

    path('queue-status/', EventViewSet.as_view({'get': 'global_queue_status'}), name='global-queue-status'),
    path('<uuid:pk>/face-processing-status/', EventViewSet.as_view({'get': 'face_processing_status'}), name='event-face-processing-status'),
    path('<uuid:pk>/join-processing-status/', EventViewSet.as_view({'get': 'join_processing_status'}), name='join-processing-status'),

    # Photographer-specific events endpoint
    path('photographer-events/', EventViewSet.as_view({'get': 'photographer_events'}), name='photographer-events'),
]