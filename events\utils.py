import logging
from io import Bytes<PERSON>
from PIL import Image, ImageOps
import uuid
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from rest_framework import status
from rest_framework.response import Response


logger = logging.getLogger('events')

def compress_image(image_file, quality=85, max_size=(1200, 1200)):
    """
    Compress an image to reduce storage and processing costs
    
    Args:
        image_file: Django InMemoryUploadedFile or similar
        quality: JPEG quality (1-100)
        max_size: Maximum dimensions (width, height)
        
    Returns:
        BytesIO: Compressed image data
    """
    try:
        # Open the image
        img = Image.open(image_file)
        
        # Auto-orient the image according to EXIF data
        img = ImageOps.exif_transpose(img)
        
        # Resize if larger than max_size while maintaining aspect ratio
        if img.width > max_size[0] or img.height > max_size[1]:
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # Save with compression
        output = BytesIO()
        
        # Preserve format if possible or default to JPEG
        format = 'JPEG'
        if hasattr(image_file, 'content_type'):
            if 'png' in image_file.content_type.lower():
                format = 'PNG'
                
        # Convert RGBA to RGB mode if saving as JPEG
        if format == 'JPEG' and img.mode == 'RGBA':
            img = img.convert('RGB')
            
        img.save(output, format=format, quality=quality, optimize=True)
        output.seek(0)
        
        logger.debug(f"Compressed image from {getattr(image_file, 'size', 'unknown')} bytes")
        return output
        
    except Exception as e:
        logger.error(f"Error compressing image: {str(e)}")
        # Return the original if compression fails
        image_file.seek(0)
        return image_file


def process_event_photos(photo_id, priority_users=None):
    """
    Process photos for an event with batching priority
    
    Args:
        photo_id: ID of the photo to process
        priority_users: Optional list of user IDs to prioritize in matching
    """
    try:
        # Import here to avoid circular imports
        from .models import EventPhoto
        from facial_recognition.services import FacialRecognitionService
        
        # Get the photo
        photo = EventPhoto.objects.get(id=photo_id)
        
        # If priority users are not provided, get users who attended the event
        if priority_users is None:
            from .models import EventAttendance
            priority_users = EventAttendance.objects.filter(
                event=photo.event,
                is_attending=True
            ).values_list('user_id', flat=True)
        
        # First try to match with priority users (if we had a mechanism to filter by user)
        # For now, we'll just call the general match_faces_in_photo method
        success, message, matches = FacialRecognitionService.match_faces_in_photo(photo_id)
        
        if not success:
            logger.warning(f"Face matching for photo {photo_id} failed: {message}")
        else:
            logger.info(f"Found {len(matches)} face matches in photo {photo_id}")
            
        # Mark the photo as processed
        photo.processed_for_faces = True
        photo.save(update_fields=['processed_for_faces'])
        
        return success, message, matches
        
    except Exception as e:
        logger.error(f"Error processing photo {photo_id} for faces: {str(e)}")
        return False, f"Error processing: {str(e)}", []


def batch_process_event_photos(event_id):
    """
    Process all unprocessed photos for an event
    
    Args:
        event_id: ID of the event to process
    """
    try:
        # Import here to avoid circular imports
        from .models import Event, EventPhoto, EventAttendance
        
        event = Event.objects.get(id=event_id)
        
        # Get photos that haven't been processed yet
        unprocessed_photos = EventPhoto.objects.filter(
            event=event,
            processed_for_faces=False
        )
        
        if not unprocessed_photos.exists():
            logger.info(f"No unprocessed photos found for event {event_id}")
            return True, "No unprocessed photos found", []
        
        # Get list of users who attended the event
        attended_users = EventAttendance.objects.filter(
            event=event,
            is_attending=True
        ).values_list('user_id', flat=True)
        
        processed = 0
        failed = 0
        
        # Process each photo
        for photo in unprocessed_photos:
            success, _, _ = process_event_photos(str(photo.id), attended_users)
            if success:
                processed += 1
            else:
                failed += 1
        
        logger.info(f"Batch processed {processed} photos for event {event_id} ({failed} failed)")
        return True, f"Processed {processed} photos ({failed} failed)", []
        
    except Exception as e:
        logger.error(f"Error batch processing photos for event {event_id}: {str(e)}")
        return False, f"Error: {str(e)}", []
    

def validate_upload_timing(event):
    """
    Validate if photo upload is allowed based on event timing.
    
    Rules:
    - No uploads before event starts
    - Uploads allowed during event
    - Uploads allowed for 2 days after event ends
    - No uploads after 2-day buffer period
    
    Returns:
        tuple: (is_valid, error_response)
    """
    now = timezone.now().date()
    
    # If event doesn't have start_date, allow uploads (backward compatibility)
    if not event.start_date:
        return True, None
    
    # Check if event hasn't started yet
    if now < event.start_date:
        return False, Response({
            'error': 'Photo upload not allowed',
            'detail': f'Event "{event.name}" has not started yet. Upload will be available from {event.start_date}.',
            'event_start_date': event.start_date.isoformat(),
            'current_date': now.isoformat(),
            'upload_status': 'NOT_STARTED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # If event has end_date, check buffer period
    if event.end_date:
        # Calculate buffer end date (2 days after event ends)
        buffer_end_date = event.end_date + timedelta(days=2)
        
        # Check if we're past the buffer period
        if now > buffer_end_date:
            return False, Response({
                'error': 'Photo upload period has ended',
                'detail': f'Event "{event.name}" ended on {event.end_date}. Upload deadline was {buffer_end_date}.',
                'event_end_date': event.end_date.isoformat(),
                'upload_deadline': buffer_end_date.isoformat(),
                'current_date': now.isoformat(),
                'upload_status': 'DEADLINE_PASSED'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Check if we're in the buffer period (after event ended)
        if now > event.end_date:
            days_remaining = (buffer_end_date - now).days
            return True, {
                'warning': 'Upload deadline approaching',
                'detail': f'Event has ended. You have {days_remaining} day(s) remaining to upload photos.',
                'upload_deadline': buffer_end_date.isoformat(),
                'upload_status': 'BUFFER_PERIOD'
            }
    
    # Event is ongoing or hasn't ended yet
    return True, {
        'upload_status': 'ACTIVE',
        'detail': 'Photo upload is allowed'
    }


def get_upload_status_info(event):
    """
    Get detailed upload status information for an event.
    
    Returns:
        dict: Upload status information
    """
    now = timezone.now().date()
    
    if not event.start_date:
        return {
            'upload_allowed': True,
            'status': 'ALWAYS_ALLOWED',
            'message': 'No date restrictions set for this event'
        }
    
    # Event hasn't started
    if now < event.start_date:
        days_until_start = (event.start_date - now).days
        return {
            'upload_allowed': False,
            'status': 'NOT_STARTED',
            'message': f'Upload will be available in {days_until_start} day(s)',
            'event_start_date': event.start_date.isoformat(),
            'days_until_start': days_until_start
        }
    
    # Event is ongoing (started but not ended, or no end date)
    if not event.end_date or now <= event.end_date:
        return {
            'upload_allowed': True,
            'status': 'ACTIVE',
            'message': 'Event is active - uploads allowed',
            'event_start_date': event.start_date.isoformat(),
            'event_end_date': event.end_date.isoformat() if event.end_date else None
        }
    
    # Event has ended - check buffer period
    buffer_end_date = event.end_date + timedelta(days=2)
    
    # Still in buffer period
    if now <= buffer_end_date:
        days_remaining = (buffer_end_date - now).days
        return {
            'upload_allowed': True,
            'status': 'BUFFER_PERIOD',
            'message': f'Event ended - {days_remaining} day(s) remaining to upload',
            'event_end_date': event.end_date.isoformat(),
            'upload_deadline': buffer_end_date.isoformat(),
            'days_remaining': days_remaining
        }
    
    # Buffer period has ended
    return {
        'upload_allowed': False,
        'status': 'DEADLINE_PASSED',
        'message': 'Upload deadline has passed',
        'event_end_date': event.end_date.isoformat(),
        'upload_deadline': buffer_end_date.isoformat()
    }