from django.contrib import admin
from .models import FacialProfile, FaceScanSession, FaceMatchResult


@admin.register(FacialProfile)
class FacialProfileAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'face_id', 'confidence_score', 'is_verified', 'created_at', 'updated_at')
    list_filter = ('is_verified', 'created_at')
    search_fields = ('user__email', 'user__username', 'face_id')
    readonly_fields = ('face_id', 'created_at', 'updated_at')
    
    def has_add_permission(self, request):
        # Only allow facial profiles to be created via the API
        return False



@admin.register(FaceScanSession)
class FaceScanSessionAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'status', 'created_at', 'completed_at')
    list_filter = ('status', 'created_at')
    search_fields = ('user__email', 'user__username')
    readonly_fields = ('created_at', 'completed_at')
    
    def has_add_permission(self, request):
        # Only allow scan sessions to be created via the API
        return False


@admin.register(FaceMatchResult)
class FaceMatchResultAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'event_photo', 'confidence_score', 'similarity_score', 
                   'is_verified', 'is_rejected', 'created_at')
    list_filter = ('is_verified', 'is_rejected', 'created_at')
    search_fields = ('user__email', 'user__username')
    readonly_fields = ('confidence_score', 'similarity_score', 'created_at', 'updated_at')
    
    def has_add_permission(self, request):
        # Only allow matches to be created via the API
        return False