# Generated by Django 5.1.5 on 2025-06-30 01:18

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('events', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FaceScanSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('STARTED', 'Started'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='STARTED', max_length=20)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('session_data', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Face Scan Session',
                'verbose_name_plural': 'Face Scan Sessions',
                'db_table': 'face_scan_session',
            },
        ),
        migrations.CreateModel(
            name='FacialProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('face_id', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('facial_features', models.JSONField(blank=True, null=True)),
                ('face_image', models.ImageField(blank=True, null=True, upload_to='facial_profiles/')),
                ('confidence_score', models.FloatField(default=0.0)),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Facial Profile',
                'verbose_name_plural': 'Facial Profiles',
                'db_table': 'facial_profile',
            },
        ),
        migrations.CreateModel(
            name='FaceMatchResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('confidence_score', models.FloatField()),
                ('bounding_box', models.JSONField(blank=True, null=True)),
                ('similarity_score', models.FloatField()),
                ('is_verified', models.BooleanField(default=False)),
                ('is_rejected', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('event_photo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='face_matches', to='events.eventphoto')),
            ],
            options={
                'verbose_name': 'Face Match Result',
                'verbose_name_plural': 'Face Match Results',
                'db_table': 'face_match_result',
            },
        ),
    ]
