# Generated by Django 5.1.5 on 2025-06-30 01:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('facial_recognition', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='facematchresult',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='face_matches', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='facescansession',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='face_scan_sessions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='facialprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='facial_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='facematchresult',
            unique_together={('user', 'event_photo')},
        ),
    ]
