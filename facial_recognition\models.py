from django.db import models
import uuid
from django.conf import settings


class FacialProfile(models.Model):
    """
    Model to store facial recognition data for a user
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='facial_profile')
    face_id = models.CharField(max_length=255, unique=True, null=True, blank=True)  # AWS Rekognition Face ID
    facial_features = models.JSONField(null=True, blank=True)  # Store facial features detected
    face_image = models.ImageField(upload_to='facial_profiles/', null=True, blank=True)  # Original image used for face scan
    confidence_score = models.FloatField(default=0.0)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'facial_profile'
        verbose_name = 'Facial Profile'
        verbose_name_plural = 'Facial Profiles'

    def __str__(self):
        return f"Facial Profile for {self.user.email}"


class FaceScanSession(models.Model):
    """
    Model to track face scanning sessions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='face_scan_sessions')
    status = models.CharField(max_length=20, choices=[
        ('STARTED', 'Started'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
    ], default='STARTED')
    error_message = models.TextField(null=True, blank=True)
    session_data = models.JSONField(null=True, blank=True)  # Additional session data
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'face_scan_session'
        verbose_name = 'Face Scan Session'
        verbose_name_plural = 'Face Scan Sessions'

    def __str__(self):
        return f"Scan Session for {self.user.email} ({self.status})"


class FaceMatchResult(models.Model):
    """
    Model to store face matching results between users and photos
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='face_matches')
    event_photo = models.ForeignKey('events.EventPhoto', on_delete=models.CASCADE, related_name='face_matches')
    confidence_score = models.FloatField()
    bounding_box = models.JSONField(null=True, blank=True)  # Coordinates of the face in the photo
    similarity_score = models.FloatField()
    is_verified = models.BooleanField(default=False)  # User manually verified this match
    is_rejected = models.BooleanField(default=False)  # User rejected this match
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'face_match_result'
        unique_together = ('user', 'event_photo')
        verbose_name = 'Face Match Result'
        verbose_name_plural = 'Face Match Results'

    def __str__(self):
        return f"Match: {self.user.email} in photo {self.event_photo.id} ({self.confidence_score})"
