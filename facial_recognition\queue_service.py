# facial_recognition/queue_service.py
"""
PhotoFish Queue Service - Integration Layer
Provides a clean interface between PhotoFish views and the enterprise queue system
"""

import logging
import uuid
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)

class PhotoFishQueueService:
    """
    Service layer for PhotoFish facial recognition queue operations
    Provides a clean, simple interface to the enterprise queue system
    """
    
    def __init__(self):
        self.queue_manager = None
        self._initialize_queue_manager()
    
    def _initialize_queue_manager(self):
        """Initialize the queue manager"""
        try:
            if getattr(settings, 'ENABLE_ENHANCED_QUEUE', False):
                from queue_system.queue_engine.queue_manager import QueueManager
                self.queue_manager = QueueManager()
                if not self.queue_manager.is_running:
                    self.queue_manager.start()
            else:
                logger.warning("Enhanced queue system is disabled")
        except Exception as e:
            logger.error(f"Failed to initialize queue manager: {str(e)}")
            self.queue_manager = None
    
    def add_photos_for_face_processing(self, photo_ids: List[str], priority: str = "STANDARD", 
                                     metadata: Optional[Dict] = None) -> Tuple[bool, Dict]:
        """
        Add photos to face processing queue
        
        Args:
            photo_ids: List of photo IDs to process
            priority: Processing priority (HIGH, STANDARD, LOW)
            metadata: Additional processing context
            
        Returns:
            (success: bool, status_info: dict)
        """
        try:
            if not self.queue_manager:
                return self._fallback_processing(photo_ids, metadata)
            
            # Prepare job data - ensure all values are JSON serializable
            job_data = {
                'photo_ids': photo_ids,
                'processing_type': 'facial_recognition',
                'event_id': str(metadata.get('event_id')) if metadata and metadata.get('event_id') else None,
                'user_id': str(metadata.get('user_id')) if metadata and metadata.get('user_id') else None,
                'batch_type': metadata.get('batch_type', 'upload') if metadata else 'upload'
            }
            
            # Submit job to enterprise queue
            job_id = self.queue_manager.submit_job(
                job_type='FACIAL_RECOGNITION',
                priority=priority,
                data=job_data,
                options={'timeout_seconds': 1800}  # 30 minutes max
            )
            
            if job_id:
                # Get queue status
                status_info = self.get_queue_status()
                status_info.update({
                    'job_id': job_id,
                    'photos_queued': len(photo_ids),
                    'priority': priority,
                    'estimated_processing_time': f"{len(photo_ids) * 2} seconds"
                })
                
                logger.info(f"Queued {len(photo_ids)} photos for face processing (Job: {job_id})")
                return True, status_info
            else:
                return False, {'error': 'Failed to submit job to queue'}
                
        except Exception as e:
            logger.error(f"Error adding photos to queue: {str(e)}")
            return False, {'error': str(e)}
    
    def get_queue_status(self) -> Dict:
        """Get current queue status"""
        try:
            if not self.queue_manager:
                return {
                    'queue_size': 0,
                    'is_processing': False,
                    'worker_count': 0,
                    'status': 'queue_unavailable'
                }
            
            status = self.queue_manager.get_status()
            
            return {
                'queue_size': status.get('total_jobs', 0),
                'pending_jobs': status.get('jobs_by_status', {}).get('PENDING', 0),
                'processing_jobs': status.get('jobs_by_status', {}).get('PROCESSING', 0),
                'is_processing': status.get('jobs_by_status', {}).get('PROCESSING', 0) > 0,
                'worker_count': status.get('active_workers', 0),
                'status': 'operational'
            }
            
        except Exception as e:
            logger.error(f"Error getting queue status: {str(e)}")
            return {
                'queue_size': 0,
                'is_processing': False,
                'worker_count': 0,
                'status': 'error',
                'error': str(e)
            }
    
    def get_job_status(self, job_id: str) -> Dict:
        """Get status of a specific job"""
        try:
            if not self.queue_manager:
                return {'status': 'unknown', 'error': 'Queue manager not available'}
            
            # This would use the queue manager's job tracking
            # Implementation depends on the actual queue manager interface
            return {'status': 'not_implemented'}
            
        except Exception as e:
            logger.error(f"Error getting job status: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def _fallback_processing(self, photo_ids: List[str], metadata: Optional[Dict] = None) -> Tuple[bool, Dict]:
        """
        Fallback to direct processing when queue is unavailable
        This ensures photos still get processed even if queue system fails
        """
        try:
            logger.warning("⚠️ Queue system unavailable, falling back to direct processing")
            
            # Import here to avoid circular imports
            from events.models import EventPhoto
            from facial_recognition.services import RekognitionService
            
            processed_count = 0
            failed_count = 0
            
            for photo_id in photo_ids:
                try:
                    # Process photo directly
                    photo = EventPhoto.objects.get(id=photo_id)
                    
                    # Skip if already processed
                    if photo.processed_for_faces:
                        continue
                    
                    # Process facial recognition
                    success = self._process_single_photo_direct(photo)
                    if success:
                        processed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"Error in fallback processing for photo {photo_id}: {str(e)}")
                    failed_count += 1
            
            return True, {
                'processed_count': processed_count,
                'failed_count': failed_count,
                'status': 'fallback_processing_complete',
                'queue_size': 0,
                'is_processing': False
            }
            
        except Exception as e:
            logger.error(f"Error in fallback processing: {str(e)}")
            return False, {'error': str(e)}
    
    def _process_single_photo_direct(self, photo) -> bool:
        """Direct photo processing (fallback)"""
        try:
            from facial_recognition.services import RekognitionService
            from facial_recognition.models import FacialProfile, FaceMatchResult
            from events.models import EventAttendance
            
            # Get photo image bytes
            photo_file = photo.image.open('rb')
            image_bytes = photo_file.read()
            photo_file.close()
            
            # Detect faces
            success, face_details, error = RekognitionService.detect_faces(image_bytes)
            
            if success and face_details:
                # Store face data
                photo.detected_faces = {
                    'faces': [face_details],
                    'face_count': 1,
                    'processed_at': datetime.now().isoformat()
                }
                
                # Match against attending users
                attending_profiles = FacialProfile.objects.filter(
                    user__in=EventAttendance.objects.filter(
                        event=photo.event, 
                        is_attending=True
                    ).values_list('user', flat=True),
                    is_verified=True,
                    face_id__isnull=False
                )
                
                # Process matches
                for profile in attending_profiles:
                    try:
                        match_success, face_matches, _ = RekognitionService.search_faces_by_image(
                            settings.AWS_REKOGNITION_COLLECTION_ID,
                            image_bytes,
                            threshold=70.0
                        )
                        
                        if match_success and face_matches:
                            for match in face_matches:
                                if match['Face']['FaceId'] == profile.face_id:
                                    FaceMatchResult.objects.create(
                                        user=profile.user,
                                        event_photo=photo,
                                        confidence_score=match['Face']['Confidence'],
                                        similarity_score=match['Similarity'],
                                        bounding_box=match['Face'].get('BoundingBox', {}),
                                        is_verified=match['Similarity'] >= 85.0,
                                    )
                                    break
                    except Exception as e:
                        logger.error(f"Error matching face: {str(e)}")
            
            # Mark as processed
            photo.processed_for_faces = True
            photo.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Error in direct photo processing: {str(e)}")
            return False


# Global service instance
photofish_queue = PhotoFishQueueService()

# Convenience functions for backwards compatibility
def add_photos_to_queue(photo_ids: List[str], priority: str = "STANDARD", metadata: Optional[Dict] = None) -> Tuple[bool, Dict]:
    """Add photos to processing queue"""
    return photofish_queue.add_photos_for_face_processing(photo_ids, priority, metadata)

def get_queue_status() -> Dict:
    """Get current queue status"""
    return photofish_queue.get_queue_status()

def get_job_status(job_id: str) -> Dict:
    """Get job status"""
    return photofish_queue.get_job_status(job_id)