from rest_framework import serializers
from .models import FacialProfile, FaceScanSession, FaceMatchResult


class FacialProfileSerializer(serializers.ModelSerializer):
    """Serializer for the FacialProfile model"""
    user_email = serializers.EmailField(source='user.email', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = FacialProfile
        fields = [
            'id', 'user', 'user_email', 'username', 'face_id', 
            'confidence_score', 'is_verified', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'user_email', 'username', 'face_id', 'created_at', 'updated_at']
        extra_kwargs = {
            'facial_features': {'write_only': True}  # Don't expose facial features in API
        }


class FaceScanSessionSerializer(serializers.ModelSerializer):
    """Serializer for the FaceScanSession model"""
    user_email = serializers.EmailField(source='user.email', read_only=True)
    
    class Meta:
        model = FaceScanSession
        fields = [
            'id', 'user', 'user_email', 'status', 'error_message',
            'created_at', 'completed_at'
        ]
        read_only_fields = ['id', 'user', 'user_email', 'status', 'error_message', 
                           'created_at', 'completed_at']


class FaceMatchResultSerializer(serializers.ModelSerializer):
    """Serializer for the FaceMatchResult model"""
    user_email = serializers.EmailField(source='user.email', read_only=True)
    username = serializers.CharField(source='user.username', read_only=True)
    event_id = serializers.UUIDField(source='event_photo.event.id', read_only=True)
    event_name = serializers.CharField(source='event_photo.event.name', read_only=True)
    photo_url = serializers.ImageField(source='event_photo.image', read_only=True)
    
    class Meta:
        model = FaceMatchResult
        fields = [
            'id', 'user', 'user_email', 'username', 'event_photo', 
            'event_id', 'event_name', 'photo_url', 'confidence_score', 
            'similarity_score', 'is_verified', 'is_rejected', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'user_email', 'username', 'event_photo', 
                           'event_id', 'event_name', 'photo_url', 'confidence_score', 
                           'similarity_score', 'created_at', 'updated_at']


class FaceScanInputSerializer(serializers.Serializer):
    """Serializer for face scan input data"""
    image_data = serializers.CharField(required=True, help_text="Base64 encoded image data")


class FaceVerificationSerializer(serializers.Serializer):
    """Serializer for face verification input"""
    is_verified = serializers.BooleanField(required=True, help_text="Whether the match is verified or rejected")