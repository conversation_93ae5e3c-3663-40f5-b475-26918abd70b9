import boto3
import logging
import os
import uuid
from datetime import datetime
from typing import Dict, Tu<PERSON>, List, Optional, Any
from django.conf import settings
from django.core.files.base import ContentFile
import base64
from io import BytesIO
from PIL import Image

from .models import FacialProfile, FaceScanSession, FaceMatchResult

logger = logging.getLogger('facial_recognition')

# AWS Rekognition client
rekognition_client = boto3.client(
    'rekognition',
    region_name=settings.AWS_REGION_NAME,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
)

# AWS S3 client for storing facial images
s3_client = boto3.client(
    's3',
    region_name=settings.AWS_REGION_NAME,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY
)


class RekognitionService:
    """Service class for AWS Rekognition operations"""
    
    @staticmethod
    def detect_faces(image_bytes: bytes) -> Tuple[bool, Dict, str]:
        """
        Detect faces in an image using AWS Rekognition
        
        Args:
            image_bytes: Binary image data
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Face details if successful (dict)
            - Error message if failed (string)
        """
        try:
            response = rekognition_client.detect_faces(
                Image={'Bytes': image_bytes},
                Attributes=['ALL']
            )
            
            # Check if any face was detected
            if not response.get('FaceDetails', []):
                return False, {}, "No face detected in the image"
                
            # For now, we only consider the first (most prominent) face
            face_details = response['FaceDetails'][0]
            
            # Check face quality
            if not RekognitionService._validate_face_quality(face_details):
                return False, {}, "Face quality too low or face not properly aligned"
                
            return True, face_details, ""
            
        except Exception as e:
            logger.error(f"Face detection error: {str(e)}", exc_info=True)
            return False, {}, f"Error detecting face: {str(e)}"
    
    @staticmethod
    def _validate_face_quality(face_details: Dict) -> bool:
        """Relaxed validation for real-world photos"""
        
        # Only check confidence score (AWS already detected the face)
        confidence = face_details.get('Confidence', 0)
        if confidence < 70:  # Lowered from 90 to 70
            return False
        
        # Remove strict pose requirements for sports photos
        # Remove eye open requirements for action shots
        # Remove strict quality requirements for varied lighting
        
        return True
    
    @staticmethod
    def create_face_collection(collection_id: str) -> Tuple[bool, str]:
        """
        Create a face collection in AWS Rekognition if it doesn't exist
        
        Args:
            collection_id: ID for the collection
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Error message if failed (string)
        """
        try:
            # Check if collection exists
            try:
                rekognition_client.describe_collection(CollectionId=collection_id)
                logger.info(f"Collection {collection_id} already exists")
                return True, ""
            except rekognition_client.exceptions.ResourceNotFoundException:
                # Collection doesn't exist, create it
                rekognition_client.create_collection(CollectionId=collection_id)
                logger.info(f"Created new collection: {collection_id}")
                return True, ""
                
        except Exception as e:
            logger.error(f"Error creating face collection: {str(e)}", exc_info=True)
            return False, f"Error creating face collection: {str(e)}"
    
    @staticmethod
    def index_face(collection_id: str, image_bytes: bytes, external_image_id: str) -> Tuple[bool, str, Dict]:
        """
        Index a face in AWS Rekognition collection
        
        Args:
            collection_id: ID of the collection
            image_bytes: Binary image data
            external_image_id: External ID to assign to the face
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Face ID if successful or error message if failed (string)
            - Face details if successful (dict)
        """
        try:
            response = rekognition_client.index_faces(
                CollectionId=collection_id,
                Image={'Bytes': image_bytes},
                ExternalImageId=external_image_id,
                DetectionAttributes=['ALL']
            )
            
            # Check if any face was indexed
            if not response.get('FaceRecords', []):
                return False, "No face was indexed", {}
                
            # Get the face ID and details
            face_record = response['FaceRecords'][0]
            face_id = face_record['Face']['FaceId']
            face_details = face_record['FaceDetail']
            
            return True, face_id, face_details
            
        except Exception as e:
            logger.error(f"Face indexing error: {str(e)}", exc_info=True)
            return False, f"Error indexing face: {str(e)}", {}
    
    @staticmethod
    def search_faces_by_image(collection_id: str, image_bytes: bytes, threshold: float = 95.0) -> Tuple[bool, List, str]:
        """
        Search for matching faces in a collection using an image
        
        Args:
            collection_id: ID of the collection
            image_bytes: Binary image data
            threshold: Minimum confidence threshold (0-100)
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - List of matches if successful (list)
            - Error message if failed (string)
        """
        try:
            response = rekognition_client.search_faces_by_image(
                CollectionId=collection_id,
                Image={'Bytes': image_bytes},
                MaxFaces=10,
                FaceMatchThreshold=threshold
            )
            
            return True, response.get('FaceMatches', []), ""
            
        except rekognition_client.exceptions.InvalidParameterException:
            return False, [], "No face detected in the image"
        except Exception as e:
            logger.error(f"Face search error: {str(e)}", exc_info=True)
            return False, [], f"Error searching faces: {str(e)}"
    
    @staticmethod
    def delete_face(collection_id: str, face_id: str) -> Tuple[bool, str]:
        """
        Delete a face from a collection
        
        Args:
            collection_id: ID of the collection
            face_id: ID of the face to delete
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Error message if failed (string)
        """
        try:
            rekognition_client.delete_faces(
                CollectionId=collection_id,
                FaceIds=[face_id]
            )
            return True, ""
            
        except Exception as e:
            logger.error(f"Face deletion error: {str(e)}", exc_info=True)
            return False, f"Error deleting face: {str(e)}"


class FacialRecognitionService:
    """Service class for facial recognition operations"""
    
    @staticmethod
    def start_scan_session(user_id) -> Tuple[bool, str, Optional[FaceScanSession]]:
        """
        Start a new face scanning session for a user
        
        Args:
            user_id: ID of the user
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Session ID or error message (string)
            - FaceScanSession object if successful (or None)
        """
        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            user = User.objects.get(id=user_id)
            
            # Check if AWS collection exists
            success, error = RekognitionService.create_face_collection(settings.AWS_REKOGNITION_COLLECTION_ID)
            if not success:
                return False, error, None
            
            # Create a new scan session
            session = FaceScanSession.objects.create(
                user=user,
                status='STARTED',
                session_data={}
            )
            
            return True, str(session.id), session
            
        except Exception as e:
            logger.error(f"Error starting scan session: {str(e)}", exc_info=True)
            return False, f"Error starting scan session: {str(e)}", None
    
    @staticmethod
    def process_face_scan(session_id: str, image_data: str) -> Tuple[bool, str, Dict]:
        """
        Process a face scan from a scan session
        
        Args:
            session_id: ID of the scan session
            image_data: Base64 encoded image data
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Message (string)
            - Result data (dict)
        """
        try:
            # Get the session
            try:
                session = FaceScanSession.objects.get(id=session_id)
            except FaceScanSession.DoesNotExist:
                return False, "Scan session not found", {}
            
            # Check if session is already completed
            if session.status != 'STARTED':
                return False, f"Scan session is already {session.status}", {}
            
            # Process the image data
            try:
                # Strip base64 header if present
                if 'base64,' in image_data:
                    image_data = image_data.split('base64,')[1]
                    
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                logger.error(f"Error decoding image data: {str(e)}", exc_info=True)
                return False, "Invalid image data format", {}
            
            # Detect faces in the image
            success, face_details, error = RekognitionService.detect_faces(image_bytes)
            if not success:
                session.status = 'FAILED'
                session.error_message = error
                session.save()
                return False, error, {}
            
            # Index the face
            user_id_str = str(session.user.id)
            success, face_id, face_details = RekognitionService.index_face(
                settings.AWS_REKOGNITION_COLLECTION_ID,
                image_bytes,
                user_id_str
            )
            
            if not success:
                session.status = 'FAILED'
                session.error_message = face_id  # Contains error message in this case
                session.save()
                return False, face_id, {}
            
            # Save the facial profile
            try:
                # Create or update facial profile
                facial_profile, created = FacialProfile.objects.get_or_create(
                    user=session.user,
                    defaults={
                        'face_id': face_id,
                        'facial_features': face_details,
                        'confidence_score': face_details.get('Confidence', 0),
                        'is_verified': True
                    }
                )
                
                if not created:
                    # Delete the old face from collection if it exists
                    if facial_profile.face_id:
                        RekognitionService.delete_face(
                            settings.AWS_REKOGNITION_COLLECTION_ID,
                            facial_profile.face_id
                        )
                    
                    # Update with new face
                    facial_profile.face_id = face_id
                    facial_profile.facial_features = face_details
                    facial_profile.confidence_score = face_details.get('Confidence', 0)
                    facial_profile.is_verified = True
                    facial_profile.save()
                
                # Save original image
                img = Image.open(BytesIO(image_bytes))
                img_io = BytesIO()
                img.save(img_io, format='JPEG')
                img_io.seek(0)
                
                # Generate a unique filename
                filename = f"{user_id_str}_{uuid.uuid4()}.jpg"
                facial_profile.face_image.save(filename, ContentFile(img_io.read()), save=True)
                
                # Update session
                session.status = 'COMPLETED'
                session.completed_at = datetime.now()
                session.save()
                
                return True, "Face scan completed successfully", {
                    "face_id": face_id,
                    "confidence_score": face_details.get('Confidence', 0)
                }
                
            except Exception as e:
                logger.error(f"Error saving facial profile: {str(e)}", exc_info=True)
                session.status = 'FAILED'
                session.error_message = f"Error saving facial profile: {str(e)}"
                session.save()
                return False, "Error saving facial profile", {}
                
        except Exception as e:
            logger.error(f"Error processing face scan: {str(e)}", exc_info=True)
            return False, f"Error processing face scan: {str(e)}", {}
    
    @staticmethod
    def match_faces_in_photo(photo_id: str) -> Tuple[bool, str, List[Dict]]:
        """
        Match faces in a photo against the collection
        
        Args:
            photo_id: ID of the event photo
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Message (string)
            - List of matches (list of dicts)
        """
        try:
            from events.models import EventPhoto
            
            # Get the photo
            try:
                photo = EventPhoto.objects.get(id=photo_id)
            except EventPhoto.DoesNotExist:
                return False, "Photo not found", []
            
            # Get the image bytes
            try:
                photo_file = photo.image.open('rb')
                image_bytes = photo_file.read()
                photo_file.close()
            except Exception as e:
                logger.error(f"Error reading photo file: {str(e)}", exc_info=True)
                return False, "Error reading photo file", []
            
            # Detect faces in the photo
            success, faces, error = RekognitionService.detect_faces(image_bytes)
            if not success:
                return False, error, []
            
            # If no faces detected, we're done
            if not faces:
                return True, "No faces detected in photo", []
            
            # For each detected face, search for matches
            matches = []
            
            # For now, we'll use search_faces_by_image since we're working with the entire photo
            success, face_matches, error = RekognitionService.search_faces_by_image(
                settings.AWS_REKOGNITION_COLLECTION_ID,
                image_bytes,
                threshold=70.0  # Lower threshold for better recall
            )
            
            if not success:
                return False, error, []
            
            # Process matches
            for match in face_matches:
                face_id = match['Face']['FaceId']
                similarity = match['Similarity']
                
                # Find the user associated with this face_id
                try:
                    facial_profile = FacialProfile.objects.get(face_id=face_id)
                    user = facial_profile.user
                    
                    # Create or update the match result
                    face_match, created = FaceMatchResult.objects.update_or_create(
                        user=user,
                        event_photo=photo,
                        defaults={
                            'confidence_score': match['Face']['Confidence'],
                            'similarity_score': similarity,
                            'bounding_box': match['Face'].get('BoundingBox', {}),
                        }
                    )
                    
                    matches.append({
                        'user_id': str(user.id),
                        'email': user.email,
                        'username': user.username,
                        'similarity': similarity,
                        'match_id': str(face_match.id)
                    })
                    
                except FacialProfile.DoesNotExist:
                    logger.warning(f"Found face ID {face_id} with no associated user")
                    continue
            
            return True, f"Found {len(matches)} matches", matches
            
        except Exception as e:
            logger.error(f"Error matching faces in photo: {str(e)}", exc_info=True)
            return False, f"Error matching faces in photo: {str(e)}", []
    
    @staticmethod
    def verify_match(match_id: str, is_verified: bool) -> Tuple[bool, str]:
        """
        Verify or reject a face match
        
        Args:
            match_id: ID of the match
            is_verified: Whether to verify or reject the match
            
        Returns:
            Tuple containing:
            - Success status (boolean)
            - Message (string)
        """
        try:
            # Get the match
            try:
                match = FaceMatchResult.objects.get(id=match_id)
            except FaceMatchResult.DoesNotExist:
                return False, "Match not found"
            
            if is_verified:
                match.is_verified = True
                match.is_rejected = False
            else:
                match.is_verified = False
                match.is_rejected = True
                
            match.save()
            
            return True, "Match updated successfully"
            
        except Exception as e:
            logger.error(f"Error verifying match: {str(e)}", exc_info=True)
            return False, f"Error verifying match: {str(e)}"