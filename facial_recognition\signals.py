import logging
from django.db.models.signals import post_save, post_delete, pre_delete
from django.dispatch import receiver
from .models import FacialProfile, FaceMatchResult
from django.conf import settings

logger = logging.getLogger('facial_recognition')


@receiver(post_save, sender=FacialProfile)
def facial_profile_saved_handler(sender, instance, created, **kwargs):
    """Handle FacialProfile post-save signal"""
    if created:
        logger.info(f"New facial profile created for user {instance.user.id}")
    else:
        logger.info(f"Facial profile updated for user {instance.user.id}")


@receiver(pre_delete, sender=FacialProfile)
def facial_profile_pre_delete_handler(sender, instance, **kwargs):
    """Handle FacialProfile pre-delete signal to clean up AWS resources"""
    if instance.face_id:
        try:
            from .services import RekognitionService
            
            # Delete the face from AWS Rekognition collection
            success, error = RekognitionService.delete_face(
                settings.AWS_REKOGNITION_COLLECTION_ID,
                instance.face_id
            )
            
            if not success:
                logger.warning(f"Failed to delete face {instance.face_id} from AWS Rekognition: {error}")
            else:
                logger.info(f"Face {instance.face_id} deleted from AWS Rekognition for user {instance.user.id}")
                
        except Exception as e:
            logger.error(f"Error deleting face from AWS Rekognition: {str(e)}", exc_info=True)


@receiver(post_save, sender=FaceMatchResult)
def face_match_saved_handler(sender, instance, created, **kwargs):
    """Handle FaceMatchResult post-save signal"""
    if created:
        logger.info(f"New face match created: User {instance.user.id} in photo {instance.event_photo.id} with score {instance.similarity_score}")
    else:
        verification_status = "verified" if instance.is_verified else ("rejected" if instance.is_rejected else "pending")
        logger.info(f"Face match updated: User {instance.user.id} in photo {instance.event_photo.id} - Status: {verification_status}")