from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch
import uuid
import base64
from io import BytesIO
from PIL import Image
import json

from .models import FacialProfile, FaceScanSession, FaceMatchResult

User = get_user_model()


class FacialRecognitionTestCase(TestCase):
    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='password123',
            is_email_verified=True
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Create a test session
        self.session = FaceScanSession.objects.create(
            user=self.user,
            status='STARTED',
            session_data={}
        )
    
    def test_start_scan(self):
        """Test starting a new face scan session"""
        with patch('facial_recognition.services.RekognitionService.create_face_collection') as mock_create_collection:
            mock_create_collection.return_value = (True, "")
            
            response = self.client.post('/api/v1/facial-recognition/start-scan/')
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            data = response.json()
            self.assertTrue(data['success'])
            self.assertIn('session_id', data)
    
    def test_scan_status_no_profile(self):
        """Test getting scan status when no profile exists"""
        response = self.client.get('/api/v1/facial-recognition/scan-status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertFalse(data['has_profile'])
        self.assertFalse(data['is_verified'])
    
    def test_scan_status_with_profile(self):
        """Test getting scan status when profile exists"""
        # Create a facial profile
        profile = FacialProfile.objects.create(
            user=self.user,
            face_id='test-face-id',
            confidence_score=95.0,
            is_verified=True
        )
        
        response = self.client.get('/api/v1/facial-recognition/scan-status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['success'])
        self.assertTrue(data['has_profile'])
        self.assertTrue(data['is_verified'])
        self.assertEqual(data['confidence_score'], 95.0)
    
    def test_process_scan(self):
        """Test processing a face scan"""
        # Create a dummy image
        img = Image.new('RGB', (100, 100), color = 'red')
        img_io = BytesIO()
        img.save(img_io, format='JPEG')
        img_io.seek(0)
        
        # Encode as base64
        image_data = base64.b64encode(img_io.getvalue()).decode('utf-8')
        
        # Mock the detect_faces and index_face functions
        with patch('facial_recognition.services.RekognitionService.detect_faces') as mock_detect:
            with patch('facial_recognition.services.RekognitionService.index_face') as mock_index:
                # Setup mock return values
                mock_detect.return_value = (True, {'Confidence': 95.0}, "")
                mock_index.return_value = (True, 'new-face-id', {'Confidence': 95.0})
                
                response = self.client.post(
                    f'/api/v1/facial-recognition/process-scan/?session_id={self.session.id}',
                    {'image_data': image_data},
                    format='json'
                )
                
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                data = response.json()
                self.assertTrue(data['success'])
                self.assertEqual(data['data']['face_id'], 'new-face-id')
                
                # Verify profile was created
                profile = FacialProfile.objects.get(user=self.user)
                self.assertEqual(profile.face_id, 'new-face-id')
                self.assertTrue(profile.is_verified)
    
    def test_reset_profile(self):
        """Test resetting a facial profile"""
        # Create a facial profile
        profile = FacialProfile.objects.create(
            user=self.user,
            face_id='test-face-id',
            confidence_score=95.0,
            is_verified=True
        )
        
        # Mock the delete_face function
        with patch('facial_recognition.services.RekognitionService.delete_face') as mock_delete:
            mock_delete.return_value = (True, "")
            
            response = self.client.delete('/api/v1/facial-recognition/reset-profile/')
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            
            data = response.json()
            self.assertTrue(data['success'])
            
            # Verify profile was deleted
            self.assertEqual(FacialProfile.objects.filter(user=self.user).count(), 0)
    
    def test_face_match_verification(self):
        """Test verifying a face match"""
        # Create a facial profile
        profile = FacialProfile.objects.create(
            user=self.user,
            face_id='test-face-id',
            confidence_score=95.0,
            is_verified=True
        )
        
        # Create an event photo (mock)
        from photos.models import EventPhoto
        event_photo = EventPhoto.objects.create(
            id=uuid.uuid4(),
            event_id=uuid.uuid4(),
            photographer=self.user,
            price=10.0
        )
        
        # Create a face match
        match = FaceMatchResult.objects.create(
            user=self.user,
            event_photo=event_photo,
            confidence_score=90.0,
            similarity_score=85.0,
            bounding_box={'Width': 0.5, 'Height': 0.5, 'Left': 0.25, 'Top': 0.25}
        )
        
        # Test verification
        response = self.client.post(
            f'/api/v1/facial-recognition/matches/{match.id}/verify/',
            {'is_verified': True},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify the match was updated
        match.refresh_from_db()
        self.assertTrue(match.is_verified)
        self.assertFalse(match.is_rejected)
        
        # Test rejection
        response = self.client.post(
            f'/api/v1/facial-recognition/matches/{match.id}/verify/',
            {'is_verified': False},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify the match was updated
        match.refresh_from_db()
        self.assertFalse(match.is_verified)
        self.assertTrue(match.is_rejected)