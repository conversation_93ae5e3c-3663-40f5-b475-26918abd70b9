from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import FacialRecognitionViewSet

router = DefaultRouter()
router.register(r'facial-recognition', FacialRecognitionViewSet, basename='facial-recognition')

urlpatterns = [
    path('', include(router.urls)),
    # Custom URLs for specific actions
    path('start-scan/', FacialRecognitionViewSet.as_view({'post': 'start_scan'}), name='start-scan'),
    path('process-scan/', FacialRecognitionViewSet.as_view({'post': 'process_scan'}), name='process-scan'),
    path('scan-status/', FacialRecognitionViewSet.as_view({'get': 'scan_status'}), name='scan-status'),
    path('matches/', FacialRecognitionViewSet.as_view({'get': 'matches'}), name='face-matches'),
    path('matches/<uuid:pk>/verify/', FacialRecognitionViewSet.as_view({'post': 'verify_match'}), name='verify-match'),
    path('reset-profile/', FacialRecognitionViewSet.as_view({'delete': 'reset_profile'}), name='reset-profile'),
]