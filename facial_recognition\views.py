from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
import logging

from .models import FacialProfile, FaceScanSession, FaceMatchResult
from .serializers import (
    FacialProfileSerializer, FaceScanSessionSerializer, 
    FaceMatchResultSerializer, FaceScanInputSerializer,
    FaceVerificationSerializer
)
from .services import FacialRecognitionService

logger = logging.getLogger('facial_recognition')


class FacialRecognitionViewSet(viewsets.GenericViewSet):
    """
    ViewSet for facial recognition operations
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def start_scan(self, request):
        """
        Start a new face scan session
        """
        try:
            success, message, session = FacialRecognitionService.start_scan_session(request.user.id)
            
            if not success:
                return Response({
                    'success': False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'success': True,
                'message': 'Scan session started successfully',
                'session_id': message,  # Contains session ID in case of success
            })
            
        except Exception as e:
            logger.error(f"Error starting scan: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def process_scan(self, request):
        """
        Process a face scan
        """
        serializer = FaceScanInputSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        session_id = request.query_params.get('session_id')
        if not session_id:
            return Response({
                'success': False,
                'message': 'Session ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Verify the session belongs to the requesting user
            try:
                session = FaceScanSession.objects.get(id=session_id)
                if session.user.id != request.user.id:
                    return Response({
                        'success': False,
                        'message': 'Access denied'
                    }, status=status.HTTP_403_FORBIDDEN)
            except FaceScanSession.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Invalid session ID'
                }, status=status.HTTP_404_NOT_FOUND)
            
            image_data = serializer.validated_data.get('image_data')
            success, message, result = FacialRecognitionService.process_face_scan(session_id, image_data)
            
            if not success:
                return Response({
                    'success': False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'success': True,
                'message': message,
                'data': result
            })
            
        except Exception as e:
            logger.error(f"Error processing scan: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def scan_status(self, request):
        """
        Get face scan status for the current user
        """
        try:
            # Check if user has a facial profile
            profile = FacialProfile.objects.filter(user=request.user).first()
            
            if profile and profile.is_verified and profile.face_id:
                return Response({
                    'success': True,
                    'has_profile': True,
                    'is_verified': True,
                    'confidence_score': profile.confidence_score,
                    'created_at': profile.created_at,
                    'updated_at': profile.updated_at
                })
            
            # Check if there's an active session
            session = FaceScanSession.objects.filter(
                user=request.user
            ).order_by('-created_at').first()
            
            if session:
                return Response({
                    'success': True,
                    'has_profile': bool(profile),
                    'is_verified': False,
                    'session_id': session.id,
                    'session_status': session.status,
                    'created_at': session.created_at
                })
            
            return Response({
                'success': True,
                'has_profile': bool(profile),
                'is_verified': False
            })
            
        except Exception as e:
            logger.error(f"Error checking scan status: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['get'])
    def matches(self, request):
        """
        Get all face matches for the current user
        """
        try:
            matches = FaceMatchResult.objects.filter(user=request.user)
            
            # Apply filters
            verified = request.query_params.get('verified')
            if verified is not None:
                verified = verified.lower() == 'true'
                matches = matches.filter(is_verified=verified)
            
            rejected = request.query_params.get('rejected')
            if rejected is not None:
                rejected = rejected.lower() == 'true'
                matches = matches.filter(is_rejected=rejected)
            
            # Get event filter
            event_id = request.query_params.get('event_id')
            if event_id:
                matches = matches.filter(event_photo__event__id=event_id)
            
            # Order by similarity score (descending)
            matches = matches.order_by('-similarity_score')
            
            serializer = FaceMatchResultSerializer(matches, many=True)
            
            return Response({
                'success': True,
                'count': matches.count(),
                'matches': serializer.data
            })
            
        except Exception as e:
            logger.error(f"Error fetching matches: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def verify_match(self, request, pk=None):
        """
        Verify or reject a face match
        """
        serializer = FaceVerificationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Verify the match belongs to the requesting user
            try:
                match = FaceMatchResult.objects.get(id=pk)
                if match.user.id != request.user.id:
                    return Response({
                        'success': False,
                        'message': 'Access denied'
                    }, status=status.HTTP_403_FORBIDDEN)
            except FaceMatchResult.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Match not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            is_verified = serializer.validated_data.get('is_verified')
            success, message = FacialRecognitionService.verify_match(pk, is_verified)
            
            if not success:
                return Response({
                    'success': False,
                    'message': message
                }, status=status.HTTP_400_BAD_REQUEST)
            
            return Response({
                'success': True,
                'message': message
            })
            
        except Exception as e:
            logger.error(f"Error verifying match: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['delete'])
    def reset_profile(self, request):
        """
        Reset facial profile for the current user
        """
        try:
            profile = FacialProfile.objects.filter(user=request.user).first()
            
            if not profile:
                return Response({
                    'success': False,
                    'message': 'No facial profile found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Delete the face from AWS Rekognition collection if it exists
            if profile.face_id:
                from django.conf import settings
                from .services import RekognitionService
                
                success, error = RekognitionService.delete_face(
                    settings.AWS_REKOGNITION_COLLECTION_ID,
                    profile.face_id
                )
                
                if not success:
                    logger.warning(f"Error deleting face from Rekognition: {error}")
            
            # Delete the profile
            profile.delete()
            
            return Response({
                'success': True,
                'message': 'Facial profile reset successfully'
            })
            
        except Exception as e:
            logger.error(f"Error resetting profile: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)