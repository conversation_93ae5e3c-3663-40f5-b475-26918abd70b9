"""
Django settings for photofish project.

Generated by 'django-admin startproject' using Django 5.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
from datetime import timedelta
import os
import warnings
import django.db.backends.utils


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-+2(2kuh4$im6^t0p7!60^+@9^ev5qmc*zn82k756wsi-@evk&^'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

PHOTOFISH_LOGO_URL = "https://lh3.google.com/u/0/d/1h69usxDCemYzATjonAt6Akk96SMbKYoG=w3840-h1957-iv1?auditContext=forDisplay"

# Rate Limiting Configuration
RATELIMIT_USE_CACHE = 'default'
RATELIMIT_ENABLE = True


warnings.filterwarnings(
    'ignore',
    message='Accessing the database during app initialization is discouraged.*',
    category=RuntimeWarning,
    module='django.db.backends.utils'
)



# Twilio SMS Configuration (Add your credentials)
TWILIO_ACCOUNT_SID = 'your_twilio_account_sid'
TWILIO_AUTH_TOKEN = 'your_twilio_auth_token'  
TWILIO_PHONE_NUMBER = '+**********'  # Your Twilio phone number

# Application definition

# Frontend URL for password reset links
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:3000')

# Phone number field settings
PHONENUMBER_DEFAULT_REGION = 'IN'  # India as default
PHONENUMBER_DEFAULT_FORMAT = 'E164'

# Rate limiting settings
OTP_RATE_LIMIT_PER_IP = 5  # Max 5 OTPs per hour per IP
OTP_RESEND_COOLDOWN_MINUTES = 2  # Wait 2 minutes between resends

# Social Auth Settings (Google only)
SOCIAL_AUTH_GOOGLE_OAUTH2_KEY = os.getenv('GOOGLE_OAUTH2_KEY', '')
SOCIAL_AUTH_GOOGLE_OAUTH2_SECRET = os.getenv('GOOGLE_OAUTH2_SECRET', '')
SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = ['openid', 'email', 'profile']


INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    'rest_framework',
    'corsheaders',
    'drf_yasg',
    'rest_framework_simplejwt',
    'social_django',
    
    'users',
    'events',
    'subscriptions',
    'phonenumber_field',
    'facial_recognition',
    'queue_system',
    'photographers',
    
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'social_django.middleware.SocialAuthExceptionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'users.middleware.RequestLoggingMiddleware', 
]

AUTH_USER_MODEL = 'users.User'

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.AllowAny',
    ),
    'DEFAULT_SCHEMA_CLASS': 'rest_framework.schemas.coreapi.AutoSchema',
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10
}


SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),  # Fixed: was 60 minutes - too long!
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),     # Extended: better UX
    'ROTATE_REFRESH_TOKENS': True,                   # Fixed: was False - security risk!
    'BLACKLIST_AFTER_ROTATION': True,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,
    'JWK_URL': None,
    'LEEWAY': 0,
}

AUTHENTICATION_BACKENDS = (
    'social_core.backends.google.GoogleOAuth2',  # Keep only Google
    'django.contrib.auth.backends.ModelBackend',
)

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # or your SMTP server
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'vszx pdgk scwf czsf'  # Use environment variable in production
DEFAULT_FROM_EMAIL = '<EMAIL>'

STATIC_ROOT = BASE_DIR / 'staticfiles'

# AWS Credentials - Should be stored in environment variables in production
AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = 'FpGLpXCq5Q7XzxMDaikB+ov/mvIOIAdQLdeLv2V3'
AWS_REGION_NAME = 'ap-south-1'  # Change to your preferred region

# AWS Rekognition specific settings
AWS_REKOGNITION_COLLECTION_ID = 'photofish-faces'  # Name of your face collection

# Social Auth Pipeline
SOCIAL_AUTH_PIPELINE = (
    'social_core.pipeline.social_auth.social_details',
    'social_core.pipeline.social_auth.social_uid',
    'social_core.pipeline.social_auth.auth_allowed',
    'social_core.pipeline.social_auth.social_user',
    'social_core.pipeline.user.get_username',
    'social_core.pipeline.social_auth.associate_by_email',
    'social_core.pipeline.user.create_user',
    'social_core.pipeline.social_auth.associate_user',
    'social_core.pipeline.social_auth.load_extra_data',
    'social_core.pipeline.user.user_details',
)

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'social_django.context_processors.backends',
                'social_django.context_processors.login_redirect',
            ],
        },
    },
]

WSGI_APPLICATION = 'photofish.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'photofish_customer_app'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'Photofish12345'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
    }
}


# Swagger Settings
SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
    'USE_SESSION_AUTH': False,
    'JSON_EDITOR': True,
    'SUPPORTED_SUBMIT_METHODS': ['get', 'post', 'put', 'delete', 'patch'],
    'OPERATIONS_SORTER': 'alpha',
    'DEFAULT_AUTO_SCHEMA_CLASS': 'drf_yasg.inspectors.SwaggerAutoSchema',
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

CORS_ALLOW_ALL_ORIGINS = True  # Change this in production
CORS_ALLOW_CREDENTIALS = True
ROOT_URLCONF = 'photofish.urls'

# Create logs directory if it doesn't exist
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{asctime} {levelname} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{asctime} {levelname} {message}',
            'style': '{',
        },
        'detailed': {
            'format': '{asctime} {levelname} {name} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {  # Add the missing 'file' handler
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'photofish.log'),
            'formatter': 'detailed',
        },
        'users_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'users.log'),
            'formatter': 'detailed',
        },
        'facial_recognition_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'facial_recognition.log'),
            'formatter': 'detailed',
        },
        'events_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'events.log'),
            'formatter': 'detailed',
        },
        'subscription_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'subscription.log'),
            'formatter': 'detailed',
        },
        'queue_system_file': {  # Add queue system specific handler
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'queue_system.log'),
            'formatter': 'detailed',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'error.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'error_file'],
            'level': 'INFO',
            'propagate': True,
        },
        'users': {
            'handlers': ['console', 'users_file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'facial_recognition': {
            'handlers': ['console', 'facial_recognition_file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'events': {
            'handlers': ['console', 'events_file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'subscription': {
            'handlers': ['console', 'subscription_file', 'error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'queue_system': {  # Add queue system logger
            'handlers': ['console', 'queue_system_file', 'error_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}


# Enhanced Cache Configuration for Rate Limiting & OTP Storage
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        'TIMEOUT': 300,  # 5 minutes default
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}

FACE_MATCHING_THRESHOLD = 70.0
FACE_VERIFICATION_THRESHOLD = 85.0

# Enhanced Queue System Configuration
ENABLE_ENHANCED_QUEUE = True  # Set to False to use old system
ENABLE_FACE_WORKER = True

ENHANCED_QUEUE_SETTINGS = {
    'WORKER_POOLS': {
        'EMERGENCY': {'min_workers': 2, 'max_workers': 4},
        'HIGH': {'min_workers': 3, 'max_workers': 6},
        'STANDARD': {'min_workers': 2, 'max_workers': 4},
        'LOW': {'min_workers': 1, 'max_workers': 2},
        'MAINTENANCE': {'min_workers': 1, 'max_workers': 1}
    },
    'CONNECTION_POOLS': {
        'AWS_REKOGNITION': {'min': 5, 'max': 15, 'timeout': 30},
        'DATABASE': {'min': 10, 'max': 25, 'timeout': 20},
        'REDIS': {'min': 5, 'max': 10, 'timeout': 15}
    },

    'JOB_PROCESSING': {
        'default_timeout': 1800,  # 30 minutes
        'max_retries': 3,
        'retry_delay_base': 5,    # seconds
        'batch_size_limit': 100
    },

    'MONITORING': {
        'METRICS_INTERVAL': 10,  # seconds
        'HEALTH_CHECK_INTERVAL': 30,  # seconds
        'LOG_ROTATION_SIZE': '50MB',
        'LOG_RETENTION_DAYS': 30
    },
    'ERROR_HANDLING': {
        'MAX_RETRIES': 3,
        'BASE_DELAY': 2,  # seconds
        'MAX_DELAY': 60,  # seconds
        'CIRCUIT_BREAKER_THRESHOLD': 5
    }
}

# Enhanced Logging for Queue System
LOGGING['loggers']['queue_system'] = {
    'handlers': ['console', 'queue_system_file', 'error_file'],
    'level': 'INFO',
    'propagate': False,
}



# Security Settings Enhancement
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Session Security
SESSION_COOKIE_SECURE = True  # Enable in production with HTTPS
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict'

SMS_PROVIDER_PRIORITY = [
    'textbelt',    # International, free tier
    'fast2sms',    # Indian service
    'msg91',       # Indian service  
    'development'  # Fallback for testing
]


# Development mode (always enabled as fallback)
DEVELOPMENT_SMS_ENABLED = True

# Phone number settings for India
PHONENUMBER_DEFAULT_REGION = 'IN'  # India as default
PHONENUMBER_DEFAULT_FORMAT = 'E164'  # International format

# Fast2SMS Configuration
FAST2SMS_API_KEY = os.getenv('FAST2SMS_API_KEY', 'j80GCwqOY2cBt1dQulDaRW9SZHKMrgUonfvPm4eIFXL365zxspgtM6wD2Hdr01CNbLFI9PWViYm7eXQ4')
FAST2SMS_SENDER_ID = os.getenv('FAST2SMS_SENDER_ID', 'FSTSMS')
FAST2SMS_ROUTE = 'q'  # 'q' for quick route, 'p' for promotional, 't' for transactional
FAST2SMS_ENABLED = bool(FAST2SMS_API_KEY and FAST2SMS_API_KEY != 'YOUR_API_KEY_HERE')  # Fixed logic

# SMS Provider Priority (Fast2SMS first, then development)
SMS_PROVIDER_PRIORITY = [
    'fast2sms',    # Primary: Fast2SMS
    'textbelt',    # Backup: TextBelt (if available)
    'development'  # Fallback: Development mode
]

# SMS Rate Limiting
SMS_RATE_LIMIT_PER_IP = 5  # Max 5 SMS per hour per IP
SMS_RESEND_COOLDOWN_MINUTES = 2  # Wait 2 minutes between resends