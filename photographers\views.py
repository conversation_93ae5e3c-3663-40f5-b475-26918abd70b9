# photographers/views.py
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from events.models import Event, EventPhoto
from users.constants import USER_TYPE_PHOTOGRAPHER  # Import the constant

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def my_photos(request):
    """Get all photos uploaded by the authenticated photographer"""
    
    # Fix: Use the correct photographer verification
    if not request.user.has_role(USER_TYPE_PHOTOGRAPHER):
        return Response(
            {'error': 'User is not a photographer'}, 
            status=status.HTTP_403_FORBIDDEN
        )
    
    # Get all photos uploaded by this photographer
    photos = EventPhoto.objects.filter(
        photographer=request.user
    ).select_related('event').order_by('-uploaded_at')
    
    # Format response
    photo_data = []
    for photo in photos:
        photo_data.append({
            'id': str(photo.id),
            'event_id': str(photo.event.id),
            'event_name': photo.event.name,
            'image': photo.image.url if photo.image else None,
            'price': str(photo.price) if photo.price else None,
            'uploaded_at': photo.uploaded_at.isoformat(),
        })
    
    return Response({
        'success': True,
        'total_photos': photos.count(),
        'photos': photo_data
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def my_event_photos(request, event_id):
    """Get photos uploaded by photographer for a specific event"""
    
    # Fix: Use the correct photographer verification
    if not request.user.has_role(USER_TYPE_PHOTOGRAPHER):
        return Response(
            {'error': 'User is not a photographer'}, 
            status=status.HTTP_403_FORBIDDEN
        )
    
    try:
        event = Event.objects.get(id=event_id)
    except Event.DoesNotExist:
        return Response(
            {'error': 'Event not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    
    # Verify photographer is assigned to this event
    if not event.photographers.filter(id=request.user.id).exists():
        return Response(
            {'error': 'Not assigned to this event'}, 
            status=status.HTTP_403_FORBIDDEN
        )
    
    # Get photos for this event by this photographer
    photos = EventPhoto.objects.filter(
        event=event,
        photographer=request.user
    ).order_by('-uploaded_at')
    
    # Format response
    photo_data = []
    for photo in photos:
        photo_data.append({
            'id': str(photo.id),
            'image': photo.image.url if photo.image else None,
            'price': str(photo.price) if photo.price else None,
            'uploaded_at': photo.uploaded_at.isoformat(),
        })
    
    return Response({
        'success': True,
        'event_id': str(event.id),
        'event_name': event.name,
        'total_photos': photos.count(),
        'photos': photo_data
    })