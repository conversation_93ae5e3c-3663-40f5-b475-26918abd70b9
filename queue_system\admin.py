# queue_system/admin.py
"""
Full-featured Django admin interface for PhotoFish Queue System
All original functionality restored with field name fixes
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Count
from datetime import <PERSON><PERSON><PERSON>
import json

from .models import QueueJob, QueueMetrics, WorkerStatus, ErrorLog, QueueConfiguration, JobDependency


@admin.register(QueueJob)
class QueueJobAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for QueueJob model
    """
    
    list_display = [
        'job_id_short', 'job_type', 'priority', 'status_colored', 
        'created_at', 'processing_time_display'
    ]
    
    list_filter = [
        'status', 'priority', 'job_type', 'created_at'
    ]
    
    search_fields = [
        'id', 'job_type', 'worker_id', 'error_message'
    ]
    
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'processing_time', 
        'formatted_job_data', 'formatted_result_data'
    ]
    
    fieldsets = (
        ('Job Information', {
            'fields': ('id', 'job_type', 'priority', 'status')
        }),
        ('Execution Details', {
            'fields': ('worker_id', 'retry_count', 'max_retries')
        }),
        ('Timing Information', {
            'fields': ('created_at', 'updated_at', 'started_at', 'completed_at')
        }),
        ('Job Data', {
            'fields': ('formatted_job_data', 'formatted_result_data'),
            'classes': ('collapse',)
        }),
        ('Error Information', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
        ('Performance Metrics', {
            'fields': ('processing_time', 'timeout_seconds'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['cancel_selected_jobs', 'retry_failed_jobs']
    
    def job_id_short(self, obj):
        """Display shortened job ID"""
        return str(obj.id)[:8] + "..."
    job_id_short.short_description = "Job ID"
    
    def status_colored(self, obj):
        """Display status with color coding"""
        colors = {
            'PENDING': '#ffc107',
            'QUEUED': '#17a2b8',
            'PROCESSING': '#007bff',
            'COMPLETED': '#28a745',
            'FAILED': '#dc3545',
            'CANCELLED': '#6c757d',
            'RETRYING': '#fd7e14'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.status
        )
    status_colored.short_description = "Status"
    
    def processing_time_display(self, obj):
        """Display processing time in human-readable format"""
        if obj.processing_time:
            seconds = obj.processing_time
            if seconds < 60:
                return f"{seconds:.1f}s"
            elif seconds < 3600:
                return f"{seconds/60:.1f}m"
            else:
                return f"{seconds/3600:.1f}h"
        return "-"
    processing_time_display.short_description = "Processing Time"
    
    def formatted_job_data(self, obj):
        """Display job data in formatted JSON"""
        if obj.job_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.job_data, indent=2))
        return "No data"
    formatted_job_data.short_description = "Job Data"
    
    def formatted_result_data(self, obj):
        """Display job result in formatted JSON"""
        if obj.result_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.result_data, indent=2))
        return "No result"
    formatted_result_data.short_description = "Job Result"
    
    def cancel_selected_jobs(self, request, queryset):
        """Admin action to cancel selected jobs"""
        updated = queryset.filter(
            status__in=['PENDING', 'QUEUED']
        ).update(
            status='CANCELLED',
            completed_at=timezone.now()
        )
        self.message_user(request, f"{updated} jobs were cancelled.")
    cancel_selected_jobs.short_description = "Cancel selected jobs"
    
    def retry_failed_jobs(self, request, queryset):
        """Admin action to retry failed jobs"""
        updated = queryset.filter(status='FAILED').update(
            status='RETRYING',
            error_message=None,
            updated_at=timezone.now()
        )
        self.message_user(request, f"{updated} failed jobs were queued for retry.")
    retry_failed_jobs.short_description = "Retry failed jobs"


@admin.register(QueueMetrics)
class QueueMetricsAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for QueueMetrics model
    """
    
    list_display = [
        'metric_type', 'metric_name', 'metric_value', 'timestamp'
    ]
    
    list_filter = [
        'metric_type', 'metric_name', 'timestamp'
    ]
    
    search_fields = ['metric_type', 'metric_name']
    
    readonly_fields = ['timestamp']
    
    date_hierarchy = 'timestamp'
    
    def get_queryset(self, request):
        """Optimize queryset for admin display"""
        return super().get_queryset(request).select_related()


@admin.register(WorkerStatus)
class WorkerStatusAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for WorkerStatus model
    """
    
    list_display = [
        'worker_id', 'worker_type', 'status_colored', 
        'jobs_processed', 'utilization_display', 'last_heartbeat'
    ]
    
    list_filter = [
        'status', 'worker_type', 'last_heartbeat'
    ]
    
    search_fields = ['worker_id']
    
    readonly_fields = [
        'started_at', 'last_heartbeat', 'utilization_display'
    ]
    
    fieldsets = (
        ('Worker Information', {
            'fields': ('worker_id', 'worker_type', 'status')
        }),
        ('Performance Metrics', {
            'fields': ('jobs_processed', 'total_processing_time', 'average_processing_time', 'utilization_display')
        }),
        ('Current Job', {
            'fields': ('current_job_id',)
        }),
        ('Error Information', {
            'fields': ('error_count', 'last_error'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('system_info',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('started_at', 'last_heartbeat'),
            'classes': ('collapse',)
        })
    )
    
    def status_colored(self, obj):
        """Display status with color coding"""
        colors = {
            'IDLE': '#28a745',
            'BUSY': '#007bff',
            'STOPPING': '#ffc107',
            'STOPPED': '#6c757d',
            'ERROR': '#dc3545'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.status
        )
    status_colored.short_description = "Status"
    
    def utilization_display(self, obj):
        """Display worker utilization percentage"""
        if hasattr(obj, 'current_job_id') and obj.current_job_id:
            return "100% (Active)"
        elif obj.status == 'IDLE':
            return "0% (Idle)"
        elif obj.status == 'BUSY':
            return "Unknown (Busy)"
        return f"Status: {obj.status}"
    utilization_display.short_description = "Utilization"


@admin.register(ErrorLog)
class ErrorLogAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for ErrorLog model
    """
    
    list_display = [
        'error_id_short', 'error_type', 'error_category', 'severity_colored',
        'is_resolved', 'occurred_at'
    ]
    
    list_filter = [
        'error_category', 'severity', 'is_resolved', 'occurred_at'
    ]
    
    search_fields = [
        'error_id', 'error_type', 'error_message', 'worker_id'
    ]
    
    readonly_fields = [
        'error_id', 'occurred_at', 'formatted_context_data', 'formatted_recovery_actions'
    ]
    
    fieldsets = (
        ('Error Information', {
            'fields': ('error_id', 'error_type', 'error_category', 'severity')
        }),
        ('Error Details', {
            'fields': ('error_message', 'stack_trace')
        }),
        ('Context', {
            'fields': ('job', 'worker_id', 'formatted_context_data'),
            'classes': ('collapse',)
        }),
        ('Resolution', {
            'fields': ('is_resolved', 'resolution_notes', 'resolved_at')
        }),
        ('Recovery', {
            'fields': ('formatted_recovery_actions',),
            'classes': ('collapse',)
        }),
        ('Timestamp', {
            'fields': ('occurred_at',)
        })
    )
    
    actions = ['mark_as_resolved', 'mark_as_unresolved']
    
    def error_id_short(self, obj):
        """Display shortened error ID"""
        return str(obj.error_id)[:8] + "..."
    error_id_short.short_description = "Error ID"
    
    def severity_colored(self, obj):
        """Display severity with color coding"""
        colors = {
            'CRITICAL': '#dc3545',
            'HIGH': '#fd7e14',
            'MEDIUM': '#ffc107',
            'LOW': '#28a745',
            'INFO': '#17a2b8'
        }
        color = colors.get(obj.severity, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.severity
        )
    severity_colored.short_description = "Severity"
    
    def formatted_context_data(self, obj):
        """Display context data in formatted JSON"""
        if obj.context_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.context_data, indent=2))
        return "No context data"
    formatted_context_data.short_description = "Context Data"
    
    def formatted_recovery_actions(self, obj):
        """Display recovery actions in formatted JSON"""
        if obj.recovery_actions:
            return format_html('<pre>{}</pre>', json.dumps(obj.recovery_actions, indent=2))
        return "No recovery actions"
    formatted_recovery_actions.short_description = "Recovery Actions"
    
    def mark_as_resolved(self, request, queryset):
        """Admin action to mark errors as resolved"""
        updated = queryset.update(
            is_resolved=True,
            resolved_at=timezone.now()
        )
        self.message_user(request, f"{updated} errors were marked as resolved.")
    mark_as_resolved.short_description = "Mark as resolved"
    
    def mark_as_unresolved(self, request, queryset):
        """Admin action to mark errors as unresolved"""
        updated = queryset.update(
            is_resolved=False,
            resolved_at=None
        )
        self.message_user(request, f"{updated} errors were marked as unresolved.")
    mark_as_unresolved.short_description = "Mark as unresolved"


@admin.register(QueueConfiguration)
class QueueConfigurationAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for QueueConfiguration model
    """
    
    list_display = [
        'config_key', 'config_value_preview', 'is_active', 'updated_at'
    ]
    
    list_filter = ['is_active', 'updated_at']
    
    search_fields = ['config_key', 'description']
    
    readonly_fields = ['created_at', 'updated_at', 'formatted_config_value']
    
    fieldsets = (
        ('Configuration', {
            'fields': ('config_key', 'formatted_config_value', 'description')
        }),
        ('Settings', {
            'fields': ('is_active',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def config_value_preview(self, obj):
        """Display truncated value preview"""
        value_str = str(obj.config_value)
        if len(value_str) > 50:
            return value_str[:50] + "..."
        return value_str
    config_value_preview.short_description = "Value"
    
    def formatted_config_value(self, obj):
        """Display config value in formatted JSON"""
        if obj.config_value:
            return format_html('<pre>{}</pre>', json.dumps(obj.config_value, indent=2))
        return "No value"
    formatted_config_value.short_description = "Configuration Value"


@admin.register(JobDependency)
class JobDependencyAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for JobDependency model
    """
    
    list_display = [
        'dependent_job_short', 'prerequisite_job_short', 'dependency_type', 'created_at'
    ]
    
    list_filter = ['dependency_type', 'created_at']
    
    search_fields = [
        'dependent_job__id', 'prerequisite_job__id'
    ]
    
    readonly_fields = ['created_at', 'formatted_condition_data']
    
    fieldsets = (
        ('Dependency Relationship', {
            'fields': ('dependent_job', 'prerequisite_job', 'dependency_type')
        }),
        ('Conditions', {
            'fields': ('formatted_condition_data',),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    
    def dependent_job_short(self, obj):
        """Display dependent job ID shortened"""
        return str(obj.dependent_job.id)[:8] + "..."
    dependent_job_short.short_description = "Dependent Job"
    
    def prerequisite_job_short(self, obj):
        """Display prerequisite job ID shortened"""
        return str(obj.prerequisite_job.id)[:8] + "..."
    prerequisite_job_short.short_description = "Prerequisite Job"
    
    def formatted_condition_data(self, obj):
        """Display condition data in formatted JSON"""
        if obj.condition_data:
            return format_html('<pre>{}</pre>', json.dumps(obj.condition_data, indent=2))
        return "No condition data"
    formatted_condition_data.short_description = "Condition Data"


# Customize admin site header and titles
admin.site.site_header = "PhotoFish Queue System Administration"
admin.site.site_title = "Queue Admin"
admin.site.index_title = "Queue System Management"

# Add custom admin dashboard stats (optional)
class QueueSystemAdminSite(admin.AdminSite):
    """Custom admin site with enhanced dashboard"""
    
    def index(self, request, extra_context=None):
        """Enhanced admin dashboard with queue statistics"""
        extra_context = extra_context or {}
        
        try:
            # Get basic queue statistics
            from django.db.models import Count, Q
            
            # Job statistics
            job_stats = QueueJob.objects.aggregate(
                total_jobs=Count('id'),
                pending_jobs=Count('id', filter=Q(status='PENDING')),
                processing_jobs=Count('id', filter=Q(status='PROCESSING')),
                completed_jobs=Count('id', filter=Q(status='COMPLETED')),
                failed_jobs=Count('id', filter=Q(status='FAILED'))
            )
            
            # Worker statistics
            worker_stats = WorkerStatus.objects.aggregate(
                total_workers=Count('id'),
                active_workers=Count('id', filter=Q(status='BUSY')),
                idle_workers=Count('id', filter=Q(status='IDLE')),
                error_workers=Count('id', filter=Q(status='ERROR'))
            )
            
            # Error statistics
            error_stats = ErrorLog.objects.aggregate(
                total_errors=Count('id'),
                unresolved_errors=Count('id', filter=Q(is_resolved=False)),
                critical_errors=Count('id', filter=Q(severity='CRITICAL'))
            )
            
            extra_context.update({
                'queue_stats': {
                    'jobs': job_stats,
                    'workers': worker_stats,
                    'errors': error_stats
                }
            })
            
        except Exception as e:
            # If stats collection fails, don't break the admin
            extra_context['stats_error'] = str(e)
        
        return super().index(request, extra_context)

# You can uncomment this line to use the enhanced admin site
# admin_site = QueueSystemAdminSite(name='queue_admin')