"""
PhotoFish Enhanced Queue System - Error Management Package
Provides comprehensive error handling, recovery, and resilience capabilities.
"""

from .retry_manager import <PERSON>tryManager, RetryPolicy, RetryStrategy
from .error_classifier import ErrorClassifier, ErrorCategory, ErrorSeverity
from .recovery_service import RecoveryService, RecoveryStrategy, RecoveryAction
from .circuit_breaker import CircuitBreaker, CircuitState, CircuitBreakerConfig

import logging

logger = logging.getLogger(__name__)

class ErrorManagementSystem:
    """
    Centralized error management system that coordinates all error handling components
    """
    
    def __init__(self):
        self.retry_manager = RetryManager()
        self.error_classifier = ErrorClassifier()
        self.recovery_service = RecoveryService()
        self.circuit_breakers = {}
        
        logger.info("Error Management System initialized")
    
    def handle_error(self, error: Exception, context: dict = None) -> dict:
        """
        Centralized error handling pipeline
        
        Args:
            error: The exception that occurred
            context: Additional context about the error
            
        Returns:
            Error handling result
        """
        try:
            # Classify the error
            classification = self.error_classifier.classify_error(error, context)
            
            # Determine if retry is appropriate
            should_retry = self.retry_manager.should_retry(error, context)
            
            # Attempt recovery if needed
            recovery_result = None
            if classification['severity'] in ['critical', 'high']:
                recovery_result = self.recovery_service.attempt_recovery(error, context)
            
            return {
                'error_classification': classification,
                'retry_recommended': should_retry,
                'recovery_attempted': recovery_result is not None,
                'recovery_result': recovery_result,
                'timestamp': self.error_classifier._get_current_timestamp()
            }
            
        except Exception as handling_error:
            logger.error(f"Error in error handling pipeline: {str(handling_error)}")
            return {'error': 'Error handling failed', 'original_error': str(error)}
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(service_name)
        return self.circuit_breakers[service_name]
    
    def execute_with_protection(self, func, service_name: str, *args, **kwargs):
        """Execute function with full error protection"""
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        try:
            return circuit_breaker.execute(func, *args, **kwargs)
        except Exception as e:
            return self.handle_error(e, {'service': service_name, 'function': func.__name__})

def initialize_error_management():
    """Initialize error management system"""
    try:
        error_system = ErrorManagementSystem()
        return {
            'status': 'initialized',
            'error_system': error_system
        }
    except Exception as e:
        logger.error(f"Failed to initialize error management: {str(e)}")
        return {
            'status': 'error',
            'error': str(e)
        }

__all__ = [
    'RetryManager',
    'RetryPolicy', 
    'RetryStrategy',
    'ErrorClassifier',
    'ErrorCategory',
    'ErrorSeverity',
    'RecoveryService',
    'RecoveryStrategy',
    'RecoveryAction',
    'CircuitBreaker',
    'CircuitState',
    'CircuitBreakerConfig',
    'ErrorManagementSystem',
    'initialize_error_management'
]