# queue_system/error_management/circuit_breaker.py
"""
Circuit Breaker Pattern Implementation
Prevents cascading failures by temporarily stopping calls to failing services
"""

import threading
import time
import logging
from typing import Dict, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from django.utils import timezone
from django.core.cache import cache

logger = logging.getLogger('queue_system.circuit_breaker')

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5
    recovery_timeout: int = 60  # seconds
    success_threshold: int = 3  # for half-open state
    timeout: int = 30  # call timeout in seconds
    expected_exception: type = Exception

class CircuitBreakerStats:
    """Circuit breaker statistics"""
    
    def __init__(self):
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.last_failure_time = None
        self.consecutive_failures = 0
        self.consecutive_successes = 0
        self.state_changes = 0
        self.created_at = timezone.now()
    
    @property
    def failure_rate(self) -> float:
        """Calculate failure rate percentage"""
        if self.total_calls == 0:
            return 0.0
        return (self.failed_calls / self.total_calls) * 100
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage"""
        return 100.0 - self.failure_rate

class CircuitBreaker:
    """
    Circuit breaker implementation for fault tolerance
    Protects services from cascading failures
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitState.CLOSED
        self.stats = CircuitBreakerStats()
        
        # State management
        self.last_failure_time = None
        self.next_attempt_time = None
        self._lock = threading.RLock()
        
        logger.info(f"⚡ Circuit breaker '{name}' initialized")
    
    def __call__(self, func: Callable) -> Callable:
        """Decorator usage"""
        def wrapper(*args, **kwargs):
            return self.call(func, *args, **kwargs)
        return wrapper
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Call function through circuit breaker
        
        Args:
            func: Function to call
            *args, **kwargs: Function arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenException: When circuit is open
            Original exception: When function fails
        """
        with self._lock:
            self.stats.total_calls += 1
            
            # Check circuit state
            current_state = self._get_current_state()
            
            if current_state == CircuitState.OPEN:
                logger.warning(f"🔴 Circuit breaker '{self.name}' is OPEN - blocking call")
                raise CircuitBreakerOpenException(f"Circuit breaker '{self.name}' is open")
            
            try:
                # Execute the function with timeout
                start_time = time.time()
                result = self._execute_with_timeout(func, *args, **kwargs)
                execution_time = time.time() - start_time
                
                # Record success
                self._record_success(execution_time)
                
                return result
                
            except self.config.expected_exception as e:
                # Record failure
                self._record_failure(e)
                raise
            except Exception as e:
                # Unexpected exception - also record as failure
                self._record_failure(e)
                raise
    
    def _execute_with_timeout(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with timeout"""
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError(f"Function call timed out after {self.config.timeout} seconds")
        
        try:
            # Set timeout if supported (Unix systems)
            if hasattr(signal, 'SIGALRM'):
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(self.config.timeout)
            
            result = func(*args, **kwargs)
            
            # Clear timeout
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)
            
            return result
            
        except Exception as e:
            # Clear timeout on exception
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)
            raise
    
    def _get_current_state(self) -> CircuitState:
        """Get current circuit state, updating if necessary"""
        now = timezone.now()
        
        if self.state == CircuitState.OPEN:
            # Check if we should transition to half-open
            if (self.next_attempt_time and now >= self.next_attempt_time):
                self._transition_to_half_open()
        
        return self.state
    
    def _record_success(self, execution_time: float):
        """Record successful function execution"""
        self.stats.successful_calls += 1
        self.stats.consecutive_successes += 1
        self.stats.consecutive_failures = 0
        
        # State transitions based on success
        if self.state == CircuitState.HALF_OPEN:
            if self.stats.consecutive_successes >= self.config.success_threshold:
                self._transition_to_closed()
        
        logger.debug(f"✅ Circuit breaker '{self.name}' recorded success (time: {execution_time:.3f}s)")
    
    def _record_failure(self, exception: Exception):
        """Record failed function execution"""
        self.stats.failed_calls += 1
        self.stats.consecutive_failures += 1
        self.stats.consecutive_successes = 0
        self.stats.last_failure_time = timezone.now()
        
        # State transitions based on failure
        if (self.state == CircuitState.CLOSED and 
            self.stats.consecutive_failures >= self.config.failure_threshold):
            self._transition_to_open()
        elif self.state == CircuitState.HALF_OPEN:
            self._transition_to_open()
        
        logger.warning(f"❌ Circuit breaker '{self.name}' recorded failure: {str(exception)}")
    
    def _transition_to_open(self):
        """Transition circuit to OPEN state"""
        self.state = CircuitState.OPEN
        self.next_attempt_time = timezone.now() + timedelta(seconds=self.config.recovery_timeout)
        self.stats.state_changes += 1
        
        logger.warning(f"🔴 Circuit breaker '{self.name}' transitioned to OPEN state")
        self._cache_state_change('OPEN')
    
    def _transition_to_half_open(self):
        """Transition circuit to HALF_OPEN state"""
        self.state = CircuitState.HALF_OPEN
        self.stats.consecutive_successes = 0
        self.stats.state_changes += 1
        
        logger.info(f"🟡 Circuit breaker '{self.name}' transitioned to HALF_OPEN state")
        self._cache_state_change('HALF_OPEN')
    
    def _transition_to_closed(self):
        """Transition circuit to CLOSED state"""
        self.state = CircuitState.CLOSED
        self.stats.consecutive_failures = 0
        self.next_attempt_time = None
        self.stats.state_changes += 1
        
        logger.info(f"🟢 Circuit breaker '{self.name}' transitioned to CLOSED state")
        self._cache_state_change('CLOSED')
    
    def _cache_state_change(self, new_state: str):
        """Cache state change for monitoring"""
        try:
            cache_key = f"circuit_breaker_{self.name}_state"
            state_data = {
                'state': new_state,
                'timestamp': timezone.now().isoformat(),
                'consecutive_failures': self.stats.consecutive_failures,
                'consecutive_successes': self.stats.consecutive_successes,
                'total_calls': self.stats.total_calls
            }
            cache.set(cache_key, state_data, timeout=3600)
        except Exception as e:
            logger.error(f"❌ Error caching state change: {str(e)}")
    
    def reset(self):
        """Reset circuit breaker to initial state"""
        with self._lock:
            self.state = CircuitState.CLOSED
            self.stats = CircuitBreakerStats()
            self.next_attempt_time = None
            
            logger.info(f"🔄 Circuit breaker '{self.name}' reset")
    
    def force_open(self):
        """Force circuit breaker to OPEN state"""
        with self._lock:
            self._transition_to_open()
            logger.warning(f"⚠️ Circuit breaker '{self.name}' forced to OPEN state")
    
    def force_closed(self):
        """Force circuit breaker to CLOSED state"""
        with self._lock:
            self._transition_to_closed()
            logger.info(f"🔧 Circuit breaker '{self.name}' forced to CLOSED state")
    
    def get_stats(self) -> Dict:
        """Get circuit breaker statistics"""
        with self._lock:
            uptime = (timezone.now() - self.stats.created_at).total_seconds()
            
            return {
                'name': self.name,
                'state': self.state.value,
                'total_calls': self.stats.total_calls,
                'successful_calls': self.stats.successful_calls,
                'failed_calls': self.stats.failed_calls,
                'consecutive_failures': self.stats.consecutive_failures,
                'consecutive_successes': self.stats.consecutive_successes,
                'failure_rate': self.stats.failure_rate,
                'success_rate': self.stats.success_rate,
                'state_changes': self.stats.state_changes,
                'last_failure_time': self.stats.last_failure_time.isoformat() if self.stats.last_failure_time else None,
                'next_attempt_time': self.next_attempt_time.isoformat() if self.next_attempt_time else None,
                'uptime_seconds': uptime,
                'config': {
                    'failure_threshold': self.config.failure_threshold,
                    'recovery_timeout': self.config.recovery_timeout,
                    'success_threshold': self.config.success_threshold,
                    'timeout': self.config.timeout
                }
            }

class CircuitBreakerManager:
    """
    Manages multiple circuit breakers
    Provides centralized configuration and monitoring
    """
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._lock = threading.RLock()
        
        logger.info("⚡ Circuit Breaker Manager initialized")
    
    def get_circuit_breaker(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """
        Get or create circuit breaker
        
        Args:
            name: Circuit breaker name
            config: Configuration (uses default if not provided)
            
        Returns:
            CircuitBreaker instance
        """
        with self._lock:
            if name not in self.circuit_breakers:
                if config is None:
                    config = CircuitBreakerConfig()
                
                self.circuit_breakers[name] = CircuitBreaker(name, config)
                logger.info(f"🆕 Created circuit breaker: {name}")
            
            return self.circuit_breakers[name]
    
    def remove_circuit_breaker(self, name: str):
        """Remove circuit breaker"""
        with self._lock:
            if name in self.circuit_breakers:
                del self.circuit_breakers[name]
                logger.info(f"🗑️ Removed circuit breaker: {name}")
    
    def get_all_stats(self) -> Dict[str, Dict]:
        """Get statistics for all circuit breakers"""
        with self._lock:
            return {
                name: breaker.get_stats()
                for name, breaker in self.circuit_breakers.items()
            }
    
    def reset_all(self):
        """Reset all circuit breakers"""
        with self._lock:
            for breaker in self.circuit_breakers.values():
                breaker.reset()
            logger.info("🔄 Reset all circuit breakers")
    
    def get_summary(self) -> Dict:
        """Get summary of all circuit breakers"""
        with self._lock:
            total_breakers = len(self.circuit_breakers)
            open_breakers = sum(1 for b in self.circuit_breakers.values() if b.state == CircuitState.OPEN)
            half_open_breakers = sum(1 for b in self.circuit_breakers.values() if b.state == CircuitState.HALF_OPEN)
            closed_breakers = total_breakers - open_breakers - half_open_breakers
            
            return {
                'total_circuit_breakers': total_breakers,
                'closed_breakers': closed_breakers,
                'half_open_breakers': half_open_breakers,
                'open_breakers': open_breakers,
                'health_percentage': (closed_breakers / total_breakers * 100) if total_breakers > 0 else 100,
                'breaker_names': list(self.circuit_breakers.keys())
            }

# Specific circuit breakers for queue system services

class AWSCircuitBreaker(CircuitBreaker):
    """Circuit breaker specifically for AWS services"""
    
    def __init__(self, service_name: str):
        config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=120,  # Longer timeout for AWS
            success_threshold=2,
            timeout=60,
            expected_exception=Exception  # AWS exceptions vary
        )
        super().__init__(f"aws_{service_name}", config)

class DatabaseCircuitBreaker(CircuitBreaker):
    """Circuit breaker specifically for database operations"""
    
    def __init__(self):
        config = CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=30,
            success_threshold=3,
            timeout=30,
            expected_exception=Exception
        )
        super().__init__("database", config)

class CacheCircuitBreaker(CircuitBreaker):
    """Circuit breaker specifically for cache operations"""
    
    def __init__(self):
        config = CircuitBreakerConfig(
            failure_threshold=10,  # Cache can tolerate more failures
            recovery_timeout=60,
            success_threshold=5,
            timeout=10,
            expected_exception=Exception
        )
        super().__init__("cache", config)

# Exceptions
class CircuitBreakerOpenException(Exception):
    """Raised when circuit breaker is open"""
    pass

class CircuitBreakerTimeoutException(Exception):
    """Raised when circuit breaker call times out"""
    pass

# Global circuit breaker manager
circuit_breaker_manager = CircuitBreakerManager()

# Convenience functions
def circuit_breaker(name: str, config: Optional[CircuitBreakerConfig] = None):
    """Decorator for circuit breaker protection"""
    def decorator(func):
        breaker = circuit_breaker_manager.get_circuit_breaker(name, config)
        return breaker(func)
    return decorator

def get_aws_circuit_breaker(service_name: str) -> AWSCircuitBreaker:
    """Get AWS-specific circuit breaker"""
    breaker_name = f"aws_{service_name}"
    if breaker_name not in circuit_breaker_manager.circuit_breakers:
        circuit_breaker_manager.circuit_breakers[breaker_name] = AWSCircuitBreaker(service_name)
    return circuit_breaker_manager.circuit_breakers[breaker_name]

def get_database_circuit_breaker() -> DatabaseCircuitBreaker:
    """Get database-specific circuit breaker"""
    if "database" not in circuit_breaker_manager.circuit_breakers:
        circuit_breaker_manager.circuit_breakers["database"] = DatabaseCircuitBreaker()
    return circuit_breaker_manager.circuit_breakers["database"]

def get_cache_circuit_breaker() -> CacheCircuitBreaker:
    """Get cache-specific circuit breaker"""
    if "cache" not in circuit_breaker_manager.circuit_breakers:
        circuit_breaker_manager.circuit_breakers["cache"] = CacheCircuitBreaker()
    return circuit_breaker_manager.circuit_breakers["cache"]