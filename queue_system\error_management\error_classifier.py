"""
PhotoFish Enhanced Queue System - Error Classifier
Classifies and categorizes different types of errors for intelligent handling.
"""

import re
import logging
from enum import Enum
from typing import Dict, List, Optional, Any, Type
from datetime import datetime, timedelta
from collections import defaultdict, deque

from django.core.exceptions import ValidationError
from django.db import DatabaseError, IntegrityError, OperationalError
from django.core.cache import cache

logger = logging.getLogger(__name__)

class ErrorCategory(Enum):
    """Error categories for classification"""
    SYSTEM = "system"
    DATABASE = "database"
    NETWORK = "network"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"

class ErrorSeverity(Enum):
    """Error severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

class ErrorPattern:
    """Represents an error pattern for classification"""
    
    def __init__(self, category: ErrorCategory, severity: ErrorSeverity, 
                 patterns: List[str], exception_types: List[Type] = None,
                 recovery_strategies: List[str] = None):
        self.category = category
        self.severity = severity
        self.patterns = patterns
        self.exception_types = exception_types or []
        self.recovery_strategies = recovery_strategies or []

class ErrorClassifier:
    """
    Intelligent error classification system that categorizes errors
    and determines appropriate handling strategies
    """
    
    def __init__(self):
        self.error_patterns = self._initialize_error_patterns()
        self.error_history = deque(maxlen=1000)
        self.pattern_statistics = defaultdict(int)
        
        # Error frequency tracking for pattern learning
        self.error_frequency = defaultdict(lambda: deque(maxlen=100))
        
        logger.info("Error Classifier initialized with {} patterns".format(len(self.error_patterns)))
    
    def classify_error(self, error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Classify an error and determine its category, severity, and handling strategy
        
        Args:
            error: The exception that occurred
            context: Additional context about the error
            
        Returns:
            Classification result dictionary
        """
        context = context or {}
        error_message = str(error)
        error_type = type(error)
        
        # Record error occurrence
        timestamp = self._get_current_timestamp()
        self._record_error_occurrence(error_type, error_message, timestamp)
        
        # Find matching pattern
        matched_pattern = self._find_matching_pattern(error, error_message, error_type)
        
        if matched_pattern:
            category = matched_pattern.category
            severity = matched_pattern.severity
            recovery_strategies = matched_pattern.recovery_strategies
        else:
            # Use heuristic classification for unknown errors
            category, severity = self._heuristic_classification(error, error_message, context)
            recovery_strategies = self._suggest_recovery_strategies(category, severity)
        
        # Analyze error frequency and patterns
        frequency_analysis = self._analyze_error_frequency(error_type, error_message)
        
        # Determine if this is a recurring issue
        is_recurring = frequency_analysis['recent_occurrences'] > 3
        
        # Create classification result
        classification = {
            'category': category.value,
            'severity': severity.value,
            'error_type': error_type.__name__,
            'error_message': error_message,
            'recovery_strategies': recovery_strategies,
            'is_recurring': is_recurring,
            'frequency_analysis': frequency_analysis,
            'context': context,
            'timestamp': timestamp,
            'classification_confidence': self._calculate_confidence(matched_pattern),
            'recommended_actions': self._recommend_actions(category, severity, is_recurring)
        }
        
        # Store in error history
        self.error_history.append(classification)
        
        # Update pattern statistics
        if matched_pattern:
            pattern_key = f"{category.value}_{severity.value}"
            self.pattern_statistics[pattern_key] += 1
        
        logger.info(f"Classified error: {category.value}/{severity.value} - {error_type.__name__}")
        
        return classification
    
    def get_error_trends(self, hours: int = 24) -> Dict[str, Any]:
        """
        Analyze error trends over specified time period
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Error trend analysis
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_errors = [
            error for error in self.error_history
            if self._parse_timestamp(error['timestamp']) >= cutoff_time
        ]
        
        if not recent_errors:
            return {'message': 'No errors in specified time period'}
        
        # Analyze trends
        category_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        error_type_counts = defaultdict(int)
        hourly_distribution = defaultdict(int)
        
        for error in recent_errors:
            category_counts[error['category']] += 1
            severity_counts[error['severity']] += 1
            error_type_counts[error['error_type']] += 1
            
            # Calculate hour bucket
            error_time = self._parse_timestamp(error['timestamp'])
            hour_bucket = error_time.replace(minute=0, second=0, microsecond=0)
            hourly_distribution[hour_bucket.isoformat()] += 1
        
        # Find most common patterns
        top_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        top_error_types = sorted(error_type_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'time_period_hours': hours,
            'total_errors': len(recent_errors),
            'category_distribution': dict(category_counts),
            'severity_distribution': dict(severity_counts),
            'top_categories': top_categories,
            'top_error_types': top_error_types,
            'hourly_distribution': dict(hourly_distribution),
            'recurring_issues': self._identify_recurring_issues(recent_errors)
        }
    
    def suggest_preventive_measures(self, category: str = None) -> List[str]:
        """
        Suggest preventive measures based on error analysis
        
        Args:
            category: Specific error category to analyze (optional)
            
        Returns:
            List of preventive measure suggestions
        """
        suggestions = []
        
        # Analyze recent error patterns
        recent_errors = list(self.error_history)[-100:]  # Last 100 errors
        
        if category:
            recent_errors = [e for e in recent_errors if e['category'] == category]
        
        category_counts = defaultdict(int)
        for error in recent_errors:
            category_counts[error['category']] += 1
        
        # Generate suggestions based on most common error categories
        for cat, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
            if count >= 5:  # If category appears 5+ times
                suggestions.extend(self._get_preventive_measures_for_category(cat))
        
        return list(set(suggestions))  # Remove duplicates
    
    def _initialize_error_patterns(self) -> List[ErrorPattern]:
        """Initialize predefined error patterns"""
        patterns = [
            # Database Errors
            ErrorPattern(
                category=ErrorCategory.DATABASE,
                severity=ErrorSeverity.CRITICAL,
                patterns=[
                    r"connection.*refused",
                    r"database.*unavailable",
                    r"connection.*timeout",
                    r"max_connections.*exceeded"
                ],
                exception_types=[DatabaseError, OperationalError],
                recovery_strategies=["retry_with_backoff", "use_connection_pool", "database_failover"]
            ),
            
            # Network Errors
            ErrorPattern(
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.HIGH,
                patterns=[
                    r"connection.*timeout",
                    r"network.*unreachable",
                    r"dns.*resolution.*failed",
                    r"ssl.*handshake.*failed"
                ],
                recovery_strategies=["retry_with_exponential_backoff", "use_alternative_endpoint"]
            ),
            
            # AWS/External Service Errors
            ErrorPattern(
                category=ErrorCategory.EXTERNAL_SERVICE,
                severity=ErrorSeverity.HIGH,
                patterns=[
                    r"aws.*throttling",
                    r"rate.*limit.*exceeded",
                    r"service.*unavailable",
                    r"internal.*server.*error"
                ],
                recovery_strategies=["exponential_backoff", "circuit_breaker", "fallback_service"]
            ),
            
            # Authentication Errors
            ErrorPattern(
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.MEDIUM,
                patterns=[
                    r"authentication.*failed",
                    r"invalid.*credentials",
                    r"token.*expired",
                    r"unauthorized.*access"
                ],
                recovery_strategies=["refresh_token", "re_authenticate", "fallback_auth"]
            ),
            
            # Validation Errors
            ErrorPattern(
                category=ErrorCategory.VALIDATION,
                severity=ErrorSeverity.LOW,
                patterns=[
                    r"validation.*error",
                    r"invalid.*input",
                    r"required.*field.*missing",
                    r"format.*invalid"
                ],
                exception_types=[ValidationError, ValueError],
                recovery_strategies=["input_sanitization", "default_values", "user_notification"]
            ),
            
            # Resource Errors
            ErrorPattern(
                category=ErrorCategory.RESOURCE,
                severity=ErrorSeverity.HIGH,
                patterns=[
                    r"out.*of.*memory",
                    r"disk.*space.*full",
                    r"resource.*exhausted",
                    r"file.*not.*found"
                ],
                exception_types=[MemoryError, FileNotFoundError],
                recovery_strategies=["cleanup_resources", "increase_limits", "alternative_storage"]
            ),
            
            # Business Logic Errors
            ErrorPattern(
                category=ErrorCategory.BUSINESS_LOGIC,
                severity=ErrorSeverity.MEDIUM,
                patterns=[
                    r"business.*rule.*violation",
                    r"invalid.*state.*transition",
                    r"constraint.*violation",
                    r"duplicate.*entry"
                ],
                exception_types=[IntegrityError],
                recovery_strategies=["state_rollback", "conflict_resolution", "user_notification"]
            ),
            
            # System Errors
            ErrorPattern(
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.CRITICAL,
                patterns=[
                    r"system.*error",
                    r"kernel.*panic",
                    r"segmentation.*fault",
                    r"stack.*overflow"
                ],
                exception_types=[SystemError, RecursionError],
                recovery_strategies=["system_restart", "emergency_shutdown", "escalate_to_admin"]
            )
        ]
        
        return patterns
    
    def _find_matching_pattern(self, error: Exception, error_message: str, 
                             error_type: Type) -> Optional[ErrorPattern]:
        """Find matching error pattern"""
        for pattern in self.error_patterns:
            # Check exception type match
            if error_type in pattern.exception_types:
                return pattern
            
            # Check message pattern match
            for regex_pattern in pattern.patterns:
                if re.search(regex_pattern, error_message, re.IGNORECASE):
                    return pattern
        
        return None
    
    def _heuristic_classification(self, error: Exception, error_message: str, 
                                context: Dict[str, Any]) -> tuple:
        """Heuristic classification for unknown errors"""
        error_type = type(error)
        
        # Database-related heuristics
        if any(keyword in error_message.lower() for keyword in ['database', 'sql', 'table', 'column']):
            return ErrorCategory.DATABASE, ErrorSeverity.HIGH
        
        # Network-related heuristics
        if any(keyword in error_message.lower() for keyword in ['connection', 'network', 'timeout', 'dns']):
            return ErrorCategory.NETWORK, ErrorSeverity.MEDIUM
        
        # Authentication heuristics
        if any(keyword in error_message.lower() for keyword in ['auth', 'login', 'permission', 'unauthorized']):
            return ErrorCategory.AUTHENTICATION, ErrorSeverity.MEDIUM
        
        # Validation heuristics
        if isinstance(error, (ValueError, ValidationError)):
            return ErrorCategory.VALIDATION, ErrorSeverity.LOW
        
        # System heuristics
        if isinstance(error, (SystemError, MemoryError)):
            return ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL
        
        # Default classification
        return ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM
    
    def _suggest_recovery_strategies(self, category: ErrorCategory, 
                                   severity: ErrorSeverity) -> List[str]:
        """Suggest recovery strategies based on category and severity"""
        strategies = {
            ErrorCategory.DATABASE: ["retry_with_backoff", "connection_pool", "read_replica"],
            ErrorCategory.NETWORK: ["exponential_backoff", "alternative_endpoint", "circuit_breaker"],
            ErrorCategory.EXTERNAL_SERVICE: ["circuit_breaker", "fallback_service", "rate_limiting"],
            ErrorCategory.AUTHENTICATION: ["token_refresh", "re_authentication", "fallback_auth"],
            ErrorCategory.VALIDATION: ["input_sanitization", "default_values", "user_feedback"],
            ErrorCategory.RESOURCE: ["cleanup", "scaling", "alternative_resources"],
            ErrorCategory.SYSTEM: ["restart", "failover", "escalation"]
        }
        
        return strategies.get(category, ["manual_intervention", "logging", "monitoring"])
    
    def _record_error_occurrence(self, error_type: Type, error_message: str, timestamp: str):
        """Record error occurrence for frequency analysis"""
        error_key = f"{error_type.__name__}:{hash(error_message) % 10000}"
        self.error_frequency[error_key].append(timestamp)
    
    def _analyze_error_frequency(self, error_type: Type, error_message: str) -> Dict[str, Any]:
        """Analyze error frequency patterns"""
        error_key = f"{error_type.__name__}:{hash(error_message) % 10000}"
        occurrences = list(self.error_frequency[error_key])
        
        if not occurrences:
            return {'recent_occurrences': 0, 'frequency_trend': 'none'}
        
        # Count recent occurrences (last hour)
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent_count = sum(
            1 for timestamp in occurrences
            if self._parse_timestamp(timestamp) >= one_hour_ago
        )
        
        # Determine frequency trend
        if len(occurrences) >= 3:
            recent_avg = recent_count
            older_avg = len(occurrences) - recent_count
            
            if recent_avg > older_avg * 1.5:
                trend = 'increasing'
            elif recent_avg < older_avg * 0.5:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'insufficient_data'
        
        return {
            'total_occurrences': len(occurrences),
            'recent_occurrences': recent_count,
            'frequency_trend': trend,
            'first_occurrence': occurrences[0] if occurrences else None,
            'last_occurrence': occurrences[-1] if occurrences else None
        }
    
    def _calculate_confidence(self, matched_pattern: Optional[ErrorPattern]) -> float:
        """Calculate classification confidence"""
        if matched_pattern:
            return 0.9  # High confidence for pattern matches
        else:
            return 0.6  # Medium confidence for heuristic classification
    
    def _recommend_actions(self, category: ErrorCategory, severity: ErrorSeverity, 
                         is_recurring: bool) -> List[str]:
        """Recommend specific actions based on classification"""
        actions = []
        
        if severity == ErrorSeverity.CRITICAL:
            actions.append("immediate_escalation")
            actions.append("emergency_response")
        
        if is_recurring:
            actions.append("pattern_analysis")
            actions.append("preventive_measures")
        
        if category == ErrorCategory.DATABASE:
            actions.append("database_health_check")
        elif category == ErrorCategory.NETWORK:
            actions.append("network_diagnostics")
        elif category == ErrorCategory.EXTERNAL_SERVICE:
            actions.append("service_status_check")
        
        actions.append("detailed_logging")
        actions.append("monitoring_alert")
        
        return actions
    
    def _identify_recurring_issues(self, errors: List[Dict]) -> List[Dict[str, Any]]:
        """Identify recurring error patterns"""
        error_groups = defaultdict(list)
        
        for error in errors:
            key = f"{error['category']}_{error['error_type']}"
            error_groups[key].append(error)
        
        recurring_issues = []
        for group_key, group_errors in error_groups.items():
            if len(group_errors) >= 3:  # 3+ occurrences = recurring
                recurring_issues.append({
                    'pattern': group_key,
                    'occurrences': len(group_errors),
                    'first_seen': min(e['timestamp'] for e in group_errors),
                    'last_seen': max(e['timestamp'] for e in group_errors),
                    'category': group_errors[0]['category'],
                    'severity': group_errors[0]['severity']
                })
        
        return sorted(recurring_issues, key=lambda x: x['occurrences'], reverse=True)
    
    def _get_preventive_measures_for_category(self, category: str) -> List[str]:
        """Get preventive measures for specific error category"""
        measures = {
            'database': [
                "Implement connection pooling",
                "Add database monitoring and alerting",
                "Set up read replicas for high availability",
                "Optimize slow queries"
            ],
            'network': [
                "Implement retry logic with exponential backoff",
                "Set up network monitoring",
                "Use multiple endpoints for redundancy",
                "Configure proper timeouts"
            ],
            'external_service': [
                "Implement circuit breaker pattern",
                "Set up service health monitoring",
                "Create fallback mechanisms",
                "Implement rate limiting"
            ],
            'authentication': [
                "Implement token refresh mechanisms",
                "Set up authentication monitoring",
                "Create backup authentication methods",
                "Add session management"
            ],
            'validation': [
                "Improve input validation",
                "Add comprehensive error messages",
                "Implement data sanitization",
                "Create validation schemas"
            ]
        }
        
        return measures.get(category, ["Implement comprehensive monitoring", "Add detailed logging"])
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp as string"""
        return datetime.now().isoformat()
    
    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp string to datetime object"""
        try:
            return datetime.fromisoformat(timestamp_str)
        except ValueError:
            return datetime.now()