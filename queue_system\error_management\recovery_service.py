# queue_system/error_management/recovery_service.py
"""
Recovery Service for Queue System
Automatic error recovery and system healing capabilities
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

from django.utils import timezone
from django.core.cache import cache
from django.db import transaction

from ..models import Que<PERSON><PERSON><PERSON>, WorkerStatus, <PERSON>rrorLog
from .error_classifier import E<PERSON>r<PERSON>lassifier, <PERSON>rror<PERSON>ategory, ErrorSeverity, get_error_classifier
from .retry_manager import RetryManager, get_retry_manager

logger = logging.getLogger('queue_system.recovery_service')

class RecoveryAction(Enum):
    """Types of recovery actions"""
    RESTART_WORKER = "restart_worker"
    CLEAR_QUEUE = "clear_queue"
    RESET_CONNECTION = "reset_connection"
    CLEANUP_MEMORY = "cleanup_memory"
    SCALE_WORKERS = "scale_workers"
    FAILOVER_SERVICE = "failover_service"
    ALERT_OPERATORS = "alert_operators"
    EMERGENCY_STOP = "emergency_stop"
    REPROCESS_JOBS = "reprocess_jobs"
    UPDATE_CONFIGURATION = "update_configuration"

@dataclass
class RecoveryPlan:
    """Recovery plan for specific error scenarios"""
    error_pattern: str
    recovery_actions: List[RecoveryAction]
    prerequisites: List[str]
    success_criteria: List[str]
    rollback_actions: List[RecoveryAction]
    max_attempts: int = 3
    cooldown_period: int = 300  # seconds

@dataclass
class RecoveryExecution:
    """Recovery execution record"""
    recovery_id: str
    error_type: str
    recovery_plan: RecoveryPlan
    start_time: datetime
    end_time: Optional[datetime]
    success: bool
    actions_executed: List[str]
    failure_reason: Optional[str]
    affected_jobs: List[str]

class RecoveryService:
    """
    Automatic recovery service for queue system errors
    Implements intelligent error recovery and system healing
    """
    
    def __init__(self):
        self.cache_prefix = 'recovery_service'
        
        # Dependencies
        self.error_classifier: Optional[ErrorClassifier] = None
        self.retry_manager: Optional[RetryManager] = None
        
        # Recovery plans registry
        self.recovery_plans: Dict[str, RecoveryPlan] = {}
        self._initialize_default_recovery_plans()
        
        # Recovery execution tracking
        self._active_recoveries: Dict[str, RecoveryExecution] = {}
        self._recovery_history: List[RecoveryExecution] = []
        
        # Monitoring and state
        self._monitoring_thread = None
        self._shutdown_event = threading.Event()
        self._is_running = False
        self._lock = threading.RLock()
        
        # Statistics
        self._stats = {
            'total_recoveries_attempted': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'automatic_recoveries': 0,
            'manual_recoveries': 0,
            'jobs_recovered': 0,
            'start_time': timezone.now()
        }
        
        logger.info("🚑 Recovery Service initialized")
    
    def set_dependencies(self, error_classifier: ErrorClassifier, retry_manager: RetryManager):
        """Set service dependencies"""
        self.error_classifier = error_classifier
        self.retry_manager = retry_manager
        logger.info("🔗 Recovery Service dependencies configured")
    
    def _initialize_default_recovery_plans(self):
        """Initialize default recovery plans"""
        
        # AWS Rate Limit Recovery
        self.recovery_plans['aws_rate_limit'] = RecoveryPlan(
            error_pattern='aws_rate_limit',
            recovery_actions=[
                RecoveryAction.CLEAR_QUEUE,
                RecoveryAction.SCALE_WORKERS,
                RecoveryAction.UPDATE_CONFIGURATION
            ],
            prerequisites=['Check AWS service status', 'Verify API quotas'],
            success_criteria=['Queue processing resumed', 'Error rate below 5%'],
            rollback_actions=[RecoveryAction.RESTART_WORKER],
            max_attempts=2,
            cooldown_period=600
        )
        
        # Database Connection Recovery
        self.recovery_plans['database_connection_error'] = RecoveryPlan(
            error_pattern='database_connection_error',
            recovery_actions=[
                RecoveryAction.RESET_CONNECTION,
                RecoveryAction.RESTART_WORKER,
                RecoveryAction.ALERT_OPERATORS
            ],
            prerequisites=['Check database server status'],
            success_criteria=['Database connectivity restored', 'Jobs processing normally'],
            rollback_actions=[RecoveryAction.EMERGENCY_STOP],
            max_attempts=3,
            cooldown_period=300
        )
        
        # Memory Error Recovery
        self.recovery_plans['memory_error'] = RecoveryPlan(
            error_pattern='memory_error',
            recovery_actions=[
                RecoveryAction.CLEANUP_MEMORY,
                RecoveryAction.RESTART_WORKER,
                RecoveryAction.SCALE_WORKERS
            ],
            prerequisites=['Check available system memory'],
            success_criteria=['Memory usage below 80%', 'Workers stable'],
            rollback_actions=[RecoveryAction.EMERGENCY_STOP],
            max_attempts=2,
            cooldown_period=180
        )
        
        # Worker Failure Recovery
        self.recovery_plans['worker_failure'] = RecoveryPlan(
            error_pattern='worker_failure',
            recovery_actions=[
                RecoveryAction.RESTART_WORKER,
                RecoveryAction.REPROCESS_JOBS,
                RecoveryAction.SCALE_WORKERS
            ],
            prerequisites=['Identify failed workers'],
            success_criteria=['All workers healthy', 'Queue processing resumed'],
            rollback_actions=[RecoveryAction.ALERT_OPERATORS],
            max_attempts=3,
            cooldown_period=120
        )
        
        # Network Error Recovery
        self.recovery_plans['network_error'] = RecoveryPlan(
            error_pattern='network_error',
            recovery_actions=[
                RecoveryAction.RESET_CONNECTION,
                RecoveryAction.RESTART_WORKER,
                RecoveryAction.FAILOVER_SERVICE
            ],
            prerequisites=['Check network connectivity'],
            success_criteria=['Network connectivity restored', 'Services accessible'],
            rollback_actions=[RecoveryAction.ALERT_OPERATORS],
            max_attempts=2,
            cooldown_period=300
        )
        
        logger.info(f"📋 Initialized {len(self.recovery_plans)} default recovery plans")
    
    def start_monitoring(self):
        """Start recovery monitoring"""
        if self._is_running:
            logger.warning("⚠️ Recovery monitoring already running")
            return
        
        # Initialize dependencies if not set
        if not self.error_classifier:
            self.error_classifier = get_error_classifier()
        if not self.retry_manager:
            self.retry_manager = get_retry_manager()
        
        self._monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            name="RecoveryMonitor",
            daemon=True
        )
        self._monitoring_thread.start()
        self._is_running = True
        
        logger.info("✅ Recovery monitoring started")
    
    def _monitoring_loop(self):
        """Main recovery monitoring loop"""
        while not self._shutdown_event.is_set():
            try:
                # Check for system issues requiring recovery
                self._check_system_health()
                
                # Monitor active recoveries
                self._monitor_active_recoveries()
                
                # Check for stuck jobs
                self._check_stuck_jobs()
                
                # Cleanup completed recoveries
                self._cleanup_recovery_history()
                
            except Exception as e:
                logger.error(f"❌ Error in recovery monitoring loop: {str(e)}")
            
            # Wait before next check
            self._shutdown_event.wait(30)  # Check every 30 seconds
    
    def _check_system_health(self):
        """Check system health and trigger recovery if needed"""
        try:
            # Check for critical alerts
            critical_alerts = QueueAlert.objects.filter(
                status='ACTIVE',
                severity='CRITICAL'
            )
            
            for alert in critical_alerts:
                self._handle_critical_alert(alert)
            
            # Check worker health
            self._check_worker_health()
            
            # Check queue health
            self._check_queue_health()
            
        except Exception as e:
            logger.error(f"❌ Error checking system health: {str(e)}")
    
    def _handle_critical_alert(self, alert: QueueAlert):
        """Handle critical system alert"""
        try:
            alert_type = alert.alert_type
            
            # Map alert types to recovery plans
            recovery_mapping = {
                'QUEUE_BACKLOG': 'worker_failure',
                'HIGH_ERROR_RATE': 'worker_failure',
                'WORKER_FAILURE': 'worker_failure',
                'RESOURCE_USAGE': 'memory_error',
                'PROCESSING_DELAY': 'worker_failure'
            }
            
            recovery_plan_key = recovery_mapping.get(alert_type)
            if recovery_plan_key and recovery_plan_key in self.recovery_plans:
                logger.warning(f"🚨 Triggering recovery for alert: {alert.title}")
                self.trigger_recovery(recovery_plan_key, f"Critical alert: {alert.title}")
                
                # Acknowledge the alert
                alert.acknowledge()
                
        except Exception as e:
            logger.error(f"❌ Error handling critical alert: {str(e)}")
    
    def _check_worker_health(self):
        """Check worker health and trigger recovery if needed"""
        try:
            # Check for dead workers
            threshold_time = timezone.now() - timedelta(minutes=5)
            dead_workers = WorkerInstance.objects.filter(
                last_heartbeat__lt=threshold_time,
                status__in=['ACTIVE', 'BUSY']
            )
            
            if dead_workers.count() > 0:
                logger.warning(f"⚠️ Found {dead_workers.count()} dead workers")
                self.trigger_recovery('worker_failure', f"Dead workers detected: {dead_workers.count()}")
            
            # Check for workers with high error rates
            high_error_workers = WorkerInstance.objects.filter(
                errors_count__gt=10,
                last_heartbeat__gte=threshold_time
            )
            
            if high_error_workers.count() > 2:
                logger.warning(f"⚠️ Found {high_error_workers.count()} workers with high error rates")
                self.trigger_recovery('worker_failure', f"High error rate workers: {high_error_workers.count()}")
                
        except Exception as e:
            logger.error(f"❌ Error checking worker health: {str(e)}")
    
    def _check_queue_health(self):
        """Check queue health and trigger recovery if needed"""
        try:
            # Check for very old pending jobs
            one_hour_ago = timezone.now() - timedelta(hours=1)
            old_pending_jobs = QueueJob.objects.filter(
                status='QUEUED',
                created_at__lt=one_hour_ago
            ).count()
            
            if old_pending_jobs > 100:
                logger.warning(f"⚠️ Found {old_pending_jobs} jobs pending for over 1 hour")
                self.trigger_recovery('worker_failure', f"Old pending jobs: {old_pending_jobs}")
            
            # Check for jobs stuck in processing
            stuck_jobs = QueueJob.objects.filter(
                status='PROCESSING',
                started_at__lt=one_hour_ago
            ).count()
            
            if stuck_jobs > 10:
                logger.warning(f"⚠️ Found {stuck_jobs} jobs stuck in processing")
                self.trigger_recovery('worker_failure', f"Stuck processing jobs: {stuck_jobs}")
                
        except Exception as e:
            logger.error(f"❌ Error checking queue health: {str(e)}")
    
    def _check_stuck_jobs(self):
        """Check for and recover stuck jobs"""
        try:
            # Find jobs stuck in processing for too long
            timeout_threshold = timezone.now() - timedelta(hours=2)
            stuck_jobs = QueueJob.objects.filter(
                status='PROCESSING',
                started_at__lt=timeout_threshold
            )
            
            for job in stuck_jobs:
                logger.warning(f"🔄 Recovering stuck job: {job.id}")
                self._recover_stuck_job(job)
                
        except Exception as e:
            logger.error(f"❌ Error checking stuck jobs: {str(e)}")
    
    def _recover_stuck_job(self, job: QueueJob):
        """Recover a specific stuck job"""
        try:
            with transaction.atomic():
                # Reset job status
                job.status = 'QUEUED'
                job.started_at = None
                job.worker_id = None
                job.attempts += 1
                job.save()
                
                # Log recovery
                logger.info(f"🔄 Reset stuck job {job.id} back to queue")
                self._stats['jobs_recovered'] += 1
                
        except Exception as e:
            logger.error(f"❌ Error recovering stuck job {job.id}: {str(e)}")
    
    def trigger_recovery(self, recovery_plan_key: str, reason: str, 
                        manual: bool = False) -> Optional[str]:
        """
        Trigger recovery execution
        
        Args:
            recovery_plan_key: Key of recovery plan to execute
            reason: Reason for triggering recovery
            manual: Whether this is a manual recovery
            
        Returns:
            Recovery ID if successful, None otherwise
        """
        try:
            if recovery_plan_key not in self.recovery_plans:
                logger.error(f"❌ Recovery plan not found: {recovery_plan_key}")
                return None
            
            recovery_plan = self.recovery_plans[recovery_plan_key]
            
            # Check if similar recovery is already running
            if self._is_recovery_in_progress(recovery_plan_key):
                logger.warning(f"⚠️ Recovery already in progress for: {recovery_plan_key}")
                return None
            
            # Check cooldown period
            if not self._check_cooldown(recovery_plan_key):
                logger.warning(f"⚠️ Recovery in cooldown period: {recovery_plan_key}")
                return None
            
            # Create recovery execution
            recovery_id = f"recovery_{recovery_plan_key}_{int(time.time())}"
            recovery_execution = RecoveryExecution(
                recovery_id=recovery_id,
                error_type=recovery_plan_key,
                recovery_plan=recovery_plan,
                start_time=timezone.now(),
                end_time=None,
                success=False,
                actions_executed=[],
                failure_reason=None,
                affected_jobs=[]
            )
            
            # Store active recovery
            with self._lock:
                self._active_recoveries[recovery_id] = recovery_execution
            
            # Execute recovery in background
            recovery_thread = threading.Thread(
                target=self._execute_recovery,
                args=(recovery_execution, reason),
                name=f"Recovery-{recovery_id}",
                daemon=True
            )
            recovery_thread.start()
            
            # Update statistics
            self._stats['total_recoveries_attempted'] += 1
            if manual:
                self._stats['manual_recoveries'] += 1
            else:
                self._stats['automatic_recoveries'] += 1
            
            logger.info(f"🚑 Started recovery: {recovery_id} for {recovery_plan_key}")
            return recovery_id
            
        except Exception as e:
            logger.error(f"❌ Error triggering recovery: {str(e)}")
            return None
    
    def _is_recovery_in_progress(self, recovery_plan_key: str) -> bool:
        """Check if recovery is already in progress for this plan"""
        with self._lock:
            return any(
                recovery.error_type == recovery_plan_key 
                for recovery in self._active_recoveries.values()
            )
    
    def _check_cooldown(self, recovery_plan_key: str) -> bool:
        """Check if recovery plan is in cooldown period"""
        try:
            recovery_plan = self.recovery_plans[recovery_plan_key]
            cooldown_period = recovery_plan.cooldown_period
            
            # Check recent recovery attempts
            cutoff_time = timezone.now() - timedelta(seconds=cooldown_period)
            
            recent_attempts = [
                recovery for recovery in self._recovery_history
                if (recovery.error_type == recovery_plan_key and 
                    recovery.start_time > cutoff_time)
            ]
            
            return len(recent_attempts) == 0
            
        except Exception as e:
            logger.error(f"❌ Error checking cooldown: {str(e)}")
            return True  # Allow recovery if check fails
    
    def _execute_recovery(self, recovery_execution: RecoveryExecution, reason: str):
        """Execute recovery plan"""
        try:
            logger.info(f"🚑 Executing recovery {recovery_execution.recovery_id}: {reason}")
            
            recovery_plan = recovery_execution.recovery_plan
            success = True
            
            # Execute each recovery action
            for action in recovery_plan.recovery_actions:
                try:
                    action_success = self._execute_recovery_action(action, recovery_execution)
                    recovery_execution.actions_executed.append(f"{action.value}:{'success' if action_success else 'failed'}")
                    
                    if not action_success:
                        success = False
                        recovery_execution.failure_reason = f"Action {action.value} failed"
                        break
                        
                except Exception as e:
                    logger.error(f"❌ Recovery action {action.value} failed: {str(e)}")
                    recovery_execution.actions_executed.append(f"{action.value}:error")
                    recovery_execution.failure_reason = f"Action {action.value} error: {str(e)}"
                    success = False
                    break
            
            # Check success criteria
            if success:
                success = self._check_success_criteria(recovery_plan, recovery_execution)
            
            # Execute rollback if failed
            if not success and recovery_plan.rollback_actions:
                logger.warning(f"🔄 Executing rollback for {recovery_execution.recovery_id}")
                for action in recovery_plan.rollback_actions:
                    try:
                        self._execute_recovery_action(action, recovery_execution)
                        recovery_execution.actions_executed.append(f"rollback_{action.value}")
                    except Exception as e:
                        logger.error(f"❌ Rollback action {action.value} failed: {str(e)}")
            
            # Complete recovery
            recovery_execution.success = success
            recovery_execution.end_time = timezone.now()
            
            # Update statistics
            if success:
                self._stats['successful_recoveries'] += 1
                logger.info(f"✅ Recovery {recovery_execution.recovery_id} completed successfully")
            else:
                self._stats['failed_recoveries'] += 1
                logger.error(f"❌ Recovery {recovery_execution.recovery_id} failed: {recovery_execution.failure_reason}")
            
            # Move to history
            with self._lock:
                if recovery_execution.recovery_id in self._active_recoveries:
                    del self._active_recoveries[recovery_execution.recovery_id]
                self._recovery_history.append(recovery_execution)
            
            # Create alert if recovery failed
            if not success:
                self._create_recovery_alert(recovery_execution)
                
        except Exception as e:
            logger.error(f"❌ Error executing recovery {recovery_execution.recovery_id}: {str(e)}")
            recovery_execution.failure_reason = f"Execution error: {str(e)}"
            recovery_execution.success = False
            recovery_execution.end_time = timezone.now()
    
    def _execute_recovery_action(self, action: RecoveryAction, 
                                recovery_execution: RecoveryExecution) -> bool:
        """Execute specific recovery action"""
        try:
            logger.info(f"🔧 Executing recovery action: {action.value}")
            
            if action == RecoveryAction.RESTART_WORKER:
                return self._restart_workers(recovery_execution)
            
            elif action == RecoveryAction.CLEAR_QUEUE:
                return self._clear_problematic_jobs(recovery_execution)
            
            elif action == RecoveryAction.RESET_CONNECTION:
                return self._reset_connections(recovery_execution)
            
            elif action == RecoveryAction.CLEANUP_MEMORY:
                return self._cleanup_memory(recovery_execution)
            
            elif action == RecoveryAction.SCALE_WORKERS:
                return self._scale_workers(recovery_execution)
            
            elif action == RecoveryAction.FAILOVER_SERVICE:
                return self._failover_service(recovery_execution)
            
            elif action == RecoveryAction.ALERT_OPERATORS:
                return self._alert_operators(recovery_execution)
            
            elif action == RecoveryAction.EMERGENCY_STOP:
                return self._emergency_stop(recovery_execution)
            
            elif action == RecoveryAction.REPROCESS_JOBS:
                return self._reprocess_failed_jobs(recovery_execution)
            
            elif action == RecoveryAction.UPDATE_CONFIGURATION:
                return self._update_configuration(recovery_execution)
            
            else:
                logger.warning(f"⚠️ Unknown recovery action: {action.value}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error executing recovery action {action.value}: {str(e)}")
            return False
    
    def _restart_workers(self, recovery_execution: RecoveryExecution) -> bool:
        """Restart failed workers"""
        try:
            # Mark dead workers for restart
            threshold_time = timezone.now() - timedelta(minutes=5)
            dead_workers = WorkerInstance.objects.filter(
                last_heartbeat__lt=threshold_time,
                status__in=['ACTIVE', 'BUSY']
            )
            
            count = dead_workers.update(status='SHUTDOWN')
            logger.info(f"🔄 Marked {count} workers for restart")
            
            return True
        except Exception as e:
            logger.error(f"❌ Error restarting workers: {str(e)}")
            return False
    
    def _clear_problematic_jobs(self, recovery_execution: RecoveryExecution) -> bool:
        """Clear jobs causing issues"""
        try:
            # Find jobs that have failed multiple times
            problematic_jobs = QueueJob.objects.filter(
                status='FAILED',
                attempts__gte=3
            )
            
            count = 0
            for job in problematic_jobs[:100]:  # Limit to prevent mass deletion
                recovery_execution.affected_jobs.append(str(job.id))
                count += 1
            
            # Move to archived status instead of deleting
            problematic_jobs.update(status='ARCHIVED')
            
            logger.info(f"🗑️ Archived {count} problematic jobs")
            return True
        except Exception as e:
            logger.error(f"❌ Error clearing problematic jobs: {str(e)}")
            return False
    
    def _reset_connections(self, recovery_execution: RecoveryExecution) -> bool:
        """Reset system connections"""
        try:
            # This would reset connection pools
            logger.info("🔄 Resetting system connections")
            # Implementation would depend on your connection pool manager
            return True
        except Exception as e:
            logger.error(f"❌ Error resetting connections: {str(e)}")
            return False
    
    def _cleanup_memory(self, recovery_execution: RecoveryExecution) -> bool:
        """Trigger memory cleanup"""
        try:
            import gc
            collected = gc.collect()
            logger.info(f"🧹 Memory cleanup collected {collected} objects")
            return True
        except Exception as e:
            logger.error(f"❌ Error cleaning up memory: {str(e)}")
            return False
    
    def _scale_workers(self, recovery_execution: RecoveryExecution) -> bool:
        """Scale worker capacity"""
        try:
            logger.info("📈 Scaling workers (implementation needed)")
            # This would interface with your worker pool manager
            return True
        except Exception as e:
            logger.error(f"❌ Error scaling workers: {str(e)}")
            return False
    
    def _failover_service(self, recovery_execution: RecoveryExecution) -> bool:
        """Failover to backup service"""
        try:
            logger.info("🔄 Service failover (implementation needed)")
            # This would implement service failover logic
            return True
        except Exception as e:
            logger.error(f"❌ Error in service failover: {str(e)}")
            return False
    
    def _alert_operators(self, recovery_execution: RecoveryExecution) -> bool:
        """Alert human operators"""
        try:
            # Create high-priority alert
            QueueAlert.objects.create(
                alert_type='RECOVERY_REQUIRED',
                severity='CRITICAL',
                title=f'Manual intervention required: {recovery_execution.error_type}',
                message=f'Automatic recovery failed. Recovery ID: {recovery_execution.recovery_id}',
                details={
                    'recovery_id': recovery_execution.recovery_id,
                    'error_type': recovery_execution.error_type,
                    'actions_executed': recovery_execution.actions_executed
                }
            )
            
            logger.critical(f"🚨 Operators alerted for recovery: {recovery_execution.recovery_id}")
            return True
        except Exception as e:
            logger.error(f"❌ Error alerting operators: {str(e)}")
            return False
    
    def _emergency_stop(self, recovery_execution: RecoveryExecution) -> bool:
        """Emergency stop of system components"""
        try:
            logger.critical("🛑 Emergency stop triggered")
            # This would implement emergency stop procedures
            return True
        except Exception as e:
            logger.error(f"❌ Error in emergency stop: {str(e)}")
            return False
    
    def _reprocess_failed_jobs(self, recovery_execution: RecoveryExecution) -> bool:
        """Reprocess failed jobs"""
        try:
            # Reset recently failed jobs
            recent_failures = QueueJob.objects.filter(
                status='FAILED',
                completed_at__gte=timezone.now() - timedelta(hours=1),
                attempts__lt=3
            )
            
            count = 0
            for job in recent_failures[:50]:  # Limit reprocessing
                job.status = 'QUEUED'
                job.completed_at = None
                job.error_message = ''
                job.save()
                recovery_execution.affected_jobs.append(str(job.id))
                count += 1
            
            logger.info(f"🔄 Requeued {count} failed jobs for reprocessing")
            return True
        except Exception as e:
            logger.error(f"❌ Error reprocessing failed jobs: {str(e)}")
            return False
    
    def _update_configuration(self, recovery_execution: RecoveryExecution) -> bool:
        """Update system configuration for recovery"""
        try:
            logger.info("⚙️ Updating configuration (implementation needed)")
            # This would update configuration based on the error pattern
            return True
        except Exception as e:
            logger.error(f"❌ Error updating configuration: {str(e)}")
            return False
    
    def _check_success_criteria(self, recovery_plan: RecoveryPlan, 
                               recovery_execution: RecoveryExecution) -> bool:
        """Check if recovery success criteria are met"""
        try:
            # Simple implementation - check basic system health
            # In production, this would check specific criteria from the plan
            
            # Check if workers are responding
            active_workers = WorkerInstance.objects.filter(
                status__in=['ACTIVE', 'BUSY'],
                last_heartbeat__gte=timezone.now() - timedelta(minutes=2)
            ).count()
            
            if active_workers == 0:
                return False
            
            # Check if jobs are being processed
            recent_completions = QueueJob.objects.filter(
                status='COMPLETED',
                completed_at__gte=timezone.now() - timedelta(minutes=5)
            ).count()
            
            return recent_completions > 0
            
        except Exception as e:
            logger.error(f"❌ Error checking success criteria: {str(e)}")
            return False
    
    def _create_recovery_alert(self, recovery_execution: RecoveryExecution):
        """Create alert for failed recovery"""
        try:
            QueueAlert.objects.create(
                alert_type='RECOVERY_FAILURE',
                severity='CRITICAL',
                title=f'Recovery failed: {recovery_execution.error_type}',
                message=f'Recovery {recovery_execution.recovery_id} failed: {recovery_execution.failure_reason}',
                details={
                    'recovery_id': recovery_execution.recovery_id,
                    'error_type': recovery_execution.error_type,
                    'failure_reason': recovery_execution.failure_reason,
                    'actions_executed': recovery_execution.actions_executed,
                    'affected_jobs': recovery_execution.affected_jobs
                }
            )
        except Exception as e:
            logger.error(f"❌ Error creating recovery alert: {str(e)}")
    
    def _monitor_active_recoveries(self):
        """Monitor active recovery executions"""
        try:
            with self._lock:
                for recovery_id, recovery in list(self._active_recoveries.items()):
                    # Check for hung recoveries
                    elapsed_time = timezone.now() - recovery.start_time
                    if elapsed_time > timedelta(minutes=30):  # 30 minute timeout
                        logger.warning(f"⚠️ Recovery {recovery_id} appears hung, marking as failed")
                        recovery.success = False
                        recovery.failure_reason = "Recovery timeout"
                        recovery.end_time = timezone.now()
                        
                        # Move to history
                        del self._active_recoveries[recovery_id]
                        self._recovery_history.append(recovery)
                        
        except Exception as e:
            logger.error(f"❌ Error monitoring active recoveries: {str(e)}")
    
    def _cleanup_recovery_history(self):
        """Clean up old recovery history"""
        try:
            # Keep only last 100 recovery executions
            if len(self._recovery_history) > 100:
                self._recovery_history = self._recovery_history[-100:]
                
        except Exception as e:
            logger.error(f"❌ Error cleaning up recovery history: {str(e)}")
    
    # Public API methods
    
    def get_recovery_status(self) -> Dict:
        """Get current recovery service status"""
        try:
            with self._lock:
                active_count = len(self._active_recoveries)
                active_recoveries = [
                    {
                        'recovery_id': recovery.recovery_id,
                        'error_type': recovery.error_type,
                        'start_time': recovery.start_time.isoformat(),
                        'actions_executed': recovery.actions_executed
                    }
                    for recovery in self._active_recoveries.values()
                ]
            
            uptime_hours = (timezone.now() - self._stats['start_time']).total_seconds() / 3600
            
            return {
                'is_running': self._is_running,
                'uptime_hours': uptime_hours,
                'active_recoveries': active_count,
                'active_recovery_details': active_recoveries,
                'statistics': self._stats.copy(),
                'available_recovery_plans': list(self.recovery_plans.keys())
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting recovery status: {str(e)}")
            return {'error': str(e)}
    
    def get_recovery_history(self, limit: int = 50) -> List[Dict]:
        """Get recovery execution history"""
        try:
            recent_history = self._recovery_history[-limit:]
            return [asdict(recovery) for recovery in recent_history]
        except Exception as e:
            logger.error(f"❌ Error getting recovery history: {str(e)}")
            return []
    
    def add_recovery_plan(self, key: str, plan: RecoveryPlan):
        """Add custom recovery plan"""
        try:
            self.recovery_plans[key] = plan
            logger.info(f"📋 Added recovery plan: {key}")
        except Exception as e:
            logger.error(f"❌ Error adding recovery plan: {str(e)}")
    
    def shutdown(self):
        """Shutdown recovery service"""
        logger.info("🛑 Shutting down recovery service")
        
        self._shutdown_event.set()
        self._is_running = False
        
        # Wait for monitoring thread
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=10)
        
        # Wait for active recoveries to complete
        with self._lock:
            if self._active_recoveries:
                logger.info(f"⏳ Waiting for {len(self._active_recoveries)} active recoveries to complete")
                # Give recoveries time to complete
                time.sleep(30)
        
        logger.info("✅ Recovery service shutdown complete")

# Global recovery service instance
recovery_service = None

def get_recovery_service() -> RecoveryService:
    """Get global recovery service instance"""
    global recovery_service
    if recovery_service is None:
        recovery_service = RecoveryService()
    return recovery_service