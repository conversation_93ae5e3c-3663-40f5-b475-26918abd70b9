# queue_system/error_management/retry_manager.py
"""
Retry Manager for Queue System
Implements intelligent retry logic with exponential backoff and jitter
"""

import random
import time
import logging
from typing import Dict, Optional, List, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from django.utils import timezone
from django.core.cache import cache

logger = logging.getLogger('queue_system.retry_manager')

class RetryReason(Enum):
    """Enumeration of retry reasons"""
    AWS_RATE_LIMIT = "aws_rate_limit"
    AWS_SERVICE_ERROR = "aws_service_error"
    DATABASE_ERROR = "database_error"
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    PROCESSING_ERROR = "processing_error"
    RESOURCE_UNAVAILABLE = "resource_unavailable"
    TEMPORARY_FAILURE = "temporary_failure"
    UNKNOWN_ERROR = "unknown_error"

@dataclass
class RetryAttempt:
    """Data structure for tracking retry attempts"""
    attempt_number: int
    timestamp: datetime
    error_type: str
    error_message: str
    delay_seconds: float
    job_id: str
    job_type: str

class RetryStrategy:
    """Base retry strategy class"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, max_delay: float = 300.0):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    def should_retry(self, attempt: int, error: Exception) -> bool:
        """Determine if job should be retried"""
        return attempt < self.max_attempts
    
    def calculate_delay(self, attempt: int, error: Exception) -> float:
        """Calculate delay for next retry attempt"""
        raise NotImplementedError("Subclasses must implement calculate_delay")

class ExponentialBackoffStrategy(RetryStrategy):
    """Exponential backoff with jitter"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 2.0, max_delay: float = 300.0, 
                 multiplier: float = 2.0, jitter: bool = True):
        super().__init__(max_attempts, base_delay, max_delay)
        self.multiplier = multiplier
        self.jitter = jitter
    
    def calculate_delay(self, attempt: int, error: Exception) -> float:
        """Calculate exponential backoff delay with optional jitter"""
        try:
            # Base exponential calculation
            delay = self.base_delay * (self.multiplier ** (attempt - 1))
            
            # Apply maximum delay limit
            delay = min(delay, self.max_delay)
            
            # Add jitter to prevent thundering herd
            if self.jitter:
                jitter_range = delay * 0.1  # 10% jitter
                delay += random.uniform(-jitter_range, jitter_range)
            
            # Ensure minimum delay
            return max(delay, 1.0)
            
        except Exception as e:
            logger.error(f"❌ Error calculating retry delay: {str(e)}")
            return self.base_delay

class LinearBackoffStrategy(RetryStrategy):
    """Linear backoff strategy"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 5.0, max_delay: float = 60.0):
        super().__init__(max_attempts, base_delay, max_delay)
    
    def calculate_delay(self, attempt: int, error: Exception) -> float:
        """Calculate linear backoff delay"""
        delay = self.base_delay * attempt
        return min(delay, self.max_delay)

class FixedDelayStrategy(RetryStrategy):
    """Fixed delay strategy"""
    
    def __init__(self, max_attempts: int = 3, delay: float = 10.0):
        super().__init__(max_attempts, delay, delay)
        self.delay = delay
    
    def calculate_delay(self, attempt: int, error: Exception) -> float:
        """Return fixed delay"""
        return self.delay

class RetryManager:
    """
    Intelligent retry management with multiple strategies
    Provides error classification and appropriate retry logic
    """
    
    def __init__(self):
        self.cache_prefix = 'retry_manager'
        
        # Retry strategies by error type
        self.strategies = {
            RetryReason.AWS_RATE_LIMIT: ExponentialBackoffStrategy(
                max_attempts=5, base_delay=5.0, max_delay=120.0, multiplier=2.5
            ),
            RetryReason.AWS_SERVICE_ERROR: ExponentialBackoffStrategy(
                max_attempts=3, base_delay=10.0, max_delay=180.0
            ),
            RetryReason.DATABASE_ERROR: ExponentialBackoffStrategy(
                max_attempts=3, base_delay=2.0, max_delay=60.0
            ),
            RetryReason.NETWORK_ERROR: ExponentialBackoffStrategy(
                max_attempts=4, base_delay=3.0, max_delay=90.0
            ),
            RetryReason.TIMEOUT_ERROR: LinearBackoffStrategy(
                max_attempts=2, base_delay=15.0, max_delay=45.0
            ),
            RetryReason.PROCESSING_ERROR: ExponentialBackoffStrategy(
                max_attempts=2, base_delay=5.0, max_delay=30.0
            ),
            RetryReason.RESOURCE_UNAVAILABLE: LinearBackoffStrategy(
                max_attempts=3, base_delay=10.0, max_delay=60.0
            ),
            RetryReason.TEMPORARY_FAILURE: ExponentialBackoffStrategy(
                max_attempts=3, base_delay=3.0, max_delay=60.0
            ),
            RetryReason.UNKNOWN_ERROR: FixedDelayStrategy(
                max_attempts=2, delay=10.0
            )
        }
        
        # Error classification patterns
        self.error_patterns = {
            RetryReason.AWS_RATE_LIMIT: [
                'Throttling', 'Rate exceeded', 'Too many requests',
                'ProvisionedThroughputExceededException', 'LimitExceededException'
            ],
            RetryReason.AWS_SERVICE_ERROR: [
                'ServiceUnavailable', 'InternalServerError', 'ServiceException',
                'TemporaryRedirect', 'SlowDown'
            ],
            RetryReason.DATABASE_ERROR: [
                'connection', 'timeout', 'deadlock', 'lock', 'IntegrityError',
                'OperationalError', 'DatabaseError'
            ],
            RetryReason.NETWORK_ERROR: [
                'ConnectionError', 'ConnectTimeout', 'ReadTimeout',
                'ConnectionRefusedError', 'socket.error', 'URLError'
            ],
            RetryReason.TIMEOUT_ERROR: [
                'TimeoutError', 'timeout', 'ReadTimeoutError', 'ConnectTimeoutError'
            ],
            RetryReason.PROCESSING_ERROR: [
                'ProcessingError', 'ValidationError', 'FormatError'
            ],
            RetryReason.RESOURCE_UNAVAILABLE: [
                'ResourceNotAvailable', 'ServiceUnavailable', 'Busy',
                'PoolExhausted', 'ResourceExhausted'
            ],
            RetryReason.TEMPORARY_FAILURE: [
                'TemporaryFailure', 'TryAgain', 'Retry', 'Temporary'
            ]
        }
        
        # Statistics
        self._stats = {
            'total_retries': 0,
            'successful_retries': 0,
            'failed_retries': 0,
            'retries_by_reason': {reason.value: 0 for reason in RetryReason},
            'retries_by_strategy': {},
            'start_time': timezone.now()
        }
        
        logger.info("🔄 Retry Manager initialized")
    
    def classify_error(self, error: Exception, context: Optional[Dict] = None) -> RetryReason:
        """
        Classify error to determine appropriate retry strategy
        
        Args:
            error: The exception that occurred
            context: Additional context about the error
            
        Returns:
            RetryReason enum value
        """
        try:
            error_message = str(error).lower()
            error_type = type(error).__name__.lower()
            
            # Check error message against patterns
            for reason, patterns in self.error_patterns.items():
                for pattern in patterns:
                    if pattern.lower() in error_message or pattern.lower() in error_type:
                        logger.debug(f"🔍 Classified error as {reason.value}: {pattern}")
                        return reason
            
            # Check specific exception types
            if 'boto' in str(type(error).__module__):
                # AWS/Boto3 related errors
                if 'throttling' in error_message or 'rate' in error_message:
                    return RetryReason.AWS_RATE_LIMIT
                else:
                    return RetryReason.AWS_SERVICE_ERROR
            
            # Context-based classification
            if context:
                if context.get('operation') == 'database':
                    return RetryReason.DATABASE_ERROR
                elif context.get('operation') == 'network':
                    return RetryReason.NETWORK_ERROR
                elif context.get('timeout', False):
                    return RetryReason.TIMEOUT_ERROR
            
            # Default classification
            logger.debug(f"🤷 Could not classify error, using UNKNOWN_ERROR: {error}")
            return RetryReason.UNKNOWN_ERROR
            
        except Exception as e:
            logger.error(f"❌ Error classifying error: {str(e)}")
            return RetryReason.UNKNOWN_ERROR
    
    def should_retry(self, job_id: str, job_type: str, attempt: int, 
                    error: Exception, context: Optional[Dict] = None) -> bool:
        """
        Determine if a job should be retried
        
        Args:
            job_id: Unique job identifier
            job_type: Type of job
            attempt: Current attempt number (1-based)
            error: The exception that occurred
            context: Additional context
            
        Returns:
            True if job should be retried, False otherwise
        """
        try:
            # Classify the error
            error_reason = self.classify_error(error, context)
            
            # Get appropriate strategy
            strategy = self.strategies.get(error_reason, self.strategies[RetryReason.UNKNOWN_ERROR])
            
            # Check if we should retry
            should_retry = strategy.should_retry(attempt, error)
            
            # Record attempt
            self._record_retry_attempt(job_id, job_type, attempt, error_reason, error, should_retry)
            
            if should_retry:
                logger.info(f"🔄 Job {job_id} will be retried (attempt {attempt}/{strategy.max_attempts})")
            else:
                logger.warning(f"❌ Job {job_id} will NOT be retried (max attempts reached)")
            
            return should_retry
            
        except Exception as e:
            logger.error(f"❌ Error determining retry: {str(e)}")
            return False  # Default to no retry on error
    
    def calculate_delay(self, job_id: str, attempt: int, error: Exception, 
                       context: Optional[Dict] = None) -> float:
        """
        Calculate delay before next retry attempt
        
        Args:
            job_id: Unique job identifier
            attempt: Current attempt number (1-based)
            error: The exception that occurred
            context: Additional context
            
        Returns:
            Delay in seconds
        """
        try:
            # Classify the error
            error_reason = self.classify_error(error, context)
            
            # Get appropriate strategy
            strategy = self.strategies.get(error_reason, self.strategies[RetryReason.UNKNOWN_ERROR])
            
            # Calculate delay
            delay = strategy.calculate_delay(attempt, error)
            
            logger.info(f"⏱️ Job {job_id} retry delay: {delay:.1f}s (reason: {error_reason.value})")
            
            return delay
            
        except Exception as e:
            logger.error(f"❌ Error calculating retry delay: {str(e)}")
            return 60.0  # Default 1 minute delay
    
    def _record_retry_attempt(self, job_id: str, job_type: str, attempt: int, 
                             error_reason: RetryReason, error: Exception, will_retry: bool):
        """Record retry attempt for statistics and monitoring"""
        try:
            # Create retry attempt record
            retry_attempt = RetryAttempt(
                attempt_number=attempt,
                timestamp=timezone.now(),
                error_type=error_reason.value,
                error_message=str(error)[:500],  # Limit message length
                delay_seconds=0.0,  # Will be updated separately
                job_id=job_id,
                job_type=job_type
            )
            
            # Cache retry attempt
            cache_key = f"{self.cache_prefix}_attempt_{job_id}_{attempt}"
            cache.set(cache_key, retry_attempt.__dict__, timeout=3600)
            
            # Update statistics
            self._stats['total_retries'] += 1
            self._stats['retries_by_reason'][error_reason.value] += 1
            
            strategy_name = type(self.strategies[error_reason]).__name__
            if strategy_name not in self._stats['retries_by_strategy']:
                self._stats['retries_by_strategy'][strategy_name] = 0
            self._stats['retries_by_strategy'][strategy_name] += 1
            
            if not will_retry:
                self._stats['failed_retries'] += 1
            
            logger.debug(f"📊 Recorded retry attempt for job {job_id}")
            
        except Exception as e:
            logger.error(f"❌ Error recording retry attempt: {str(e)}")
    
    def record_retry_success(self, job_id: str):
        """Record successful retry completion"""
        try:
            self._stats['successful_retries'] += 1
            logger.debug(f"✅ Recorded successful retry for job {job_id}")
        except Exception as e:
            logger.error(f"❌ Error recording retry success: {str(e)}")
    
    def get_retry_history(self, job_id: str) -> List[Dict]:
        """Get retry history for a specific job"""
        try:
            history = []
            
            # Look for cached retry attempts
            for attempt in range(1, 11):  # Check up to 10 attempts
                cache_key = f"{self.cache_prefix}_attempt_{job_id}_{attempt}"
                attempt_data = cache.get(cache_key)
                
                if attempt_data:
                    history.append(attempt_data)
                else:
                    break  # No more attempts
            
            return sorted(history, key=lambda x: x['attempt_number'])
            
        except Exception as e:
            logger.error(f"❌ Error getting retry history: {str(e)}")
            return []
    
    def get_retry_statistics(self) -> Dict:
        """Get comprehensive retry statistics"""
        try:
            uptime_hours = (timezone.now() - self._stats['start_time']).total_seconds() / 3600
            
            stats = self._stats.copy()
            stats['uptime_hours'] = uptime_hours
            
            # Calculate rates
            if uptime_hours > 0:
                stats['retries_per_hour'] = self._stats['total_retries'] / uptime_hours
            else:
                stats['retries_per_hour'] = 0
            
            # Calculate success rate
            if self._stats['total_retries'] > 0:
                stats['retry_success_rate'] = (
                    self._stats['successful_retries'] / self._stats['total_retries'] * 100
                )
            else:
                stats['retry_success_rate'] = 0
            
            # Add strategy information
            stats['available_strategies'] = {
                reason.value: {
                    'strategy_class': type(strategy).__name__,
                    'max_attempts': strategy.max_attempts,
                    'base_delay': strategy.base_delay,
                    'max_delay': strategy.max_delay
                }
                for reason, strategy in self.strategies.items()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error getting retry statistics: {str(e)}")
            return {'error': str(e)}
    
    def update_strategy(self, error_reason: RetryReason, strategy: RetryStrategy):
        """Update retry strategy for specific error reason"""
        try:
            self.strategies[error_reason] = strategy
            logger.info(f"🔧 Updated retry strategy for {error_reason.value}")
        except Exception as e:
            logger.error(f"❌ Error updating strategy: {str(e)}")
    
    def add_error_pattern(self, error_reason: RetryReason, pattern: str):
        """Add error pattern for classification"""
        try:
            if error_reason not in self.error_patterns:
                self.error_patterns[error_reason] = []
            
            self.error_patterns[error_reason].append(pattern)
            logger.info(f"📝 Added error pattern '{pattern}' for {error_reason.value}")
        except Exception as e:
            logger.error(f"❌ Error adding error pattern: {str(e)}")
    
    def clear_retry_history(self, job_id: str):
        """Clear retry history for a specific job"""
        try:
            # Clear cached retry attempts
            for attempt in range(1, 11):  # Clear up to 10 attempts
                cache_key = f"{self.cache_prefix}_attempt_{job_id}_{attempt}"
                cache.delete(cache_key)
            
            logger.debug(f"🧹 Cleared retry history for job {job_id}")
            
        except Exception as e:
            logger.error(f"❌ Error clearing retry history: {str(e)}")

# Decorator for automatic retry
def with_retry(retry_manager: RetryManager, job_id: str, job_type: str, 
               context: Optional[Dict] = None):
    """
    Decorator to add automatic retry logic to functions
    
    Args:
        retry_manager: RetryManager instance
        job_id: Unique job identifier
        job_type: Type of job
        context: Additional context for error classification
    """
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            attempt = 1
            last_error = None
            
            while True:
                try:
                    return func(*args, **kwargs)
                    
                except Exception as error:
                    last_error = error
                    
                    # Check if we should retry
                    if retry_manager.should_retry(job_id, job_type, attempt, error, context):
                        # Calculate delay
                        delay = retry_manager.calculate_delay(job_id, attempt, error, context)
                        
                        # Wait before retry
                        time.sleep(delay)
                        
                        attempt += 1
                        continue
                    else:
                        # No more retries, raise the last error
                        raise last_error
            
        return wrapper
    return decorator

# Context manager for retry logic
class RetryContext:
    """Context manager for retry operations"""
    
    def __init__(self, retry_manager: RetryManager, job_id: str, job_type: str,
                 context: Optional[Dict] = None):
        self.retry_manager = retry_manager
        self.job_id = job_id
        self.job_type = job_type
        self.context = context
        self.attempt = 1
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            # An exception occurred
            should_retry = self.retry_manager.should_retry(
                self.job_id, self.job_type, self.attempt, exc_val, self.context
            )
            
            if should_retry:
                delay = self.retry_manager.calculate_delay(
                    self.job_id, self.attempt, exc_val, self.context
                )
                
                # Store retry information for caller
                self.should_retry = True
                self.retry_delay = delay
                self.attempt += 1
                return True  # Suppress the exception
            else:
                self.should_retry = False
                return False  # Let the exception propagate
        else:
            # No exception, operation succeeded
            if self.attempt > 1:
                self.retry_manager.record_retry_success(self.job_id)
            return False

# Global retry manager instance
retry_manager = None

def get_retry_manager() -> RetryManager:
    """Get global retry manager instance"""
    global retry_manager
    if retry_manager is None:
        retry_manager = RetryManager()
    return retry_manager