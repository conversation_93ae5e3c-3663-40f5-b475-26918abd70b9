# Generated by Django 5.1.5 on 2025-06-28 05:53

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='QueueConfiguration',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('config_key', models.CharField(max_length=100, unique=True)),
                ('config_value', models.JSONField()),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Queue Configuration',
                'verbose_name_plural': 'Queue Configurations',
                'db_table': 'queue_configuration',
                'ordering': ['config_key'],
            },
        ),
        migrations.CreateModel(
            name='QueueJob',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('job_type', models.CharField(choices=[('FACIAL_RECOGNITION', 'Facial Recognition'), ('PHOTO_PROCESSING', 'Photo Processing'), ('BULK_UPLOAD', 'Bulk Upload'), ('MAINTENANCE', 'Maintenance')], max_length=50)),
                ('priority', models.CharField(choices=[('EMERGENCY', 'Emergency'), ('HIGH', 'High'), ('STANDARD', 'Standard'), ('LOW', 'Low'), ('MAINTENANCE', 'Maintenance')], default='STANDARD', max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('QUEUED', 'Queued'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled'), ('RETRYING', 'Retrying')], default='PENDING', max_length=20)),
                ('job_data', models.JSONField(help_text='Job-specific data and parameters')),
                ('result_data', models.JSONField(blank=True, help_text='Job execution results', null=True)),
                ('error_message', models.TextField(blank=True, help_text='Error details if job failed')),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('timeout_seconds', models.PositiveIntegerField(default=300)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('scheduled_at', models.DateTimeField(blank=True, help_text='When job should be executed', null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('processing_time', models.FloatField(blank=True, help_text='Processing time in seconds', null=True)),
                ('worker_id', models.CharField(blank=True, help_text='ID of worker processing this job', max_length=100)),
                ('callback_url', models.URLField(blank=True, help_text='URL to call when job completes')),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata')),
            ],
            options={
                'verbose_name': 'Queue Job',
                'verbose_name_plural': 'Queue Jobs',
                'db_table': 'queue_jobs',
                'ordering': ['priority', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='QueueMetrics',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('metric_type', models.CharField(choices=[('SYSTEM', 'System'), ('QUEUE', 'Queue'), ('WORKER', 'Worker'), ('PERFORMANCE', 'Performance')], max_length=20)),
                ('metric_name', models.CharField(max_length=100)),
                ('metric_value', models.FloatField()),
                ('tags', models.JSONField(default=dict, help_text='Additional metric tags')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('period_start', models.DateTimeField(blank=True, help_text='Start of measurement period', null=True)),
                ('period_end', models.DateTimeField(blank=True, help_text='End of measurement period', null=True)),
            ],
            options={
                'verbose_name': 'Queue Metric',
                'verbose_name_plural': 'Queue Metrics',
                'db_table': 'queue_metrics',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='WorkerStatus',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('worker_id', models.CharField(max_length=100, unique=True)),
                ('worker_type', models.CharField(choices=[('EMERGENCY', 'Emergency'), ('HIGH', 'High'), ('STANDARD', 'Standard'), ('LOW', 'Low'), ('MAINTENANCE', 'Maintenance')], max_length=20)),
                ('status', models.CharField(choices=[('IDLE', 'Idle'), ('BUSY', 'Busy'), ('STOPPING', 'Stopping'), ('STOPPED', 'Stopped'), ('ERROR', 'Error')], max_length=20)),
                ('current_job_id', models.UUIDField(blank=True, null=True)),
                ('jobs_processed', models.PositiveIntegerField(default=0)),
                ('total_processing_time', models.FloatField(default=0.0)),
                ('average_processing_time', models.FloatField(default=0.0)),
                ('last_job_at', models.DateTimeField(blank=True, null=True)),
                ('error_count', models.PositiveIntegerField(default=0)),
                ('last_error', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('last_heartbeat', models.DateTimeField(auto_now=True)),
                ('system_info', models.JSONField(default=dict, help_text='Worker system information')),
            ],
            options={
                'verbose_name': 'Worker Status',
                'verbose_name_plural': 'Worker Statuses',
                'db_table': 'worker_status',
                'ordering': ['worker_type', 'worker_id'],
            },
        ),
        migrations.CreateModel(
            name='ErrorLog',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('error_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('error_type', models.CharField(max_length=100)),
                ('error_category', models.CharField(choices=[('SYSTEM', 'System'), ('DATABASE', 'Database'), ('NETWORK', 'Network'), ('AUTHENTICATION', 'Authentication'), ('VALIDATION', 'Validation'), ('BUSINESS_LOGIC', 'Business Logic'), ('EXTERNAL_SERVICE', 'External Service'), ('RESOURCE', 'Resource'), ('CONFIGURATION', 'Configuration'), ('UNKNOWN', 'Unknown')], max_length=20)),
                ('severity', models.CharField(choices=[('CRITICAL', 'Critical'), ('HIGH', 'High'), ('MEDIUM', 'Medium'), ('LOW', 'Low'), ('INFO', 'Info')], max_length=10)),
                ('error_message', models.TextField()),
                ('stack_trace', models.TextField(blank=True)),
                ('context_data', models.JSONField(default=dict, help_text='Error context and metadata')),
                ('recovery_actions', models.JSONField(default=list, help_text='Recovery actions taken')),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolution_notes', models.TextField(blank=True)),
                ('occurred_at', models.DateTimeField(auto_now_add=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('worker_id', models.CharField(blank=True, max_length=100)),
                ('job', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='error_logs', to='queue_system.queuejob')),
            ],
            options={
                'verbose_name': 'Error Log',
                'verbose_name_plural': 'Error Logs',
                'db_table': 'error_logs',
                'ordering': ['-occurred_at'],
            },
        ),
        migrations.CreateModel(
            name='JobDependency',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('dependency_type', models.CharField(choices=[('SEQUENTIAL', 'Sequential'), ('PARALLEL', 'Parallel'), ('CONDITIONAL', 'Conditional')], max_length=20)),
                ('condition_data', models.JSONField(blank=True, help_text='Condition data for conditional dependencies', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('dependent_job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dependencies', to='queue_system.queuejob')),
                ('prerequisite_job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dependents', to='queue_system.queuejob')),
            ],
            options={
                'verbose_name': 'Job Dependency',
                'verbose_name_plural': 'Job Dependencies',
                'db_table': 'job_dependencies',
                'unique_together': {('dependent_job', 'prerequisite_job')},
            },
        ),
    ]
