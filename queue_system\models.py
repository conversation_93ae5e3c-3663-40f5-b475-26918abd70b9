# queue_system/models.py
"""
Django models for PhotoFish Enhanced Queue System
EXACT MATCH to existing migration - no field conflicts
"""

import uuid
from django.db import models
from django.utils import timezone
from django.conf import settings
from django.core.validators import MinV<PERSON>ueValida<PERSON>, MaxValueValidator


class QueueJob(models.Model):
    """
    Model for storing queue job information and state
    MATCHES EXISTING MIGRATION EXACTLY
    """
    
    JOB_TYPES = [
        ('FACIAL_RECOGNITION', 'Facial Recognition'),
        ('PHOTO_PROCESSING', 'Photo Processing'),
        ('BULK_UPLOAD', 'Bulk Upload'),
        ('MAINTENANCE', 'Maintenance'),
    ]
    
    PRIORITY_LEVELS = [
        ('EMERGENCY', 'Emergency'),
        ('HIGH', 'High'),
        ('STANDARD', 'Standard'),
        ('LOW', 'Low'),
        ('MAINTENANCE', 'Maintenance'),
    ]
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('QUEUED', 'Queued'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
        ('RETRYING', 'Retrying'),
    ]
    
    # Fields EXACTLY as in your existing migration
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    job_type = models.CharField(max_length=50, choices=JOB_TYPES)
    priority = models.CharField(max_length=20, choices=PRIORITY_LEVELS, default='STANDARD')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    job_data = models.JSONField(help_text="Job-specific data and parameters")
    result_data = models.JSONField(null=True, blank=True, help_text="Job execution results")
    error_message = models.TextField(blank=True, help_text="Error details if job failed")
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    timeout_seconds = models.PositiveIntegerField(default=300)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    scheduled_at = models.DateTimeField(null=True, blank=True, help_text="When job should be executed")
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    processing_time = models.FloatField(null=True, blank=True, help_text="Processing time in seconds")
    worker_id = models.CharField(max_length=100, blank=True, help_text="ID of worker processing this job")
    callback_url = models.URLField(blank=True, help_text="URL to call when job completes")
    metadata = models.JSONField(default=dict, help_text="Additional metadata")
    
    class Meta:
        db_table = 'queue_jobs'
        ordering = ['priority', 'created_at']
        verbose_name = 'Queue Job'
        verbose_name_plural = 'Queue Jobs'
    
    def __str__(self):
        return f"{self.job_type} - {self.priority} - {self.status}"


class QueueMetrics(models.Model):
    """
    Model for storing queue metrics - MATCHES MIGRATION
    """
    
    METRIC_TYPES = [
        ('SYSTEM', 'System'),
        ('QUEUE', 'Queue'),
        ('WORKER', 'Worker'),
        ('PERFORMANCE', 'Performance'),
    ]
    
    id = models.AutoField(primary_key=True)
    metric_type = models.CharField(max_length=20, choices=METRIC_TYPES)
    metric_name = models.CharField(max_length=100)
    metric_value = models.FloatField()
    tags = models.JSONField(default=dict, help_text="Additional metric tags")
    timestamp = models.DateTimeField(auto_now_add=True)
    period_start = models.DateTimeField(null=True, blank=True, help_text="Start of measurement period")
    period_end = models.DateTimeField(null=True, blank=True, help_text="End of measurement period")
    
    class Meta:
        db_table = 'queue_metrics'
        ordering = ['-timestamp']
        verbose_name = 'Queue Metric'
        verbose_name_plural = 'Queue Metrics'
    
    def __str__(self):
        return f"{self.metric_type}: {self.metric_value}"


class WorkerStatus(models.Model):
    """
    Model for tracking worker status - MATCHES MIGRATION
    """
    
    WORKER_TYPES = [
        ('EMERGENCY', 'Emergency'),
        ('HIGH', 'High'),
        ('STANDARD', 'Standard'),
        ('LOW', 'Low'),
        ('MAINTENANCE', 'Maintenance'),
    ]
    
    STATUS_CHOICES = [
        ('IDLE', 'Idle'),
        ('BUSY', 'Busy'),
        ('STOPPING', 'Stopping'),
        ('STOPPED', 'Stopped'),
        ('ERROR', 'Error'),
    ]
    
    id = models.AutoField(primary_key=True)
    worker_id = models.CharField(max_length=100, unique=True)
    worker_type = models.CharField(max_length=20, choices=WORKER_TYPES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    current_job_id = models.UUIDField(null=True, blank=True)
    jobs_processed = models.PositiveIntegerField(default=0)
    total_processing_time = models.FloatField(default=0.0)
    average_processing_time = models.FloatField(default=0.0)
    last_job_at = models.DateTimeField(null=True, blank=True)
    error_count = models.PositiveIntegerField(default=0)
    last_error = models.TextField(blank=True)
    started_at = models.DateTimeField(auto_now_add=True)
    last_heartbeat = models.DateTimeField(auto_now=True)
    system_info = models.JSONField(default=dict, help_text="Worker system information")
    
    class Meta:
        db_table = 'worker_status'
        ordering = ['worker_type', 'worker_id']
        verbose_name = 'Worker Status'
        verbose_name_plural = 'Worker Statuses'
    
    def __str__(self):
        return f"Worker {self.worker_id} - {self.status}"


class ErrorLog(models.Model):
    """
    Model for error logging - MATCHES MIGRATION
    """
    
    ERROR_CATEGORIES = [
        ('SYSTEM', 'System'),
        ('DATABASE', 'Database'),
        ('NETWORK', 'Network'),
        ('AUTHENTICATION', 'Authentication'),
        ('VALIDATION', 'Validation'),
        ('BUSINESS_LOGIC', 'Business Logic'),
        ('EXTERNAL_SERVICE', 'External Service'),
        ('RESOURCE', 'Resource'),
        ('CONFIGURATION', 'Configuration'),
        ('UNKNOWN', 'Unknown'),
    ]
    
    SEVERITY_CHOICES = [
        ('CRITICAL', 'Critical'),
        ('HIGH', 'High'),
        ('MEDIUM', 'Medium'),
        ('LOW', 'Low'),
        ('INFO', 'Info'),
    ]
    
    id = models.AutoField(primary_key=True)
    error_id = models.UUIDField(default=uuid.uuid4, unique=True)
    error_type = models.CharField(max_length=100)
    error_category = models.CharField(max_length=20, choices=ERROR_CATEGORIES)
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES)
    error_message = models.TextField()
    stack_trace = models.TextField(blank=True)
    context_data = models.JSONField(default=dict, help_text="Error context and metadata")
    recovery_actions = models.JSONField(default=list, help_text="Recovery actions taken")
    is_resolved = models.BooleanField(default=False)
    resolution_notes = models.TextField(blank=True)
    occurred_at = models.DateTimeField(auto_now_add=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    job = models.ForeignKey(QueueJob, on_delete=models.SET_NULL, null=True, blank=True, related_name='error_logs')
    worker_id = models.CharField(max_length=100, blank=True)
    
    class Meta:
        db_table = 'error_logs'
        ordering = ['-occurred_at']
        verbose_name = 'Error Log'
        verbose_name_plural = 'Error Logs'
    
    def __str__(self):
        return f"{self.error_type} - {self.severity}"


class QueueConfiguration(models.Model):
    """
    Model for queue configuration - MATCHES MIGRATION
    """
    
    id = models.AutoField(primary_key=True)
    config_key = models.CharField(max_length=100, unique=True)
    config_value = models.JSONField()
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'queue_configuration'
        ordering = ['config_key']
        verbose_name = 'Queue Configuration'
        verbose_name_plural = 'Queue Configurations'
    
    def __str__(self):
        return f"{self.config_key}"


class JobDependency(models.Model):
    """
    Model for job dependencies - MATCHES MIGRATION
    """
    
    DEPENDENCY_TYPES = [
        ('SEQUENTIAL', 'Sequential'),
        ('PARALLEL', 'Parallel'),
        ('CONDITIONAL', 'Conditional'),
    ]
    
    id = models.AutoField(primary_key=True)
    dependent_job = models.ForeignKey(QueueJob, on_delete=models.CASCADE, related_name='dependencies')
    prerequisite_job = models.ForeignKey(QueueJob, on_delete=models.CASCADE, related_name='dependents')
    dependency_type = models.CharField(max_length=20, choices=DEPENDENCY_TYPES)
    condition_data = models.JSONField(null=True, blank=True, help_text="Condition data for conditional dependencies")
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'job_dependencies'
        unique_together = ('dependent_job', 'prerequisite_job')
        verbose_name = 'Job Dependency'
        verbose_name_plural = 'Job Dependencies'
    
    def __str__(self):
        return f"{self.prerequisite_job.id} -> {self.dependent_job.id} ({self.dependency_type})"