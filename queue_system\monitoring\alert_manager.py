# queue_system/monitoring/alert_manager.py
"""
Alert Manager for Queue System
Handles alert generation, routing, and notification delivery
"""

import threading
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

from django.utils import timezone
from django.core.cache import cache
from django.core.mail import send_mail
from django.conf import settings

from ..models import QueueAlert

logger = logging.getLogger('queue_system.alert_manager')

class AlertChannel(Enum):
    """Alert delivery channels"""
    EMAIL = "email"
    SMS = "sms"
    WEBHOOK = "webhook"
    SLACK = "slack"
    DATABASE = "database"
    LOG = "log"

class AlertPriority(Enum):
    """Alert priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    condition: Callable[[Dict], bool]
    severity: str
    channels: List[AlertChannel]
    cooldown_minutes: int = 15
    max_alerts_per_hour: int = 10
    escalation_minutes: int = 60
    description: str = ""

@dataclass
class Alert:
    """Alert data structure"""
    id: str
    title: str
    message: str
    severity: str
    alert_type: str
    source: str
    timestamp: datetime
    data: Dict[str, Any]
    channels: List[AlertChannel]
    acknowledged: bool = False
    resolved: bool = False
    escalated: bool = False

class AlertManager:
    """
    Comprehensive alert management system
    Handles alert generation, routing, and delivery
    """
    
    def __init__(self):
        self.cache_prefix = 'alert_manager'
        
        # Alert rules registry
        self.alert_rules: Dict[str, AlertRule] = {}
        self._initialize_default_rules()
        
        # Alert channels
        self.channels: Dict[AlertChannel, Callable] = {}
        self._initialize_channels()
        
        # State management
        self._alert_history: List[Alert] = []
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_counters: Dict[str, List[datetime]] = {}
        
        # Threading
        self._processing_thread = None
        self._shutdown_event = threading.Event()
        self._lock = threading.RLock()
        self._is_running = False
        
        # Statistics
        self._stats = {
            'total_alerts_generated': 0,
            'alerts_sent': 0,
            'alerts_failed': 0,
            'alerts_acknowledged': 0,
            'alerts_escalated': 0,
            'start_time': timezone.now()
        }
        
        logger.info("🚨 Alert Manager initialized")
    
    def _initialize_default_rules(self):
        """Initialize default alert rules"""
        
        # High error rate alert
        self.alert_rules['high_error_rate'] = AlertRule(
            name='High Error Rate',
            condition=lambda data: data.get('error_rate', 0) > 10,
            severity='HIGH',
            channels=[AlertChannel.EMAIL, AlertChannel.DATABASE],
            cooldown_minutes=30,
            description='Error rate exceeds 10%'
        )
        
        # Queue backlog alert
        self.alert_rules['queue_backlog'] = AlertRule(
            name='Queue Backlog',
            condition=lambda data: data.get('total_pending', 0) > 1000,
            severity='CRITICAL',
            channels=[AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.DATABASE],
            cooldown_minutes=15,
            description='Queue backlog exceeds 1000 jobs'
        )
        
        # Worker failure alert
        self.alert_rules['worker_failure'] = AlertRule(
            name='Worker Failure',
            condition=lambda data: data.get('active_workers', 0) < 2,
            severity='CRITICAL',
            channels=[AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.DATABASE],
            cooldown_minutes=10,
            description='Less than 2 active workers'
        )
        
        # Memory usage alert
        self.alert_rules['high_memory_usage'] = AlertRule(
            name='High Memory Usage',
            condition=lambda data: data.get('memory_usage', 0) > 90,
            severity='HIGH',
            channels=[AlertChannel.EMAIL, AlertChannel.DATABASE],
            cooldown_minutes=20,
            description='Memory usage exceeds 90%'
        )
        
        # Disk space alert
        self.alert_rules['low_disk_space'] = AlertRule(
            name='Low Disk Space',
            condition=lambda data: data.get('disk_free_percent', 100) < 10,
            severity='CRITICAL',
            channels=[AlertChannel.EMAIL, AlertChannel.SMS, AlertChannel.DATABASE],
            cooldown_minutes=60,
            description='Disk space below 10%'
        )
        
        logger.info(f"📋 Initialized {len(self.alert_rules)} default alert rules")
    
    def _initialize_channels(self):
        """Initialize alert delivery channels"""
        self.channels = {
            AlertChannel.EMAIL: self._send_email_alert,
            AlertChannel.SMS: self._send_sms_alert,
            AlertChannel.WEBHOOK: self._send_webhook_alert,
            AlertChannel.SLACK: self._send_slack_alert,
            AlertChannel.DATABASE: self._save_database_alert,
            AlertChannel.LOG: self._log_alert
        }
    
    def start_processing(self):
        """Start alert processing"""
        if self._is_running:
            logger.warning("⚠️ Alert processing already running")
            return
        
        self._processing_thread = threading.Thread(
            target=self._processing_loop,
            name="AlertProcessor",
            daemon=True
        )
        self._processing_thread.start()
        self._is_running = True
        
        logger.info("✅ Alert processing started")
    
    def _processing_loop(self):
        """Main alert processing loop"""
        while not self._shutdown_event.is_set():
            try:
                # Check for escalations
                self._check_escalations()
                
                # Clean up old alerts
                self._cleanup_old_alerts()
                
                # Clean up counters
                self._cleanup_old_counters()
                
            except Exception as e:
                logger.error(f"❌ Error in alert processing loop: {str(e)}")
            
            # Wait before next check
            self._shutdown_event.wait(60)  # Check every minute
    
    def check_conditions(self, data: Dict[str, Any], source: str = "system"):
        """
        Check alert conditions against provided data
        
        Args:
            data: Data to check against alert rules
            source: Source of the data
        """
        try:
            for rule_name, rule in self.alert_rules.items():
                try:
                    if rule.condition(data):
                        self._trigger_alert(rule_name, rule, data, source)
                except Exception as e:
                    logger.error(f"❌ Error checking rule {rule_name}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"❌ Error checking alert conditions: {str(e)}")
    
    def _trigger_alert(self, rule_name: str, rule: AlertRule, data: Dict, source: str):
        """Trigger alert if conditions are met"""
        try:
            # Check cooldown
            if not self._check_cooldown(rule_name, rule.cooldown_minutes):
                logger.debug(f"🕒 Alert {rule_name} in cooldown period")
                return
            
            # Check rate limiting
            if not self._check_rate_limit(rule_name, rule.max_alerts_per_hour):
                logger.warning(f"⚠️ Alert {rule_name} rate limited")
                return
            
            # Create alert
            alert = Alert(
                id=f"{rule_name}_{int(timezone.now().timestamp())}",
                title=rule.name,
                message=self._generate_alert_message(rule, data),
                severity=rule.severity,
                alert_type=rule_name,
                source=source,
                timestamp=timezone.now(),
                data=data.copy(),
                channels=rule.channels.copy()
            )
            
            # Store alert
            with self._lock:
                self._active_alerts[alert.id] = alert
                self._alert_history.append(alert)
            
            # Send through channels
            self._send_alert(alert)
            
            # Update statistics
            self._stats['total_alerts_generated'] += 1
            
            # Update rate limiting counter
            self._update_alert_counter(rule_name)
            
            logger.warning(f"🚨 Alert triggered: {rule.name} - {rule.severity}")
            
        except Exception as e:
            logger.error(f"❌ Error triggering alert {rule_name}: {str(e)}")
    
    def _generate_alert_message(self, rule: AlertRule, data: Dict) -> str:
        """Generate detailed alert message"""
        base_message = rule.description
        
        # Add relevant data to message
        details = []
        if 'error_rate' in data:
            details.append(f"Error rate: {data['error_rate']:.1f}%")
        if 'total_pending' in data:
            details.append(f"Pending jobs: {data['total_pending']}")
        if 'active_workers' in data:
            details.append(f"Active workers: {data['active_workers']}")
        if 'memory_usage' in data:
            details.append(f"Memory usage: {data['memory_usage']:.1f}%")
        if 'disk_free_percent' in data:
            details.append(f"Free disk space: {data['disk_free_percent']:.1f}%")
        
        if details:
            return f"{base_message}. Details: {', '.join(details)}"
        
        return base_message
    
    def _check_cooldown(self, rule_name: str, cooldown_minutes: int) -> bool:
        """Check if alert is in cooldown period"""
        try:
            cache_key = f"{self.cache_prefix}_cooldown_{rule_name}"
            last_alert_time = cache.get(cache_key)
            
            if last_alert_time:
                last_time = datetime.fromisoformat(last_alert_time)
                cooldown_end = last_time + timedelta(minutes=cooldown_minutes)
                
                if timezone.now() < cooldown_end:
                    return False
            
            # Update cooldown
            cache.set(cache_key, timezone.now().isoformat(), timeout=cooldown_minutes * 60)
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking cooldown: {str(e)}")
            return True  # Allow alert if check fails
    
    def _check_rate_limit(self, rule_name: str, max_per_hour: int) -> bool:
        """Check rate limiting for alert rule"""
        try:
            now = timezone.now()
            one_hour_ago = now - timedelta(hours=1)
            
            if rule_name not in self._alert_counters:
                self._alert_counters[rule_name] = []
            
            # Clean old entries
            self._alert_counters[rule_name] = [
                timestamp for timestamp in self._alert_counters[rule_name]
                if timestamp > one_hour_ago
            ]
            
            # Check limit
            return len(self._alert_counters[rule_name]) < max_per_hour
            
        except Exception as e:
            logger.error(f"❌ Error checking rate limit: {str(e)}")
            return True  # Allow alert if check fails
    
    def _update_alert_counter(self, rule_name: str):
        """Update alert counter for rate limiting"""
        try:
            if rule_name not in self._alert_counters:
                self._alert_counters[rule_name] = []
            
            self._alert_counters[rule_name].append(timezone.now())
            
        except Exception as e:
            logger.error(f"❌ Error updating alert counter: {str(e)}")
    
    def _send_alert(self, alert: Alert):
        """Send alert through configured channels"""
        for channel in alert.channels:
            try:
                channel_handler = self.channels.get(channel)
                if channel_handler:
                    success = channel_handler(alert)
                    if success:
                        self._stats['alerts_sent'] += 1
                    else:
                        self._stats['alerts_failed'] += 1
                else:
                    logger.warning(f"⚠️ No handler for channel: {channel.value}")
                    
            except Exception as e:
                logger.error(f"❌ Error sending alert via {channel.value}: {str(e)}")
                self._stats['alerts_failed'] += 1
    
    def _send_email_alert(self, alert: Alert) -> bool:
        """Send alert via email"""
        try:
            recipients = getattr(settings, 'ALERT_EMAIL_RECIPIENTS', [])
            if not recipients:
                logger.warning("⚠️ No email recipients configured for alerts")
                return False
            
            subject = f"[{alert.severity}] PhotoFish Alert: {alert.title}"
            message = f"""
Alert Details:
- Type: {alert.alert_type}
- Severity: {alert.severity}
- Source: {alert.source}
- Time: {alert.timestamp}
- Message: {alert.message}

Data: {alert.data}

Alert ID: {alert.id}
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=recipients,
                fail_silently=False
            )
            
            logger.info(f"📧 Email alert sent: {alert.title}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending email alert: {str(e)}")
            return False
    
    def _send_sms_alert(self, alert: Alert) -> bool:
        """Send alert via SMS"""
        try:
            # SMS implementation would depend on your SMS provider
            # This is a placeholder implementation
            logger.info(f"📱 SMS alert would be sent: {alert.title}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending SMS alert: {str(e)}")
            return False
    
    def _send_webhook_alert(self, alert: Alert) -> bool:
        """Send alert via webhook"""
        try:
            import requests
            
            webhook_url = getattr(settings, 'ALERT_WEBHOOK_URL', None)
            if not webhook_url:
                return False
            
            payload = {
                'alert_id': alert.id,
                'title': alert.title,
                'message': alert.message,
                'severity': alert.severity,
                'alert_type': alert.alert_type,
                'source': alert.source,
                'timestamp': alert.timestamp.isoformat(),
                'data': alert.data
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
            logger.info(f"🔗 Webhook alert sent: {alert.title}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending webhook alert: {str(e)}")
            return False
    
    def _send_slack_alert(self, alert: Alert) -> bool:
        """Send alert via Slack"""
        try:
            # Slack implementation would use Slack API
            # This is a placeholder implementation
            logger.info(f"💬 Slack alert would be sent: {alert.title}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error sending Slack alert: {str(e)}")
            return False
    
    def _save_database_alert(self, alert: Alert) -> bool:
        """Save alert to database"""
        try:
            QueueAlert.objects.create(
                alert_type=alert.alert_type.upper(),
                severity=alert.severity.upper(),
                title=alert.title,
                message=alert.message,
                details=alert.data
            )
            
            logger.info(f"💾 Database alert saved: {alert.title}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error saving database alert: {str(e)}")
            return False
    
    def _log_alert(self, alert: Alert) -> bool:
        """Log alert to application logs"""
        try:
            log_message = f"ALERT [{alert.severity}] {alert.title}: {alert.message}"
            
            if alert.severity == 'CRITICAL':
                logger.critical(log_message)
            elif alert.severity == 'HIGH':
                logger.error(log_message)
            elif alert.severity == 'MEDIUM':
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error logging alert: {str(e)}")
            return False
    
    def _check_escalations(self):
        """Check for alerts that need escalation"""
        try:
            with self._lock:
                for alert in self._active_alerts.values():
                    if (not alert.escalated and not alert.acknowledged and not alert.resolved):
                        # Get rule for escalation time
                        rule = self.alert_rules.get(alert.alert_type)
                        if rule:
                            escalation_time = alert.timestamp + timedelta(minutes=rule.escalation_minutes)
                            
                            if timezone.now() > escalation_time:
                                self._escalate_alert(alert)
                                
        except Exception as e:
            logger.error(f"❌ Error checking escalations: {str(e)}")
    
    def _escalate_alert(self, alert: Alert):
        """Escalate unacknowledged alert"""
        try:
            alert.escalated = True
            
            # Send escalation notification
            escalation_alert = Alert(
                id=f"{alert.id}_escalation",
                title=f"ESCALATED: {alert.title}",
                message=f"Alert has been escalated due to no acknowledgment. Original: {alert.message}",
                severity='CRITICAL',
                alert_type='ESCALATION',
                source=alert.source,
                timestamp=timezone.now(),
                data=alert.data,
                channels=[AlertChannel.EMAIL, AlertChannel.SMS]
            )
            
            self._send_alert(escalation_alert)
            self._stats['alerts_escalated'] += 1
            
            logger.critical(f"🚨 Alert escalated: {alert.title}")
            
        except Exception as e:
            logger.error(f"❌ Error escalating alert: {str(e)}")
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "system"):
        """Acknowledge an alert"""
        try:
            with self._lock:
                if alert_id in self._active_alerts:
                    alert = self._active_alerts[alert_id]
                    alert.acknowledged = True
                    
                    self._stats['alerts_acknowledged'] += 1
                    
                    logger.info(f"✅ Alert acknowledged: {alert.title} by {acknowledged_by}")
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"❌ Error acknowledging alert: {str(e)}")
            return False
    
    def resolve_alert(self, alert_id: str, resolved_by: str = "system"):
        """Resolve an alert"""
        try:
            with self._lock:
                if alert_id in self._active_alerts:
                    alert = self._active_alerts[alert_id]
                    alert.resolved = True
                    
                    # Remove from active alerts
                    del self._active_alerts[alert_id]
                    
                    logger.info(f"✅ Alert resolved: {alert.title} by {resolved_by}")
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"❌ Error resolving alert: {str(e)}")
            return False
    
    def _cleanup_old_alerts(self):
        """Clean up old alert history"""
        try:
            cutoff_time = timezone.now() - timedelta(days=7)
            
            with self._lock:
                self._alert_history = [
                    alert for alert in self._alert_history
                    if alert.timestamp > cutoff_time
                ]
                
        except Exception as e:
            logger.error(f"❌ Error cleaning up old alerts: {str(e)}")
    
    def _cleanup_old_counters(self):
        """Clean up old rate limiting counters"""
        try:
            one_hour_ago = timezone.now() - timedelta(hours=1)
            
            for rule_name in list(self._alert_counters.keys()):
                self._alert_counters[rule_name] = [
                    timestamp for timestamp in self._alert_counters[rule_name]
                    if timestamp > one_hour_ago
                ]
                
                # Remove empty counters
                if not self._alert_counters[rule_name]:
                    del self._alert_counters[rule_name]
                    
        except Exception as e:
            logger.error(f"❌ Error cleaning up old counters: {str(e)}")
    
    # Public API methods
    
    def add_alert_rule(self, name: str, rule: AlertRule):
        """Add custom alert rule"""
        try:
            self.alert_rules[name] = rule
            logger.info(f"📝 Added alert rule: {name}")
        except Exception as e:
            logger.error(f"❌ Error adding alert rule: {str(e)}")
    
    def remove_alert_rule(self, name: str):
        """Remove alert rule"""
        try:
            if name in self.alert_rules:
                del self.alert_rules[name]
                logger.info(f"🗑️ Removed alert rule: {name}")
        except Exception as e:
            logger.error(f"❌ Error removing alert rule: {str(e)}")
    
    def get_active_alerts(self) -> List[Dict]:
        """Get list of active alerts"""
        try:
            with self._lock:
                return [asdict(alert) for alert in self._active_alerts.values()]
        except Exception as e:
            logger.error(f"❌ Error getting active alerts: {str(e)}")
            return []
    
    def get_alert_history(self, hours: int = 24) -> List[Dict]:
        """Get alert history for specified hours"""
        try:
            cutoff_time = timezone.now() - timedelta(hours=hours)
            
            with self._lock:
                recent_alerts = [
                    asdict(alert) for alert in self._alert_history
                    if alert.timestamp > cutoff_time
                ]
            
            return sorted(recent_alerts, key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            logger.error(f"❌ Error getting alert history: {str(e)}")
            return []
    
    def get_alert_stats(self) -> Dict:
        """Get alert manager statistics"""
        try:
            uptime_hours = (timezone.now() - self._stats['start_time']).total_seconds() / 3600
            
            stats = self._stats.copy()
            stats['uptime_hours'] = uptime_hours
            
            with self._lock:
                stats['active_alert_count'] = len(self._active_alerts)
                stats['total_rules'] = len(self.alert_rules)
            
            # Calculate rates
            if uptime_hours > 0:
                stats['alerts_per_hour'] = self._stats['total_alerts_generated'] / uptime_hours
            else:
                stats['alerts_per_hour'] = 0
            
            # Calculate success rate
            total_sent = self._stats['alerts_sent'] + self._stats['alerts_failed']
            if total_sent > 0:
                stats['success_rate'] = (self._stats['alerts_sent'] / total_sent) * 100
            else:
                stats['success_rate'] = 100
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error getting alert stats: {str(e)}")
            return {'error': str(e)}
    
    def shutdown(self):
        """Shutdown alert manager"""
        logger.info("🛑 Shutting down alert manager")
        
        self._shutdown_event.set()
        self._is_running = False
        
        # Wait for processing thread
        if self._processing_thread and self._processing_thread.is_alive():
            self._processing_thread.join(timeout=5)
        
        logger.info("✅ Alert manager shutdown complete")

# Global alert manager instance
alert_manager = AlertManager()

def get_alert_manager() -> AlertManager:
    """Get global alert manager instance"""
    return alert_manager