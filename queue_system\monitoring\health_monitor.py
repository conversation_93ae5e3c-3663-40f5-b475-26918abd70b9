# queue_system/monitoring/health_monitor.py
"""
Health Monitor for PhotoFish Enhanced Queue System
Monitors overall system health and component availability
"""

import logging
import threading
import time
import requests
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from django.conf import settings
from django.core.cache import cache
from django.db import connection
from django.utils import timezone

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class ComponentHealth:
    """Health status of a system component"""
    component_name: str
    status: HealthStatus
    message: str
    response_time: float
    last_check: datetime
    metadata: Dict[str, Any]
    dependencies: List[str]

@dataclass
class HealthCheckResult:
    """Result of a health check operation"""
    overall_status: HealthStatus
    components: Dict[str, ComponentHealth]
    total_checks: int
    healthy_checks: int
    issues: List[str]
    recommendations: List[str]
    timestamp: datetime

class HealthMonitor:
    """
    Comprehensive health monitoring for the queue system
    """
    
    def __init__(self):
        # Health check configuration
        self.health_checks = {}
        self.is_monitoring = False
        self.monitoring_thread = None
        self._lock = threading.RLock()
        
        # Health status tracking
        self.component_status = {}
        self.health_history = []
        self.max_history_size = 1000
        
        # Monitoring configuration
        self.check_interval = 30  # seconds
        self.timeout = 10  # seconds for health checks
        self.failure_threshold = 3  # consecutive failures before marking unhealthy
        
        # Component failure tracking
        self.failure_counts = {}
        self.last_check_times = {}
        
        # Alert callbacks
        self.health_callbacks = []
        
        # Initialize built-in health checks
        self._initialize_health_checks()
    
    def start(self) -> bool:
        """
        Start health monitoring
        
        Returns:
            Success status
        """
        try:
            if self.is_monitoring:
                logger.warning("Health monitoring is already running")
                return True
            
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="HealthMonitor"
            )
            self.monitoring_thread.start()
            
            logger.info("Health monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting health monitoring: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """
        Stop health monitoring
        
        Returns:
            Success status
        """
        try:
            self.is_monitoring = False
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=10)
            
            logger.info("Health monitoring stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping health monitoring: {str(e)}")
            return False
    
    def check_overall_health(self) -> HealthCheckResult:
        """
        Perform comprehensive health check of all components
        
        Returns:
            Overall health check result
        """
        try:
            start_time = time.time()
            components = {}
            issues = []
            recommendations = []
            
            # Check each registered component
            for component_name, check_func in self.health_checks.items():
                try:
                    component_health = self._check_component_health(component_name, check_func)
                    components[component_name] = component_health
                    
                    # Collect issues
                    if component_health.status in [HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]:
                        issues.append(f"{component_name}: {component_health.message}")
                    elif component_health.status == HealthStatus.DEGRADED:
                        recommendations.append(f"Monitor {component_name}: {component_health.message}")
                        
                except Exception as e:
                    logger.error(f"Error checking {component_name} health: {str(e)}")
                    components[component_name] = ComponentHealth(
                        component_name=component_name,
                        status=HealthStatus.UNKNOWN,
                        message=f"Health check failed: {str(e)}",
                        response_time=0.0,
                        last_check=datetime.now(),
                        metadata={'error': str(e)},
                        dependencies=[]
                    )
                    issues.append(f"{component_name}: Health check failed")
            
            # Determine overall status
            overall_status = self._calculate_overall_status(components)
            
            # Create result
            result = HealthCheckResult(
                overall_status=overall_status,
                components=components,
                total_checks=len(components),
                healthy_checks=len([c for c in components.values() if c.status == HealthStatus.HEALTHY]),
                issues=issues,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            # Store in history
            with self._lock:
                self.health_history.append(result)
                if len(self.health_history) > self.max_history_size:
                    self.health_history = self.health_history[-self.max_history_size:]
            
            # Trigger callbacks if status changed
            self._trigger_health_callbacks(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in overall health check: {str(e)}")
            return HealthCheckResult(
                overall_status=HealthStatus.UNKNOWN,
                components={},
                total_checks=0,
                healthy_checks=0,
                issues=[f"Health check system error: {str(e)}"],
                recommendations=[],
                timestamp=datetime.now()
            )
    
    def register_health_check(self, component_name: str, check_function: Callable[[], Dict[str, Any]]) -> bool:
        """
        Register a custom health check
        
        Args:
            component_name: Name of the component
            check_function: Function that returns health status
            
        Returns:
            Success status
        """
        try:
            self.health_checks[component_name] = check_function
            logger.info(f"Registered health check for {component_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering health check for {component_name}: {str(e)}")
            return False
    
    def get_component_health(self, component_name: str) -> Optional[ComponentHealth]:
        """
        Get health status of a specific component
        
        Args:
            component_name: Component to check
            
        Returns:
            Component health or None if not found
        """
        try:
            if component_name in self.health_checks:
                check_func = self.health_checks[component_name]
                return self._check_component_health(component_name, check_func)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting health for {component_name}: {str(e)}")
            return None
    
    def get_health_trends(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get health trends over specified time period
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Health trend analysis
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Filter health history
            with self._lock:
                recent_checks = [
                    check for check in self.health_history
                    if check.timestamp > cutoff_time
                ]
            
            if not recent_checks:
                return {'error': 'Insufficient data for trend analysis'}
            
            # Analyze trends
            trends = {
                'period_hours': hours,
                'total_checks': len(recent_checks),
                'availability_percentage': 0.0,
                'component_trends': {},
                'incidents': [],
                'recovery_times': []
            }
            
            # Calculate overall availability
            healthy_checks = len([
                check for check in recent_checks
                if check.overall_status == HealthStatus.HEALTHY
            ])
            trends['availability_percentage'] = (healthy_checks / len(recent_checks)) * 100
            
            # Analyze component trends
            for component_name in self.health_checks.keys():
                component_statuses = []
                for check in recent_checks:
                    if component_name in check.components:
                        component_statuses.append(check.components[component_name].status)
                
                if component_statuses:
                    healthy_count = len([s for s in component_statuses if s == HealthStatus.HEALTHY])
                    trends['component_trends'][component_name] = {
                        'availability_percentage': (healthy_count / len(component_statuses)) * 100,
                        'total_checks': len(component_statuses),
                        'current_status': component_statuses[-1].value if component_statuses else 'unknown'
                    }
            
            # Identify incidents (periods of degraded/unhealthy status)
            incidents = self._identify_health_incidents(recent_checks)
            trends['incidents'] = incidents
            
            return trends
            
        except Exception as e:
            logger.error(f"Error getting health trends: {str(e)}")
            return {'error': str(e)}
    
    def add_health_callback(self, callback: Callable[[HealthCheckResult], None]):
        """Add callback for health status changes"""
        self.health_callbacks.append(callback)
    
    def force_health_check(self) -> HealthCheckResult:
        """Force an immediate health check of all components"""
        return self.check_overall_health()
    
    def _monitoring_loop(self):
        """Main health monitoring loop"""
        logger.info("Health monitoring loop started")
        
        while self.is_monitoring:
            try:
                # Perform health check
                result = self.check_overall_health()
                
                # Log critical issues
                if result.overall_status == HealthStatus.CRITICAL:
                    logger.critical(f"System health is CRITICAL: {', '.join(result.issues)}")
                elif result.overall_status == HealthStatus.UNHEALTHY:
                    logger.error(f"System health is UNHEALTHY: {', '.join(result.issues)}")
                elif result.overall_status == HealthStatus.DEGRADED:
                    logger.warning(f"System health is DEGRADED: {', '.join(result.issues)}")
                
                # Sleep until next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {str(e)}")
                time.sleep(60)  # Wait before retrying
    
    def _check_component_health(self, component_name: str, check_func: Callable) -> ComponentHealth:
        """Check health of a single component"""
        start_time = time.time()
        
        try:
            # Execute health check with timeout
            result = check_func()
            response_time = time.time() - start_time
            
            # Parse result
            status_str = result.get('status', 'unknown')
            try:
                status = HealthStatus(status_str.lower())
            except ValueError:
                status = HealthStatus.UNKNOWN
            
            # Track failures
            if status in [HealthStatus.UNHEALTHY, HealthStatus.CRITICAL]:
                self.failure_counts[component_name] = self.failure_counts.get(component_name, 0) + 1
            else:
                self.failure_counts[component_name] = 0
            
            # Create component health
            component_health = ComponentHealth(
                component_name=component_name,
                status=status,
                message=result.get('message', 'Health check completed'),
                response_time=response_time,
                last_check=datetime.now(),
                metadata=result.get('metadata', {}),
                dependencies=result.get('dependencies', [])
            )
            
            # Store status
            self.component_status[component_name] = component_health
            self.last_check_times[component_name] = datetime.now()
            
            return component_health
            
        except Exception as e:
            response_time = time.time() - start_time
            
            # Increment failure count
            self.failure_counts[component_name] = self.failure_counts.get(component_name, 0) + 1
            
            return ComponentHealth(
                component_name=component_name,
                status=HealthStatus.CRITICAL,
                message=f"Health check failed: {str(e)}",
                response_time=response_time,
                last_check=datetime.now(),
                metadata={'error': str(e), 'failure_count': self.failure_counts[component_name]},
                dependencies=[]
            )
    
    def _calculate_overall_status(self, components: Dict[str, ComponentHealth]) -> HealthStatus:
        """Calculate overall system health status"""
        try:
            if not components:
                return HealthStatus.UNKNOWN
            
            statuses = [comp.status for comp in components.values()]
            
            # Critical if any component is critical
            if HealthStatus.CRITICAL in statuses:
                return HealthStatus.CRITICAL
            
            # Unhealthy if any core component is unhealthy
            core_components = ['database', 'queue_manager', 'worker_pool']
            for comp_name, comp in components.items():
                if comp_name in core_components and comp.status == HealthStatus.UNHEALTHY:
                    return HealthStatus.UNHEALTHY
            
            # Degraded if any component is unhealthy or degraded
            if HealthStatus.UNHEALTHY in statuses or HealthStatus.DEGRADED in statuses:
                return HealthStatus.DEGRADED
            
            # Unknown if any component status is unknown
            if HealthStatus.UNKNOWN in statuses:
                return HealthStatus.UNKNOWN
            
            # Healthy if all components are healthy
            return HealthStatus.HEALTHY
            
        except Exception as e:
            logger.error(f"Error calculating overall status: {str(e)}")
            return HealthStatus.UNKNOWN
    
    def _trigger_health_callbacks(self, result: HealthCheckResult):
        """Trigger registered health callbacks"""
        try:
            for callback in self.health_callbacks:
                try:
                    callback(result)
                except Exception as e:
                    logger.error(f"Error in health callback: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error triggering health callbacks: {str(e)}")
    
    def _identify_health_incidents(self, health_checks: List[HealthCheckResult]) -> List[Dict[str, Any]]:
        """Identify health incidents from check history"""
        incidents = []
        
        try:
            current_incident = None
            
            for check in health_checks:
                is_incident = check.overall_status in [
                    HealthStatus.DEGRADED, HealthStatus.UNHEALTHY, HealthStatus.CRITICAL
                ]
                
                if is_incident and not current_incident:
                    # Start new incident
                    current_incident = {
                        'start_time': check.timestamp,
                        'severity': check.overall_status.value,
                        'issues': check.issues.copy()
                    }
                elif is_incident and current_incident:
                    # Update ongoing incident
                    current_incident['end_time'] = check.timestamp
                    current_incident['issues'].extend(check.issues)
                    # Update severity to worst seen
                    if check.overall_status == HealthStatus.CRITICAL:
                        current_incident['severity'] = 'critical'
                    elif check.overall_status == HealthStatus.UNHEALTHY and current_incident['severity'] != 'critical':
                        current_incident['severity'] = 'unhealthy'
                elif not is_incident and current_incident:
                    # End current incident
                    current_incident['end_time'] = check.timestamp
                    current_incident['duration_minutes'] = (
                        current_incident['end_time'] - current_incident['start_time']
                    ).total_seconds() / 60
                    current_incident['resolved'] = True
                    incidents.append(current_incident)
                    current_incident = None
            
            # Handle ongoing incident
            if current_incident:
                current_incident['end_time'] = datetime.now()
                current_incident['duration_minutes'] = (
                    current_incident['end_time'] - current_incident['start_time']
                ).total_seconds() / 60
                current_incident['resolved'] = False
                incidents.append(current_incident)
            
            return incidents
            
        except Exception as e:
            logger.error(f"Error identifying health incidents: {str(e)}")
            return []
    
    def _initialize_health_checks(self):
        """Initialize built-in health checks"""
        try:
            # Database health check
            self.register_health_check('database', self._check_database_health)
            
            # Queue manager health check
            self.register_health_check('queue_manager', self._check_queue_manager_health)
            
            # Worker pool health check
            self.register_health_check('worker_pool', self._check_worker_pool_health)
            
            # Cache health check
            self.register_health_check('cache', self._check_cache_health)
            
            # Disk space health check
            self.register_health_check('disk_space', self._check_disk_space_health)
            
            # Memory health check
            self.register_health_check('memory', self._check_memory_health)
            
            logger.info("Initialized built-in health checks")
            
        except Exception as e:
            logger.error(f"Error initializing health checks: {str(e)}")
    
    # ==================== BUILT-IN HEALTH CHECKS ====================
    
    def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            response_time = time.time() - start_time
            
            if response_time > 5.0:
                return {
                    'status': 'degraded',
                    'message': f'Database response slow: {response_time:.2f}s',
                    'metadata': {'response_time': response_time}
                }
            elif response_time > 10.0:
                return {
                    'status': 'unhealthy',
                    'message': f'Database response very slow: {response_time:.2f}s',
                    'metadata': {'response_time': response_time}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Database responsive: {response_time:.2f}s',
                    'metadata': {'response_time': response_time}
                }
                
        except Exception as e:
            return {
                'status': 'critical',
                'message': f'Database connection failed: {str(e)}',
                'metadata': {'error': str(e)}
            }
    
    def _check_queue_manager_health(self) -> Dict[str, Any]:
        """Check queue manager status"""
        try:
            from ..queue_engine.queue_manager import QueueManager
            
            # This would check if queue manager is running and responsive
            # For now, return a basic check
            
            return {
                'status': 'healthy',
                'message': 'Queue manager operational',
                'metadata': {}
            }
            
        except Exception as e:
            return {
                'status': 'critical',
                'message': f'Queue manager check failed: {str(e)}',
                'metadata': {'error': str(e)}
            }
    
    def _check_worker_pool_health(self) -> Dict[str, Any]:
        """Check worker pool status"""
        try:
            from ..models import WorkerStatus
            
            # Check active workers
            active_workers = WorkerStatus.objects.filter(
                status__in=['idle', 'active', 'busy'],
                last_heartbeat__gt=timezone.now() - timedelta(minutes=5)
            ).count()
            
            total_workers = WorkerStatus.objects.count()
            
            if active_workers == 0:
                return {
                    'status': 'critical',
                    'message': 'No active workers available',
                    'metadata': {'active_workers': 0, 'total_workers': total_workers}
                }
            elif active_workers < total_workers * 0.5:
                return {
                    'status': 'degraded',
                    'message': f'Low worker availability: {active_workers}/{total_workers}',
                    'metadata': {'active_workers': active_workers, 'total_workers': total_workers}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Workers available: {active_workers}/{total_workers}',
                    'metadata': {'active_workers': active_workers, 'total_workers': total_workers}
                }
                
        except Exception as e:
            return {
                'status': 'critical',
                'message': f'Worker pool check failed: {str(e)}',
                'metadata': {'error': str(e)}
            }
    
    def _check_cache_health(self) -> Dict[str, Any]:
        """Check cache system health"""
        try:
            start_time = time.time()
            
            # Test cache operations
            test_key = 'health_check_test'
            test_value = str(datetime.now())
            
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            cache.delete(test_key)
            
            response_time = time.time() - start_time
            
            if retrieved_value != test_value:
                return {
                    'status': 'unhealthy',
                    'message': 'Cache read/write test failed',
                    'metadata': {'response_time': response_time}
                }
            elif response_time > 1.0:
                return {
                    'status': 'degraded',
                    'message': f'Cache response slow: {response_time:.2f}s',
                    'metadata': {'response_time': response_time}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Cache operational: {response_time:.2f}s',
                    'metadata': {'response_time': response_time}
                }
                
        except Exception as e:
            return {
                'status': 'critical',
                'message': f'Cache check failed: {str(e)}',
                'metadata': {'error': str(e)}
            }
    
    def _check_disk_space_health(self) -> Dict[str, Any]:
        """Check disk space availability"""
        try:
            import shutil
            
            total, used, free = shutil.disk_usage('/')
            used_percent = (used / total) * 100
            
            if used_percent > 95:
                return {
                    'status': 'critical',
                    'message': f'Disk space critical: {used_percent:.1f}% used',
                    'metadata': {'used_percent': used_percent, 'free_gb': free / (1024**3)}
                }
            elif used_percent > 85:
                return {
                    'status': 'degraded',
                    'message': f'Disk space low: {used_percent:.1f}% used',
                    'metadata': {'used_percent': used_percent, 'free_gb': free / (1024**3)}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Disk space available: {used_percent:.1f}% used',
                    'metadata': {'used_percent': used_percent, 'free_gb': free / (1024**3)}
                }
                
        except Exception as e:
            return {
                'status': 'unknown',
                'message': f'Disk space check failed: {str(e)}',
                'metadata': {'error': str(e)}
            }
    
    def _check_memory_health(self) -> Dict[str, Any]:
        """Check system memory health"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            
            if memory.percent > 95:
                return {
                    'status': 'critical',
                    'message': f'Memory usage critical: {memory.percent:.1f}%',
                    'metadata': {'used_percent': memory.percent, 'available_gb': memory.available / (1024**3)}
                }
            elif memory.percent > 85:
                return {
                    'status': 'degraded',
                    'message': f'Memory usage high: {memory.percent:.1f}%',
                    'metadata': {'used_percent': memory.percent, 'available_gb': memory.available / (1024**3)}
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'Memory usage normal: {memory.percent:.1f}%',
                    'metadata': {'used_percent': memory.percent, 'available_gb': memory.available / (1024**3)}
                }
                
        except Exception as e:
            return {
                'status': 'unknown',
                'message': f'Memory check failed: {str(e)}',
                'metadata': {'error': str(e)}
            }