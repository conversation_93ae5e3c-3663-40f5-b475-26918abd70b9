# queue_system/monitoring/metrics_collector.py
"""
Metrics Collector for PhotoFish Enhanced Queue System
Collects and aggregates system performance metrics
"""

import logging
import threading
import time
import psutil
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque, defaultdict
from enum import Enum
from django.conf import settings
from django.core.cache import cache
from django.db.models import Count, Avg, Max, Min
from django.utils import timezone

logger = logging.getLogger(__name__)

class MetricType(Enum):
    """Types of metrics that can be collected"""
    COUNTER = "counter"          # Incremental values (total jobs processed)
    GAUGE = "gauge"             # Point-in-time values (current queue length)
    HISTOGRAM = "histogram"     # Distribution of values (response times)
    RATE = "rate"              # Rate of change (jobs per second)

@dataclass
class MetricData:
    """Individual metric data point"""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    tags: Dict[str, str]
    timestamp: datetime
    source: str

@dataclass
class AggregatedMetric:
    """Aggregated metric over time period"""
    name: str
    metric_type: MetricType
    count: int
    sum_value: float
    min_value: float
    max_value: float
    avg_value: float
    percentile_95: float
    percentile_99: float
    start_time: datetime
    end_time: datetime

class MetricsCollector:
    """
    Comprehensive metrics collection system for queue performance monitoring
    """
    
    def __init__(self):
        # Metrics storage
        self.metrics_buffer = deque(maxlen=10000)
        self.aggregated_metrics = {}
        self.metric_counters = defaultdict(int)
        self.metric_rates = defaultdict(list)
        
        # Collection state
        self.is_collecting = False
        self.collection_thread = None
        self._lock = threading.RLock()
        
        # Configuration
        self.collection_interval = 10  # seconds
        self.aggregation_window = 300  # 5 minutes
        self.retention_period = 86400  # 24 hours
        
        # Metric definitions
        self.metric_definitions = self._initialize_metric_definitions()
        
        # Performance tracking
        self.last_collection_time = datetime.now()
        self.collection_stats = {
            'total_metrics_collected': 0,
            'collection_errors': 0,
            'last_error': None
        }
    
    def start_collection(self) -> bool:
        """
        Start metrics collection
        
        Returns:
            Success status
        """
        try:
            if self.is_collecting:
                logger.warning("Metrics collection is already running")
                return True
            
            self.is_collecting = True
            self.collection_thread = threading.Thread(
                target=self._collection_loop,
                daemon=True,
                name="MetricsCollector"
            )
            self.collection_thread.start()
            
            logger.info("Metrics collection started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting metrics collection: {str(e)}")
            return False
    
    def stop_collection(self) -> bool:
        """
        Stop metrics collection
        
        Returns:
            Success status
        """
        try:
            self.is_collecting = False
            
            if self.collection_thread and self.collection_thread.is_alive():
                self.collection_thread.join(timeout=10)
            
            logger.info("Metrics collection stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping metrics collection: {str(e)}")
            return False
    
    def record_metric(self, name: str, value: Union[int, float], 
                     metric_type: MetricType, tags: Dict[str, str] = None,
                     source: str = "queue_system") -> bool:
        """
        Record a single metric
        
        Args:
            name: Metric name
            value: Metric value
            metric_type: Type of metric
            tags: Optional tags for metric
            source: Source component
            
        Returns:
            Success status
        """
        try:
            metric = MetricData(
                name=name,
                value=value,
                metric_type=metric_type,
                tags=tags or {},
                timestamp=datetime.now(),
                source=source
            )
            
            with self._lock:
                self.metrics_buffer.append(metric)
                
                # Update counters for rate calculations
                if metric_type == MetricType.COUNTER:
                    self.metric_counters[name] += value
                
                # Track rates
                if metric_type == MetricType.RATE:
                    self.metric_rates[name].append((datetime.now(), value))
                    # Keep only recent rate data
                    cutoff_time = datetime.now() - timedelta(minutes=5)
                    self.metric_rates[name] = [
                        (ts, val) for ts, val in self.metric_rates[name]
                        if ts > cutoff_time
                    ]
                
                self.collection_stats['total_metrics_collected'] += 1
            
            return True
            
        except Exception as e:
            logger.error(f"Error recording metric {name}: {str(e)}")
            self.collection_stats['collection_errors'] += 1
            self.collection_stats['last_error'] = str(e)
            return False
    
    def get_current_metrics_by_priority(self) -> Dict[str, Dict[str, Any]]:
        """
        Get current metrics organized by priority level
        
        Returns:
            Metrics by priority level
        """
        try:
            metrics_by_priority = {}
            
            # Get queue statistics from models
            from ..models import QueueJob, WorkerStatus
            
            priorities = ['EMERGENCY', 'HIGH', 'STANDARD', 'LOW', 'MAINTENANCE']
            
            for priority in priorities:
                # Queue metrics
                pending_jobs = QueueJob.objects.filter(
                    priority=priority,
                    status__in=['pending', 'queued']
                ).count()
                
                processing_jobs = QueueJob.objects.filter(
                    priority=priority,
                    status='processing'
                ).count()
                
                # Worker metrics
                active_workers = WorkerStatus.objects.filter(
                    priority_levels__contains=[priority],
                    status__in=['idle', 'active', 'busy']
                ).count()
                
                total_workers = WorkerStatus.objects.filter(
                    priority_levels__contains=[priority]
                ).count()
                
                # Calculate utilization
                utilization = 0.0
                if total_workers > 0:
                    busy_workers = WorkerStatus.objects.filter(
                        priority_levels__contains=[priority],
                        status='busy'
                    ).count()
                    utilization = (busy_workers / total_workers) * 100
                
                # Processing rate
                processing_rate = self._calculate_processing_rate(priority)
                
                # Error rate
                error_rate = self._calculate_error_rate(priority)
                
                metrics_by_priority[priority] = {
                    'queue_length': pending_jobs,
                    'processing_jobs': processing_jobs,
                    'current_workers': total_workers,
                    'active_workers': active_workers,
                    'utilization': utilization,
                    'processing_rate': processing_rate,
                    'error_rate': error_rate,
                    'timestamp': datetime.now().isoformat()
                }
            
            return metrics_by_priority
            
        except Exception as e:
            logger.error(f"Error getting current metrics by priority: {str(e)}")
            return {}
    
    def get_metrics_history(self, metric_name: str, hours: int = 24) -> List[MetricData]:
        """
        Get historical metrics for a specific metric
        
        Args:
            metric_name: Name of the metric
            hours: Number of hours of history to retrieve
            
        Returns:
            List of historical metric data
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            with self._lock:
                filtered_metrics = [
                    metric for metric in self.metrics_buffer
                    if metric.name == metric_name and metric.timestamp > cutoff_time
                ]
            
            return sorted(filtered_metrics, key=lambda m: m.timestamp)
            
        except Exception as e:
            logger.error(f"Error getting metrics history for {metric_name}: {str(e)}")
            return []
    
    def get_aggregated_metrics(self, start_time: datetime, end_time: datetime) -> Dict[str, AggregatedMetric]:
        """
        Get aggregated metrics for a time period
        
        Args:
            start_time: Start of aggregation period
            end_time: End of aggregation period
            
        Returns:
            Aggregated metrics by name
        """
        try:
            aggregated = {}
            
            with self._lock:
                # Filter metrics by time period
                period_metrics = [
                    metric for metric in self.metrics_buffer
                    if start_time <= metric.timestamp <= end_time
                ]
            
            # Group by metric name
            metrics_by_name = defaultdict(list)
            for metric in period_metrics:
                metrics_by_name[metric.name].append(metric)
            
            # Calculate aggregations
            for name, metrics in metrics_by_name.items():
                if not metrics:
                    continue
                
                values = [m.value for m in metrics]
                values.sort()
                
                # Calculate percentiles
                p95_index = int(len(values) * 0.95)
                p99_index = int(len(values) * 0.99)
                
                aggregated[name] = AggregatedMetric(
                    name=name,
                    metric_type=metrics[0].metric_type,
                    count=len(values),
                    sum_value=sum(values),
                    min_value=min(values),
                    max_value=max(values),
                    avg_value=sum(values) / len(values),
                    percentile_95=values[p95_index] if values else 0,
                    percentile_99=values[p99_index] if values else 0,
                    start_time=start_time,
                    end_time=end_time
                )
            
            return aggregated
            
        except Exception as e:
            logger.error(f"Error getting aggregated metrics: {str(e)}")
            return {}
    
    def get_average_processing_rate(self, priority: str, hours: int = 24) -> float:
        """
        Get average processing rate for a priority level
        
        Args:
            priority: Priority level
            hours: Hours to look back
            
        Returns:
            Average processing rate (jobs per minute)
        """
        try:
            # Get completed jobs in time period
            from ..models import QueueJob
            
            cutoff_time = timezone.now() - timedelta(hours=hours)
            
            completed_jobs = QueueJob.objects.filter(
                priority=priority,
                status='completed',
                completed_at__gte=cutoff_time
            ).count()
            
            # Calculate rate
            if hours > 0:
                return completed_jobs / (hours * 60)  # jobs per minute
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating processing rate for {priority}: {str(e)}")
            return 0.0
    
    def check_sustained_low_utilization(self, priority: str, duration_seconds: int) -> bool:
        """
        Check if utilization has been consistently low
        
        Args:
            priority: Priority level to check
            duration_seconds: Duration to check
            
        Returns:
            True if utilization has been consistently low
        """
        try:
            # This would check metrics history for sustained low utilization
            # For now, return a simple check
            
            from ..models import WorkerStatus
            
            # Get current utilization
            total_workers = WorkerStatus.objects.filter(
                priority_levels__contains=[priority]
            ).count()
            
            if total_workers == 0:
                return True
            
            busy_workers = WorkerStatus.objects.filter(
                priority_levels__contains=[priority],
                status='busy'
            ).count()
            
            utilization = (busy_workers / total_workers) * 100
            
            # Simple check - in production this would check historical data
            return utilization < 20.0
            
        except Exception as e:
            logger.error(f"Error checking sustained low utilization: {str(e)}")
            return False
    
    def export_metrics(self, format: str = 'json', start_time: Optional[datetime] = None,
                      end_time: Optional[datetime] = None) -> str:
        """
        Export metrics in specified format
        
        Args:
            format: Export format ('json', 'csv', 'prometheus')
            start_time: Start time for export
            end_time: End time for export
            
        Returns:
            Exported metrics as string
        """
        try:
            # Set default time range
            if not end_time:
                end_time = datetime.now()
            if not start_time:
                start_time = end_time - timedelta(hours=24)
            
            # Get metrics for time range
            with self._lock:
                export_metrics = [
                    metric for metric in self.metrics_buffer
                    if start_time <= metric.timestamp <= end_time
                ]
            
            if format.lower() == 'json':
                import json
                metrics_data = [asdict(metric) for metric in export_metrics]
                # Convert datetime to ISO format
                for metric in metrics_data:
                    metric['timestamp'] = metric['timestamp'].isoformat()
                    metric['metric_type'] = metric['metric_type'].value
                return json.dumps(metrics_data, indent=2)
            
            elif format.lower() == 'csv':
                import csv
                import io
                
                output = io.StringIO()
                writer = csv.writer(output)
                
                # Write header
                writer.writerow(['name', 'value', 'type', 'source', 'timestamp', 'tags'])
                
                # Write data
                for metric in export_metrics:
                    writer.writerow([
                        metric.name,
                        metric.value,
                        metric.metric_type.value,
                        metric.source,
                        metric.timestamp.isoformat(),
                        str(metric.tags)
                    ])
                
                return output.getvalue()
            
            elif format.lower() == 'prometheus':
                # Prometheus format export
                lines = []
                metrics_by_name = defaultdict(list)
                
                for metric in export_metrics:
                    metrics_by_name[metric.name].append(metric)
                
                for name, metrics in metrics_by_name.items():
                    # Get latest value for each metric
                    latest_metric = max(metrics, key=lambda m: m.timestamp)
                    
                    # Create Prometheus format line
                    tags_str = ','.join([f'{k}="{v}"' for k, v in latest_metric.tags.items()])
                    if tags_str:
                        line = f"{name}{{{tags_str}}} {latest_metric.value}"
                    else:
                        line = f"{name} {latest_metric.value}"
                    lines.append(line)
                
                return '\n'.join(lines)
            
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting metrics: {str(e)}")
            return ""
    
    def _collection_loop(self):
        """Main metrics collection loop"""
        logger.info("Metrics collection loop started")
        
        while self.is_collecting:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Collect queue metrics
                self._collect_queue_metrics()
                
                # Collect worker metrics
                self._collect_worker_metrics()
                
                # Clean up old metrics
                self._cleanup_old_metrics()
                
                # Sleep until next collection
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {str(e)}")
                self.collection_stats['collection_errors'] += 1
                self.collection_stats['last_error'] = str(e)
                time.sleep(60)  # Wait before retrying
    
    def _collect_system_metrics(self):
        """Collect system-level metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent()
            self.record_metric('system_cpu_percent', cpu_percent, MetricType.GAUGE, 
                             source='system')
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.record_metric('system_memory_percent', memory.percent, MetricType.GAUGE,
                             source='system')
            self.record_metric('system_memory_available', memory.available, MetricType.GAUGE,
                             source='system')
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.record_metric('system_disk_percent', disk_percent, MetricType.GAUGE,
                             source='system')
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
    
    def _collect_queue_metrics(self):
        """Collect queue-specific metrics"""
        try:
            from ..models import QueueJob
            
            # Total jobs by status
            status_counts = QueueJob.objects.values('status').annotate(count=Count('id'))
            
            for status_data in status_counts:
                status = status_data['status']
                count = status_data['count']
                self.record_metric(f'queue_jobs_{status}', count, MetricType.GAUGE,
                                 tags={'status': status}, source='queue')
            
            # Jobs by priority
            priority_counts = QueueJob.objects.values('priority').annotate(count=Count('id'))
            
            for priority_data in priority_counts:
                priority = priority_data['priority']
                count = priority_data['count']
                self.record_metric(f'queue_jobs_by_priority', count, MetricType.GAUGE,
                                 tags={'priority': priority}, source='queue')
            
        except Exception as e:
            logger.error(f"Error collecting queue metrics: {str(e)}")
    
    def _collect_worker_metrics(self):
        """Collect worker-specific metrics"""
        try:
            from ..models import WorkerStatus
            
            # Worker counts by status
            status_counts = WorkerStatus.objects.values('status').annotate(count=Count('id'))
            
            for status_data in status_counts:
                status = status_data['status']
                count = status_data['count']
                self.record_metric(f'workers_{status}', count, MetricType.GAUGE,
                                 tags={'status': status}, source='workers')
            
            # Worker utilization
            total_workers = WorkerStatus.objects.count()
            if total_workers > 0:
                busy_workers = WorkerStatus.objects.filter(status='busy').count()
                utilization = (busy_workers / total_workers) * 100
                self.record_metric('worker_utilization_percent', utilization, MetricType.GAUGE,
                                 source='workers')
            
        except Exception as e:
            logger.error(f"Error collecting worker metrics: {str(e)}")
    
    def _cleanup_old_metrics(self):
        """Clean up old metrics from buffer"""
        try:
            cutoff_time = datetime.now() - timedelta(seconds=self.retention_period)
            
            with self._lock:
                # Filter out old metrics
                self.metrics_buffer = deque([
                    metric for metric in self.metrics_buffer
                    if metric.timestamp > cutoff_time
                ], maxlen=self.metrics_buffer.maxlen)
                
                # Clean up rate data
                for name in list(self.metric_rates.keys()):
                    self.metric_rates[name] = [
                        (ts, val) for ts, val in self.metric_rates[name]
                        if ts > cutoff_time
                    ]
                    
                    if not self.metric_rates[name]:
                        del self.metric_rates[name]
            
        except Exception as e:
            logger.error(f"Error cleaning up old metrics: {str(e)}")
    
    def _calculate_processing_rate(self, priority: str) -> float:
        """Calculate processing rate for priority level"""
        try:
            # This would calculate based on recent job completions
            # For now, return a simple calculation
            return self.get_average_processing_rate(priority, hours=1)
        except Exception as e:
            logger.error(f"Error calculating processing rate: {str(e)}")
            return 0.0
    
    def _calculate_error_rate(self, priority: str) -> float:
        """Calculate error rate for priority level"""
        try:
            from ..models import QueueJob
            
            # Get jobs from last hour
            cutoff_time = timezone.now() - timedelta(hours=1)
            
            total_jobs = QueueJob.objects.filter(
                priority=priority,
                completed_at__gte=cutoff_time
            ).count()
            
            failed_jobs = QueueJob.objects.filter(
                priority=priority,
                status='failed',
                completed_at__gte=cutoff_time
            ).count()
            
            if total_jobs > 0:
                return (failed_jobs / total_jobs) * 100
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating error rate: {str(e)}")
            return 0.0
    
    def _initialize_metric_definitions(self) -> Dict[str, Dict[str, Any]]:
        """Initialize metric definitions"""
        return {
            'queue_length': {
                'type': MetricType.GAUGE,
                'description': 'Number of jobs waiting in queue',
                'unit': 'jobs'
            },
            'processing_rate': {
                'type': MetricType.RATE,
                'description': 'Rate of job processing',
                'unit': 'jobs_per_minute'
            },
            'worker_utilization': {
                'type': MetricType.GAUGE,
                'description': 'Percentage of workers currently busy',
                'unit': 'percentage'
            },
            'error_rate': {
                'type': MetricType.RATE,
                'description': 'Rate of job failures',
                'unit': 'errors_per_minute'
            },
            'response_time': {
                'type': MetricType.HISTOGRAM,
                'description': 'Job processing response time',
                'unit': 'seconds'
            }
        }