"""
PhotoFish Enhanced Queue System - Performance Tracker
Tracks and analyzes system performance trends and optimization opportunities.
"""

import time
import threading
from datetime import datetime, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Optional, Tuple, Any
import statistics
import logging

from django.conf import settings
from django.utils import timezone
from django.core.cache import cache

from .metrics_collector import MetricsCollector

logger = logging.getLogger(__name__)

class PerformanceTracker:
    """
    Advanced performance tracking and trend analysis system
    """
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.performance_history = defaultdict(deque)
        self.response_times = deque(maxlen=1000)
        self.throughput_data = deque(maxlen=100)
        self.bottleneck_detector = BottleneckDetector()
        self.trend_analyzer = TrendAnalyzer()
        self._tracking_thread = None
        self._stop_tracking = threading.Event()
        
        # Performance tracking configuration
        self.config = getattr(settings, 'ENHANCED_QUEUE_SETTINGS', {}).get('MONITORING', {})
        self.tracking_interval = self.config.get('PERFORMANCE_TRACKING_INTERVAL', 30)  # seconds
        self.history_retention = self.config.get('PERFORMANCE_HISTORY_HOURS', 24)  # hours
        
        # Performance thresholds
        self.thresholds = {
            'response_time_warning': 1.0,  # seconds
            'response_time_critical': 3.0,  # seconds
            'throughput_warning': 5.0,  # requests/second
            'throughput_critical': 2.0,  # requests/second
            'error_rate_warning': 0.05,  # 5%
            'error_rate_critical': 0.1,   # 10%
        }
        
        logger.info("Performance Tracker initialized")
    
    def start_tracking(self):
        """Start performance tracking in background thread"""
        if self._tracking_thread and self._tracking_thread.is_alive():
            logger.warning("Performance tracking already running")
            return
        
        self._stop_tracking.clear()
        self._tracking_thread = threading.Thread(target=self._tracking_loop, daemon=True)
        self._tracking_thread.start()
        logger.info("Performance tracking started")
    
    def stop_tracking(self):
        """Stop performance tracking"""
        if self._tracking_thread:
            self._stop_tracking.set()
            self._tracking_thread.join(timeout=5)
            logger.info("Performance tracking stopped")
    
    def _tracking_loop(self):
        """Main tracking loop"""
        while not self._stop_tracking.is_set():
            try:
                self._collect_performance_metrics()
                self._analyze_trends()
                self._detect_bottlenecks()
                self._cleanup_old_data()
                
            except Exception as e:
                logger.error(f"Error in performance tracking loop: {str(e)}")
            
            self._stop_tracking.wait(self.tracking_interval)
    
    def track_response_time(self, endpoint: str, response_time: float, status_code: int = 200):
        """
        Track API response time for specific endpoint
        
        Args:
            endpoint: API endpoint path
            response_time: Response time in seconds
            status_code: HTTP status code
        """
        timestamp = timezone.now()
        
        # Store in response times deque
        self.response_times.append({
            'timestamp': timestamp,
            'endpoint': endpoint,
            'response_time': response_time,
            'status_code': status_code,
            'is_error': status_code >= 400
        })
        
        # Store in performance history
        self.performance_history['response_times'].append({
            'timestamp': timestamp,
            'endpoint': endpoint,
            'value': response_time,
            'status_code': status_code
        })
        
        # Check for threshold violations
        self._check_response_time_thresholds(endpoint, response_time)
        
        # Update real-time cache
        cache_key = f"perf_response_time_{endpoint.replace('/', '_')}"
        cache.set(cache_key, response_time, timeout=300)
    
    def track_throughput(self, requests_count: int, time_window: int = 60):
        """
        Track system throughput (requests per second)
        
        Args:
            requests_count: Number of requests in time window
            time_window: Time window in seconds (default: 60)
        """
        timestamp = timezone.now()
        throughput = requests_count / time_window
        
        self.throughput_data.append({
            'timestamp': timestamp,
            'throughput': throughput,
            'requests_count': requests_count,
            'time_window': time_window
        })
        
        # Store in performance history
        self.performance_history['throughput'].append({
            'timestamp': timestamp,
            'value': throughput,
            'requests': requests_count
        })
        
        # Check for threshold violations
        self._check_throughput_thresholds(throughput)
        
        # Update cache
        cache.set('perf_current_throughput', throughput, timeout=300)
    
    def analyze_endpoint_performance(self, endpoint: str, hours: int = 1) -> Dict[str, Any]:
        """
        Analyze performance for specific endpoint over time period
        
        Args:
            endpoint: API endpoint to analyze
            hours: Hours of history to analyze
            
        Returns:
            Performance analysis dictionary
        """
        cutoff_time = timezone.now() - timedelta(hours=hours)
        
        # Filter response times for this endpoint
        endpoint_data = [
            entry for entry in self.response_times
            if entry['endpoint'] == endpoint and entry['timestamp'] >= cutoff_time
        ]
        
        if not endpoint_data:
            return {
                'endpoint': endpoint,
                'error': 'No data available for specified time period'
            }
        
        response_times = [entry['response_time'] for entry in endpoint_data]
        error_count = sum(1 for entry in endpoint_data if entry['is_error'])
        
        return {
            'endpoint': endpoint,
            'time_period_hours': hours,
            'total_requests': len(endpoint_data),
            'error_count': error_count,
            'error_rate': error_count / len(endpoint_data) if endpoint_data else 0,
            'response_time_stats': {
                'mean': statistics.mean(response_times),
                'median': statistics.median(response_times),
                'min': min(response_times),
                'max': max(response_times),
                'p95': self._calculate_percentile(response_times, 95),
                'p99': self._calculate_percentile(response_times, 99)
            },
            'performance_trend': self.trend_analyzer.analyze_trend(
                [(entry['timestamp'], entry['response_time']) for entry in endpoint_data]
            ),
            'recommendations': self._generate_endpoint_recommendations(endpoint_data)
        }
    
    def get_system_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get overall system performance summary
        
        Args:
            hours: Hours of history to analyze
            
        Returns:
            System performance summary
        """
        cutoff_time = timezone.now() - timedelta(hours=hours)
        
        # Analyze response times
        recent_responses = [
            entry for entry in self.response_times
            if entry['timestamp'] >= cutoff_time
        ]
        
        # Analyze throughput
        recent_throughput = [
            entry for entry in self.throughput_data
            if entry['timestamp'] >= cutoff_time
        ]
        
        if not recent_responses:
            return {'error': 'No performance data available'}
        
        response_times = [entry['response_time'] for entry in recent_responses]
        error_count = sum(1 for entry in recent_responses if entry['is_error'])
        
        # Calculate endpoint breakdown
        endpoint_stats = defaultdict(list)
        for entry in recent_responses:
            endpoint_stats[entry['endpoint']].append(entry['response_time'])
        
        slowest_endpoints = sorted(
            [(endpoint, statistics.mean(times)) for endpoint, times in endpoint_stats.items()],
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        return {
            'time_period_hours': hours,
            'overall_stats': {
                'total_requests': len(recent_responses),
                'error_count': error_count,
                'error_rate': error_count / len(recent_responses) if recent_responses else 0,
                'average_response_time': statistics.mean(response_times),
                'median_response_time': statistics.median(response_times),
                'p95_response_time': self._calculate_percentile(response_times, 95),
                'p99_response_time': self._calculate_percentile(response_times, 99)
            },
            'throughput_stats': {
                'current_throughput': recent_throughput[-1]['throughput'] if recent_throughput else 0,
                'average_throughput': statistics.mean([t['throughput'] for t in recent_throughput]) if recent_throughput else 0,
                'peak_throughput': max([t['throughput'] for t in recent_throughput]) if recent_throughput else 0
            },
            'slowest_endpoints': slowest_endpoints,
            'performance_alerts': self._get_active_performance_alerts(),
            'bottlenecks': self.bottleneck_detector.get_current_bottlenecks(),
            'recommendations': self._generate_system_recommendations()
        }
    
    def identify_bottlenecks(self) -> List[Dict[str, Any]]:
        """
        Identify current system bottlenecks
        
        Returns:
            List of identified bottlenecks with details
        """
        return self.bottleneck_detector.detect_bottlenecks(
            self.response_times,
            self.throughput_data,
            self.performance_history
        )
    
    def generate_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """
        Generate comprehensive performance report
        
        Args:
            hours: Hours of data to include in report
            
        Returns:
            Comprehensive performance report
        """
        cutoff_time = timezone.now() - timedelta(hours=hours)
        
        # Get system summary
        system_summary = self.get_system_performance_summary(hours)
        
        # Analyze all endpoints
        endpoints = set(entry['endpoint'] for entry in self.response_times if entry['timestamp'] >= cutoff_time)
        endpoint_analyses = {}
        for endpoint in endpoints:
            endpoint_analyses[endpoint] = self.analyze_endpoint_performance(endpoint, hours)
        
        # Generate trends
        trends = self.trend_analyzer.analyze_system_trends(self.performance_history, hours)
        
        # Get bottleneck analysis
        bottlenecks = self.identify_bottlenecks()
        
        return {
            'report_timestamp': timezone.now().isoformat(),
            'report_period_hours': hours,
            'system_summary': system_summary,
            'endpoint_analysis': endpoint_analyses,
            'performance_trends': trends,
            'bottleneck_analysis': bottlenecks,
            'optimization_recommendations': self._generate_optimization_recommendations(),
            'historical_comparison': self._compare_with_baseline()
        }
    
    def _collect_performance_metrics(self):
        """Collect current performance metrics"""
        try:
            # Get current system metrics
            metrics = self.metrics_collector.collect_all_metrics()
            
            # Store performance metrics
            timestamp = timezone.now()
            
            # CPU and Memory metrics
            if 'system' in metrics:
                system_metrics = metrics['system']
                self.performance_history['cpu_usage'].append({
                    'timestamp': timestamp,
                    'value': system_metrics.get('cpu_usage', 0)
                })
                self.performance_history['memory_usage'].append({
                    'timestamp': timestamp,
                    'value': system_metrics.get('memory_usage', 0)
                })
            
            # Queue metrics
            if 'queue' in metrics:
                queue_metrics = metrics['queue']
                self.performance_history['queue_depth'].append({
                    'timestamp': timestamp,
                    'value': queue_metrics.get('total_jobs', 0)
                })
                self.performance_history['processing_rate'].append({
                    'timestamp': timestamp,
                    'value': queue_metrics.get('processing_rate', 0)
                })
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {str(e)}")
    
    def _analyze_trends(self):
        """Analyze performance trends"""
        try:
            # Analyze response time trends
            if self.performance_history['response_times']:
                recent_data = list(self.performance_history['response_times'])[-50:]  # Last 50 samples
                trend = self.trend_analyzer.analyze_trend(
                    [(entry['timestamp'], entry['value']) for entry in recent_data]
                )
                
                if trend['direction'] == 'increasing' and trend['significance'] > 0.7:
                    logger.warning(f"Response time trend increasing: {trend}")
                    self._trigger_performance_alert('response_time_trend', trend)
            
        except Exception as e:
            logger.error(f"Error analyzing trends: {str(e)}")
    
    def _detect_bottlenecks(self):
        """Detect system bottlenecks"""
        try:
            bottlenecks = self.bottleneck_detector.detect_bottlenecks(
                self.response_times,
                self.throughput_data,
                self.performance_history
            )
            
            if bottlenecks:
                logger.info(f"Detected {len(bottlenecks)} bottlenecks")
                for bottleneck in bottlenecks:
                    self._trigger_performance_alert('bottleneck_detected', bottleneck)
            
        except Exception as e:
            logger.error(f"Error detecting bottlenecks: {str(e)}")
    
    def _cleanup_old_data(self):
        """Clean up old performance data"""
        cutoff_time = timezone.now() - timedelta(hours=self.history_retention)
        
        # Clean up response times
        self.response_times = deque(
            [entry for entry in self.response_times if entry['timestamp'] >= cutoff_time],
            maxlen=1000
        )
        
        # Clean up throughput data
        self.throughput_data = deque(
            [entry for entry in self.throughput_data if entry['timestamp'] >= cutoff_time],
            maxlen=100
        )
        
        # Clean up performance history
        for metric_name in self.performance_history:
            self.performance_history[metric_name] = deque(
                [entry for entry in self.performance_history[metric_name] if entry['timestamp'] >= cutoff_time],
                maxlen=1000
            )
    
    def _check_response_time_thresholds(self, endpoint: str, response_time: float):
        """Check response time against thresholds"""
        if response_time >= self.thresholds['response_time_critical']:
            self._trigger_performance_alert('response_time_critical', {
                'endpoint': endpoint,
                'response_time': response_time,
                'threshold': self.thresholds['response_time_critical']
            })
        elif response_time >= self.thresholds['response_time_warning']:
            self._trigger_performance_alert('response_time_warning', {
                'endpoint': endpoint,
                'response_time': response_time,
                'threshold': self.thresholds['response_time_warning']
            })
    
    def _check_throughput_thresholds(self, throughput: float):
        """Check throughput against thresholds"""
        if throughput <= self.thresholds['throughput_critical']:
            self._trigger_performance_alert('throughput_critical', {
                'current_throughput': throughput,
                'threshold': self.thresholds['throughput_critical']
            })
        elif throughput <= self.thresholds['throughput_warning']:
            self._trigger_performance_alert('throughput_warning', {
                'current_throughput': throughput,
                'threshold': self.thresholds['throughput_warning']
            })
    
    def _trigger_performance_alert(self, alert_type: str, data: Dict[str, Any]):
        """Trigger performance alert"""
        try:
            from .alert_manager import AlertManager
            alert_manager = AlertManager()
            alert_manager.send_alert(
                alert_type=alert_type,
                severity='warning' if 'warning' in alert_type else 'critical',
                message=f"Performance alert: {alert_type}",
                data=data
            )
        except Exception as e:
            logger.error(f"Error triggering performance alert: {str(e)}")
    
    def _calculate_percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile value"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = (percentile / 100) * (len(sorted_values) - 1)
        
        if index.is_integer():
            return sorted_values[int(index)]
        else:
            lower = sorted_values[int(index)]
            upper = sorted_values[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def _get_active_performance_alerts(self) -> List[Dict[str, Any]]:
        """Get currently active performance alerts"""
        # Implementation would check alert system for active alerts
        return []
    
    def _generate_endpoint_recommendations(self, endpoint_data: List[Dict]) -> List[str]:
        """Generate optimization recommendations for endpoint"""
        recommendations = []
        
        response_times = [entry['response_time'] for entry in endpoint_data]
        avg_response_time = statistics.mean(response_times)
        error_rate = sum(1 for entry in endpoint_data if entry['is_error']) / len(endpoint_data)
        
        if avg_response_time > 2.0:
            recommendations.append("Consider adding database indexing or caching")
        
        if error_rate > 0.05:
            recommendations.append("Investigate error causes and add error handling")
        
        if max(response_times) > 10.0:
            recommendations.append("Add timeout handling for long-running operations")
        
        return recommendations
    
    def _generate_system_recommendations(self) -> List[str]:
        """Generate system-wide optimization recommendations"""
        recommendations = []
        
        # Analyze recent performance
        if len(self.response_times) > 10:
            recent_avg = statistics.mean([entry['response_time'] for entry in list(self.response_times)[-10:]])
            if recent_avg > 1.0:
                recommendations.append("Consider horizontal scaling or performance optimization")
        
        if len(self.throughput_data) > 5:
            recent_throughput = [entry['throughput'] for entry in list(self.throughput_data)[-5:]]
            if statistics.mean(recent_throughput) < 10:
                recommendations.append("Monitor system capacity and consider scaling")
        
        return recommendations
    
    def _generate_optimization_recommendations(self) -> List[str]:
        """Generate detailed optimization recommendations"""
        return [
            "Implement connection pooling for database operations",
            "Add Redis caching for frequently accessed data",
            "Consider CDN for static asset delivery",
            "Optimize database queries with proper indexing",
            "Implement background job processing for heavy operations"
        ]
    
    def _compare_with_baseline(self) -> Dict[str, Any]:
        """Compare current performance with baseline"""
        # This would compare with stored baseline metrics
        return {
            'baseline_comparison': 'No baseline data available',
            'improvement_suggestions': []
        }


class BottleneckDetector:
    """Detects system bottlenecks based on performance data"""
    
    def __init__(self):
        self.detection_rules = {
            'database_bottleneck': self._check_database_bottleneck,
            'memory_bottleneck': self._check_memory_bottleneck,
            'cpu_bottleneck': self._check_cpu_bottleneck,
            'network_bottleneck': self._check_network_bottleneck,
            'queue_bottleneck': self._check_queue_bottleneck
        }
    
    def detect_bottlenecks(self, response_times, throughput_data, performance_history) -> List[Dict[str, Any]]:
        """Detect current system bottlenecks"""
        bottlenecks = []
        
        for bottleneck_type, detection_func in self.detection_rules.items():
            try:
                bottleneck = detection_func(response_times, throughput_data, performance_history)
                if bottleneck:
                    bottlenecks.append(bottleneck)
            except Exception as e:
                logger.error(f"Error detecting {bottleneck_type}: {str(e)}")
        
        return bottlenecks
    
    def get_current_bottlenecks(self) -> List[str]:
        """Get list of current bottleneck types"""
        # This would return currently detected bottlenecks
        return []
    
    def _check_database_bottleneck(self, response_times, throughput_data, performance_history) -> Optional[Dict]:
        """Check for database bottlenecks"""
        # Implementation would analyze database response times
        return None
    
    def _check_memory_bottleneck(self, response_times, throughput_data, performance_history) -> Optional[Dict]:
        """Check for memory bottlenecks"""
        # Implementation would analyze memory usage patterns
        return None
    
    def _check_cpu_bottleneck(self, response_times, throughput_data, performance_history) -> Optional[Dict]:
        """Check for CPU bottlenecks"""
        # Implementation would analyze CPU usage patterns
        return None
    
    def _check_network_bottleneck(self, response_times, throughput_data, performance_history) -> Optional[Dict]:
        """Check for network bottlenecks"""
        # Implementation would analyze network I/O patterns
        return None
    
    def _check_queue_bottleneck(self, response_times, throughput_data, performance_history) -> Optional[Dict]:
        """Check for queue processing bottlenecks"""
        # Implementation would analyze queue depth and processing rates
        return None


class TrendAnalyzer:
    """Analyzes performance trends and patterns"""
    
    def analyze_trend(self, data_points: List[Tuple[datetime, float]]) -> Dict[str, Any]:
        """
        Analyze trend in time series data
        
        Args:
            data_points: List of (timestamp, value) tuples
            
        Returns:
            Trend analysis results
        """
        if len(data_points) < 3:
            return {'direction': 'insufficient_data', 'significance': 0}
        
        # Simple linear regression for trend detection
        timestamps = [point[0].timestamp() for point in data_points]
        values = [point[1] for point in data_points]
        
        # Normalize timestamps
        min_time = min(timestamps)
        x_values = [(t - min_time) for t in timestamps]
        
        # Calculate trend
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(values)
        sum_xy = sum(x * y for x, y in zip(x_values, values))
        sum_x2 = sum(x * x for x in x_values)
        
        # Linear regression slope
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        
        # Determine trend direction and significance
        if abs(slope) < 0.001:
            direction = 'stable'
        elif slope > 0:
            direction = 'increasing'
        else:
            direction = 'decreasing'
        
        # Calculate R-squared for trend significance
        mean_y = sum_y / n
        ss_tot = sum((y - mean_y) ** 2 for y in values)
        intercept = (sum_y - slope * sum_x) / n
        ss_res = sum((y - (slope * x + intercept)) ** 2 for x, y in zip(x_values, values))
        
        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        
        return {
            'direction': direction,
            'slope': slope,
            'significance': r_squared,
            'data_points': len(data_points),
            'confidence': 'high' if r_squared > 0.7 else 'medium' if r_squared > 0.4 else 'low'
        }
    
    def analyze_system_trends(self, performance_history: Dict, hours: int = 24) -> Dict[str, Any]:
        """Analyze trends across all system metrics"""
        trends = {}
        cutoff_time = timezone.now() - timedelta(hours=hours)
        
        for metric_name, metric_data in performance_history.items():
            recent_data = [
                (entry['timestamp'], entry['value'])
                for entry in metric_data
                if entry['timestamp'] >= cutoff_time
            ]
            
            if recent_data:
                trends[metric_name] = self.analyze_trend(recent_data)
        
        return trends