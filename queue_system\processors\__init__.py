# queue_system/processors/__init__.py
"""
Job Processors for PhotoFish Enhanced Queue System
Contains all job processing logic for different job types
"""

from .facial_recognition_processor import FacialRecognitionProcessor, facial_recognition_processor

# Job processor registry
JOB_PROCESSORS = {
    'FACIAL_RECOGNITION': facial_recognition_processor,
    'PHOTO_PROCESSING': facial_recognition_processor,  # Alias for compatibility
    'BULK_UPLOAD': facial_recognition_processor,       # Alias for compatibility
}

def get_job_processor(job_type: str):
    """
    Get the appropriate processor for a job type
    
    Args:
        job_type: Type of job to process
        
    Returns:
        Job processor instance or None
    """
    return JOB_PROCESSORS.get(job_type)

def process_job(job_type: str, job_data: dict, worker_id: str = None) -> dict:
    """
    Process a job using the appropriate processor
    
    Args:
        job_type: Type of job to process
        job_data: Job data and parameters
        worker_id: ID of worker processing the job
        
    Returns:
        Job processing result
    """
    processor = get_job_processor(job_type)
    
    if processor:
        return processor.process_job(job_data, worker_id)
    else:
        return {
            'success': False,
            'error': f'No processor found for job type: {job_type}',
            'processed_count': 0,
            'failed_count': 1
        }

__all__ = [
    'FacialRecognitionProcessor',
    'facial_recognition_processor',
    'JOB_PROCESSORS',
    'get_job_processor',
    'process_job'
]