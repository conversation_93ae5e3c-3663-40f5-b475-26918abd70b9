# queue_system/processors/facial_recognition_processor.py
"""
Facial Recognition Job Processor for PhotoFish Enterprise Queue System
Handles facial recognition jobs submitted to the queue
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, List
from django.conf import settings
from django.db import transaction

logger = logging.getLogger('queue_system.facial_recognition')

class FacialRecognitionProcessor:
    """
    Processes facial recognition jobs from the enterprise queue
    """
    
    def __init__(self):
        self.name = "FacialRecognitionProcessor"
        self.supported_job_types = ['FACIAL_RECOGNITION']
    
    def process_job(self, job_data: Dict[str, Any], worker_id: str = None) -> Dict[str, Any]:
        """
        Process a facial recognition job
        
        Args:
            job_data: Job data containing photo IDs and processing parameters
            worker_id: ID of the worker processing this job
            
        Returns:
            Job result with processing details
        """
        start_time = time.time()
        
        try:
            # Extract job parameters
            photo_ids = job_data.get('photo_ids', [])
            processing_type = job_data.get('processing_type', 'facial_recognition')
            event_id = job_data.get('event_id')
            user_id = job_data.get('user_id')
            batch_type = job_data.get('batch_type', 'upload')
            
            logger.info(f"🔍 Processing facial recognition job: {len(photo_ids)} photos, type: {batch_type}")
            
            if not photo_ids:
                return {
                    'success': False,
                    'error': 'No photo IDs provided',
                    'processed_count': 0,
                    'failed_count': 0
                }
            
            # Process photos based on batch type
            if batch_type in ['batch_upload', 'single_upload']:
                result = self._process_upload_batch(photo_ids, event_id)
            elif batch_type == 'join_processing':
                result = self._process_join_batch(photo_ids, event_id, user_id)
            else:
                result = self._process_general_batch(photo_ids, event_id)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            result['worker_id'] = worker_id
            
            logger.info(f"✅ Facial recognition job completed: {result['processed_count']} processed, "
                       f"{result['failed_count']} failed in {processing_time:.2f}s")
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ Error processing facial recognition job: {str(e)}")
            
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0,
                'failed_count': len(photo_ids) if photo_ids else 0,
                'processing_time': processing_time,
                'worker_id': worker_id
            }
    
    def _process_upload_batch(self, photo_ids: List[str], event_id: str = None) -> Dict[str, Any]:
        """
        Process a batch of newly uploaded photos
        Performs face detection and matching against all attending users
        """
        try:
            from events.models import EventPhoto, Event, EventAttendance
            from facial_recognition.models import FacialProfile, FaceMatchResult
            from facial_recognition.services import RekognitionService
            
            processed_count = 0
            failed_count = 0
            
            for photo_id in photo_ids:
                try:
                    with transaction.atomic():
                        photo = EventPhoto.objects.get(id=photo_id)
                        
                        # Skip if already processed
                        if photo.processed_for_faces:
                            continue
                        
                        # Process the photo
                        success = self._process_single_photo_detection_and_matching(photo)
                        
                        if success:
                            processed_count += 1
                        else:
                            failed_count += 1
                            
                except EventPhoto.DoesNotExist:
                    logger.error(f"Photo {photo_id} not found")
                    failed_count += 1
                except Exception as e:
                    logger.error(f"Error processing photo {photo_id}: {str(e)}")
                    failed_count += 1
            
            return {
                'success': True,
                'processed_count': processed_count,
                'failed_count': failed_count,
                'batch_type': 'upload'
            }
            
        except Exception as e:
            logger.error(f"Error in upload batch processing: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0,
                'failed_count': len(photo_ids)
            }
    
    def _process_join_batch(self, photo_ids: List[str], event_id: str, user_id: int) -> Dict[str, Any]:
        """
        Process photos for a specific user joining an event
        Only matches against the specific user's facial profile
        """
        try:
            from events.models import EventPhoto
            from facial_recognition.models import FacialProfile, FaceMatchResult
            from facial_recognition.services import RekognitionService
            from django.contrib.auth.models import User
            
            processed_count = 0
            failed_count = 0
            matches_found = 0
            
            # Get user's facial profile
            try:
                user = User.objects.get(id=user_id)
                facial_profile = user.facial_profile
                
                if not facial_profile.is_verified or not facial_profile.face_id:
                    return {
                        'success': False,
                        'error': 'User does not have verified facial profile',
                        'processed_count': 0,
                        'failed_count': len(photo_ids)
                    }
                    
            except (User.DoesNotExist, AttributeError):
                return {
                    'success': False,
                    'error': 'User or facial profile not found',
                    'processed_count': 0,
                    'failed_count': len(photo_ids)
                }
            
            for photo_id in photo_ids:
                try:
                    with transaction.atomic():
                        photo = EventPhoto.objects.get(id=photo_id)
                        
                        # Check if match already exists for this user
                        existing_match = FaceMatchResult.objects.filter(
                            user=user,
                            event_photo=photo
                        ).first()
                        
                        if existing_match:
                            processed_count += 1
                            continue
                        
                        # Get photo image bytes
                        photo_file = photo.image.open('rb')
                        image_bytes = photo_file.read()
                        photo_file.close()
                        
                        # Search for user's face in photo
                        success, face_matches, error = RekognitionService.search_faces_by_image(
                            settings.AWS_REKOGNITION_COLLECTION_ID,
                            image_bytes,
                            threshold=70.0
                        )
                        
                        if success and face_matches:
                            for match in face_matches:
                                if match['Face']['FaceId'] == facial_profile.face_id:
                                    # Create match result
                                    FaceMatchResult.objects.create(
                                        user=user,
                                        event_photo=photo,
                                        confidence_score=match['Face']['Confidence'],
                                        similarity_score=match['Similarity'],
                                        bounding_box=match['Face'].get('BoundingBox', {}),
                                        is_verified=match['Similarity'] >= 85.0,
                                    )
                                    matches_found += 1
                                    logger.info(f"Face match created for user {user_id} in photo {photo_id}")
                                    break
                        
                        processed_count += 1
                        
                except EventPhoto.DoesNotExist:
                    logger.error(f"Photo {photo_id} not found")
                    failed_count += 1
                except Exception as e:
                    logger.error(f"Error processing photo {photo_id} for user {user_id}: {str(e)}")
                    failed_count += 1
            
            return {
                'success': True,
                'processed_count': processed_count,
                'failed_count': failed_count,
                'matches_found': matches_found,
                'batch_type': 'join_processing'
            }
            
        except Exception as e:
            logger.error(f"Error in join batch processing: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0,
                'failed_count': len(photo_ids)
            }
    
    def _process_general_batch(self, photo_ids: List[str], event_id: str = None) -> Dict[str, Any]:
        """
        General photo processing (fallback method)
        """
        return self._process_upload_batch(photo_ids, event_id)
    
    def _process_single_photo_detection_and_matching(self, photo) -> bool:
        """
        Process face detection and matching for a single photo
        
        Args:
            photo: EventPhoto instance
            
        Returns:
            True if successful, False otherwise
        """
        try:
            from facial_recognition.services import RekognitionService
            from facial_recognition.models import FacialProfile, FaceMatchResult
            from events.models import EventAttendance
            
            event = photo.event
            
            logger.debug(f"Processing face detection for photo {photo.id} in event {event.id}")
            
            # Get photo image bytes
            photo_file = photo.image.open('rb')
            image_bytes = photo_file.read()
            photo_file.close()
            
            # Step 1: Detect faces in photo
            success, face_details, error = RekognitionService.detect_faces(image_bytes)
            
            if not success:
                logger.error(f"Face detection failed for photo {photo.id}: {error}")
                # Still mark as processed to avoid reprocessing
                photo.processed_for_faces = True
                photo.save()
                return False
            
            # Store face data
            if face_details:
                photo.detected_faces = {
                    'faces': [face_details],
                    'face_count': 1,
                    'processed_at': datetime.now().isoformat(),
                    'aws_collection_id': settings.AWS_REKOGNITION_COLLECTION_ID
                }
                photo.save()
                logger.debug(f"Stored face data for photo {photo.id}")
            
            # Step 2: Match faces against attending users
            attending_user_ids = EventAttendance.objects.filter(
                event=event, 
                is_attending=True
            ).values_list('user_id', flat=True)
            
            attending_profiles = FacialProfile.objects.filter(
                user_id__in=attending_user_ids,
                face_id__isnull=False,
                is_verified=True
            )
            
            logger.debug(f"Matching against {len(attending_profiles)} attending users for photo {photo.id}")
            
            # Perform face matching
            match_count = 0
            for facial_profile in attending_profiles:
                try:
                    # Check if match already exists
                    existing_match = FaceMatchResult.objects.filter(
                        user=facial_profile.user,
                        event_photo=photo
                    ).first()
                    
                    if existing_match:
                        continue
                    
                    # Search for user's face in photo
                    success, face_matches, error = RekognitionService.search_faces_by_image(
                        settings.AWS_REKOGNITION_COLLECTION_ID,
                        image_bytes,
                        threshold=70.0
                    )
                    
                    if success and face_matches:
                        for match in face_matches:
                            if match['Face']['FaceId'] == facial_profile.face_id:
                                # Create match result
                                FaceMatchResult.objects.create(
                                    user=facial_profile.user,
                                    event_photo=photo,
                                    confidence_score=match['Face']['Confidence'],
                                    similarity_score=match['Similarity'],
                                    bounding_box=match['Face'].get('BoundingBox', {}),
                                    is_verified=match['Similarity'] >= 85.0,
                                )
                                match_count += 1
                                logger.debug(f"Face match created for user {facial_profile.user.id} in photo {photo.id}")
                                break
                                
                except Exception as e:
                    logger.error(f"Error matching face for user {facial_profile.user.id} in photo {photo.id}: {str(e)}")
                    continue
            
            # Mark as processed
            photo.processed_for_faces = True
            photo.save()
            
            logger.debug(f"Face processing completed for photo {photo.id}: {match_count} matches found")
            return True
            
        except Exception as e:
            logger.error(f"Error processing photo {photo.id}: {str(e)}")
            return False


# Global processor instance
facial_recognition_processor = FacialRecognitionProcessor()