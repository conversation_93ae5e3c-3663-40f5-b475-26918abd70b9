# queue_system/queue_engine/job_dispatcher.py
"""
Job Dispatcher for PhotoFish Enhanced Queue System
Intelligent job routing and execution coordination
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)

@dataclass
class JobResult:
    """Result of job execution"""
    job_id: str
    success: bool
    result_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    processing_time: float
    worker_id: str
    completed_at: datetime

class JobHandler(ABC):
    """Abstract base class for job handlers"""
    
    @abstractmethod
    def can_handle(self, job_type: str) -> bool:
        """Check if this handler can process the given job type"""
        pass
    
    @abstractmethod
    def process_job(self, job: Any, worker_id: str) -> JobResult:
        """Process the job and return result"""
        pass
    
    @abstractmethod
    def get_estimated_duration(self, job: Any) -> int:
        """Get estimated processing duration in seconds"""
        pass

class FacialRecognitionJobHandler(JobHandler):
    """Handler for facial recognition jobs"""
    
    def can_handle(self, job_type: str) -> bool:
        """Check if this handler can process facial recognition jobs"""
        return job_type.lower() in ['facial_recognition', 'face_detection', 'face_matching']
    
    def process_job(self, job: Any, worker_id: str) -> JobResult:
        """Process facial recognition job"""
        start_time = time.time()
        
        try:
            logger.info(f"Processing facial recognition job {job.job_id} on worker {worker_id}")
            
            # Extract job data
            job_data = job.data  # QueuedJob uses 'data' field
            photo_ids = job_data.get('photo_ids', [])
            event_id = job_data.get('event_id')
            
            if not photo_ids:
                raise ValueError("No photo IDs provided for facial recognition")
            
            # Process each photo
            results = []
            processed_count = 0
            matches_found = 0

            for photo_id in photo_ids:
                photo_result = self._process_single_photo(photo_id, event_id)
                results.append(photo_result)

                if not photo_result.get('error'):
                    processed_count += 1
                    matches_found += photo_result.get('matches_found', 0)
            
            processing_time = time.time() - start_time
            
            return JobResult(
                job_id=job.job_id,
                success=True,
                result_data={
                    'processed_photos': processed_count,
                    'total_photos': len(photo_ids),
                    'matches_found': matches_found,
                    'results': results,
                    'event_id': event_id
                },
                error_message=None,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Facial recognition job failed: {str(e)}"
            logger.error(error_msg)
            
            return JobResult(
                job_id=job.job_id,
                success=False,
                result_data=None,
                error_message=error_msg,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
    
    def get_estimated_duration(self, job: Any) -> int:
        """Estimate processing duration for facial recognition"""
        try:
            photo_count = len(job.data.get('photo_ids', []))
            # Estimate 2 seconds per photo plus 1 second overhead
            return max(3, photo_count * 2 + 1)
        except:
            return 10  # Default estimate
    
    def _process_single_photo(self, photo_id: str, event_id: Optional[str]) -> Dict[str, Any]:
        """Process a single photo for facial recognition"""
        try:
            # This would integrate with the actual facial recognition system
            from facial_recognition.services import RekognitionService
            
            # Get photo from database
            from events.models import EventPhoto
            try:
                photo = EventPhoto.objects.get(id=photo_id)
            except EventPhoto.DoesNotExist:
                raise ValueError(f"Photo {photo_id} not found")
            
            # Get photo image bytes
            try:
                photo_file = photo.image.open('rb')
                image_bytes = photo_file.read()
                photo_file.close()
            except Exception as e:
                raise ValueError(f"Error reading photo file: {str(e)}")

            # Detect faces in the photo
            success, face_details, error = RekognitionService.detect_faces(image_bytes)

            if not success:
                logger.warning(f"No faces detected in photo {photo_id}: {error}")
                faces_detected = 0
                matches = []
            else:
                faces_detected = 1  # detect_faces returns details for the most prominent face

                # Search for matching faces in the collection
                from django.conf import settings
                success, face_matches, error = RekognitionService.search_faces_by_image(
                    settings.AWS_REKOGNITION_COLLECTION_ID,
                    image_bytes,
                    threshold=70.0
                )

                if success and face_matches:
                    matches = []
                    # Process matches and save to database
                    from facial_recognition.models import FacialProfile, FaceMatchResult

                    for match in face_matches:
                        face_id = match['Face']['FaceId']
                        similarity = match['Similarity']

                        try:
                            # Find the user associated with this face_id
                            facial_profile = FacialProfile.objects.get(face_id=face_id)
                            user = facial_profile.user

                            # Create or update the match result
                            face_match, created = FaceMatchResult.objects.update_or_create(
                                user=user,
                                event_photo=photo,
                                defaults={
                                    'confidence_score': match['Face']['Confidence'],
                                    'similarity_score': similarity,
                                    'bounding_box': match['Face'].get('BoundingBox', {}),
                                    'is_verified': similarity >= 85.0,
                                }
                            )

                            matches.append({
                                'user_id': str(user.id),
                                'email': user.email,
                                'username': user.username,
                                'similarity': similarity,
                                'match_id': str(face_match.id),
                                'created': created
                            })

                            if created:
                                logger.info(f"Created face match for user {user.email} in photo {photo_id} (similarity: {similarity:.1f}%)")

                        except FacialProfile.DoesNotExist:
                            logger.warning(f"No facial profile found for face_id {face_id}")
                            continue
                        except Exception as e:
                            logger.error(f"Error creating face match for face_id {face_id}: {str(e)}")
                            continue
                else:
                    logger.debug(f"No face matches found for photo {photo_id}")
                    matches = []
            
            # Mark photo as processed for faces
            photo.processed_for_faces = True
            photo.save()

            logger.info(f"Photo {photo_id} processed: {faces_detected} faces detected, {len(matches)} matches found")

            return {
                'photo_id': photo_id,
                'faces_detected': faces_detected,
                'matches_found': len(matches),
                'face_details': face_details if 'face_details' in locals() else None,
                'matches': matches,
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing photo {photo_id}: {str(e)}")
            return {
                'photo_id': photo_id,
                'error': str(e),
                'faces_detected': 0,
                'matches_found': 0,
                'processed_at': datetime.now().isoformat()
            }

class PhotoProcessingJobHandler(JobHandler):
    """Handler for general photo processing jobs"""
    
    def can_handle(self, job_type: str) -> bool:
        """Check if this handler can process photo jobs"""
        return job_type.lower() in ['photo_processing', 'image_resize', 'thumbnail_generation']
    
    def process_job(self, job: Any, worker_id: str) -> JobResult:
        """Process photo processing job"""
        start_time = time.time()
        
        try:
            logger.info(f"Processing photo job {job.job_id} on worker {worker_id}")
            
            job_data = job.data  # QueuedJob uses 'data' field
            photo_ids = job_data.get('photo_ids', [])
            processing_type = job_data.get('processing_type', 'resize')
            
            if not photo_ids:
                raise ValueError("No photo IDs provided for processing")
            
            # Process photos based on type
            results = []
            for photo_id in photo_ids:
                if processing_type == 'resize':
                    result = self._resize_photo(photo_id, job_data.get('size', (800, 600)))
                elif processing_type == 'thumbnail':
                    result = self._generate_thumbnail(photo_id, job_data.get('size', (150, 150)))
                else:
                    result = {'photo_id': photo_id, 'error': f'Unknown processing type: {processing_type}'}
                
                results.append(result)
            
            processing_time = time.time() - start_time
            
            return JobResult(
                job_id=job.job_id,
                success=True,
                result_data={
                    'processed_photos': len(photo_ids),
                    'processing_type': processing_type,
                    'results': results
                },
                error_message=None,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Photo processing job failed: {str(e)}"
            logger.error(error_msg)
            
            return JobResult(
                job_id=job.job_id,
                success=False,
                result_data=None,
                error_message=error_msg,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
    
    def get_estimated_duration(self, job: Any) -> int:
        """Estimate processing duration for photo processing"""
        try:
            photo_count = len(job.data.get('photo_ids', []))
            processing_type = job.data.get('processing_type', 'resize')
            
            # Different estimates based on processing type
            if processing_type == 'thumbnail':
                return max(2, photo_count * 0.5 + 1)
            else:  # resize or other
                return max(3, photo_count * 1 + 1)
        except:
            return 5  # Default estimate
    
    def _resize_photo(self, photo_id: int, size: tuple) -> Dict[str, Any]:
        """Resize a photo"""
        try:
            # This would integrate with actual image processing
            from PIL import Image
            from events.models import EventPhoto
            
            photo = EventPhoto.objects.get(id=photo_id)
            
            # Open and resize image
            with Image.open(photo.image.path) as img:
                original_size = img.size
                resized_img = img.resize(size, Image.Resampling.LANCZOS)
                
                # Save resized image (would need proper file handling)
                # For now, just simulate the process
                
            return {
                'photo_id': photo_id,
                'original_size': original_size,
                'new_size': size,
                'success': True,
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'photo_id': photo_id,
                'error': str(e),
                'success': False,
                'processed_at': datetime.now().isoformat()
            }
    
    def _generate_thumbnail(self, photo_id: int, size: tuple) -> Dict[str, Any]:
        """Generate thumbnail for a photo"""
        try:
            # Similar to resize but with thumbnail-specific logic
            return self._resize_photo(photo_id, size)
        except Exception as e:
            return {
                'photo_id': photo_id,
                'error': str(e),
                'success': False,
                'processed_at': datetime.now().isoformat()
            }

class BulkUploadJobHandler(JobHandler):
    """Handler for bulk upload jobs"""
    
    def can_handle(self, job_type: str) -> bool:
        """Check if this handler can process bulk upload jobs"""
        return job_type.lower() in ['bulk_upload', 'batch_processing']
    
    def process_job(self, job: Any, worker_id: str) -> JobResult:
        """Process bulk upload job"""
        start_time = time.time()
        
        try:
            logger.info(f"Processing bulk upload job {job.job_id} on worker {worker_id}")
            
            job_data = job.data
            file_paths = job_data.get('file_paths', [])
            event_id = job_data.get('event_id')
            
            if not file_paths:
                raise ValueError("No file paths provided for bulk upload")
            
            # Process each file
            results = []
            for file_path in file_paths:
                file_result = self._process_uploaded_file(file_path, event_id)
                results.append(file_result)
            
            processing_time = time.time() - start_time
            
            return JobResult(
                job_id=job.job_id,
                success=True,
                result_data={
                    'processed_files': len(file_paths),
                    'results': results,
                    'event_id': event_id
                },
                error_message=None,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Bulk upload job failed: {str(e)}"
            logger.error(error_msg)
            
            return JobResult(
                job_id=job.job_id,
                success=False,
                result_data=None,
                error_message=error_msg,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
    
    def get_estimated_duration(self, job: Any) -> int:
        """Estimate processing duration for bulk upload"""
        try:
            file_count = len(job.data.get('file_paths', []))
            # Estimate 3 seconds per file plus overhead
            return max(5, file_count * 3 + 2)
        except:
            return 15  # Default estimate
    
    def _process_uploaded_file(self, file_path: str, event_id: Optional[int]) -> Dict[str, Any]:
        """Process a single uploaded file"""
        try:
            # This would integrate with the actual file processing system
            import os
            from events.models import EventPhoto, Event
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Create EventPhoto record
            event = None
            if event_id:
                try:
                    event = Event.objects.get(id=event_id)
                except Event.DoesNotExist:
                    pass
            
            # Create photo record (simplified)
            # In reality, this would need proper file field handling
            photo = EventPhoto.objects.create(
                event=event,
                original_filename=os.path.basename(file_path),
                file_size=os.path.getsize(file_path)
                # image field would be handled differently
            )
            
            return {
                'file_path': file_path,
                'photo_id': photo.id,
                'success': True,
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {str(e)}")
            return {
                'file_path': file_path,
                'error': str(e),
                'success': False,
                'processed_at': datetime.now().isoformat()
            }

class MaintenanceJobHandler(JobHandler):
    """Handler for maintenance jobs"""
    
    def can_handle(self, job_type: str) -> bool:
        """Check if this handler can process maintenance jobs"""
        return job_type.lower() in ['maintenance', 'cleanup', 'system_optimization']
    
    def process_job(self, job: Any, worker_id: str) -> JobResult:
        """Process maintenance job"""
        start_time = time.time()
        
        try:
            logger.info(f"Processing maintenance job {job.job_id} on worker {worker_id}")
            
            job_data = job.data  # QueuedJob uses 'data' field
            maintenance_type = job_data.get('maintenance_type', 'general')
            
            # Perform maintenance based on type
            if maintenance_type == 'cleanup':
                result = self._perform_cleanup()
            elif maintenance_type == 'optimization':
                result = self._perform_optimization()
            else:
                result = self._perform_general_maintenance()
            
            processing_time = time.time() - start_time
            
            return JobResult(
                job_id=job.job_id,
                success=result['success'],
                result_data=result,
                error_message=result.get('error'),
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Maintenance job failed: {str(e)}"
            logger.error(error_msg)
            
            return JobResult(
                job_id=job.job_id,
                success=False,
                result_data=None,
                error_message=error_msg,
                processing_time=processing_time,
                worker_id=worker_id,
                completed_at=datetime.now()
            )
    
    def get_estimated_duration(self, job: Any) -> int:
        """Estimate processing duration for maintenance"""
        maintenance_type = job.data.get('maintenance_type', 'general')
        
        if maintenance_type == 'cleanup':
            return 30  # 30 seconds
        elif maintenance_type == 'optimization':
            return 60  # 1 minute
        else:
            return 15  # Default
    
    def _perform_cleanup(self) -> Dict[str, Any]:
        """Perform system cleanup"""
        try:
            # This would integrate with cleanup services
            from ..resource_manager.cleanup_service import CleanupService
            
            cleanup_service = CleanupService()
            result = cleanup_service.cleanup_temp_files()
            
            return {
                'maintenance_type': 'cleanup',
                'success': result.success,
                'items_cleaned': result.items_cleaned,
                'bytes_freed': result.bytes_freed
            }
            
        except Exception as e:
            return {
                'maintenance_type': 'cleanup',
                'success': False,
                'error': str(e)
            }
    
    def _perform_optimization(self) -> Dict[str, Any]:
        """Perform system optimization"""
        try:
            # This would integrate with memory manager
            from ..resource_manager.memory_manager import MemoryManager
            
            memory_manager = MemoryManager()
            result = memory_manager.optimize_memory()
            
            return {
                'maintenance_type': 'optimization',
                'success': True,
                'memory_freed_mb': result.get('memory_freed_mb', 0),
                'gc_collections': result.get('gc_collections', 0)
            }
            
        except Exception as e:
            return {
                'maintenance_type': 'optimization',
                'success': False,
                'error': str(e)
            }
    
    def _perform_general_maintenance(self) -> Dict[str, Any]:
        """Perform general maintenance tasks"""
        return {
            'maintenance_type': 'general',
            'success': True,
            'tasks_completed': ['health_check', 'log_rotation']
        }

class JobDispatcher:
    """
    Intelligent job dispatcher that routes jobs to appropriate handlers and workers
    """
    
    def __init__(self, worker_pool=None, queue_manager=None):
        self.worker_pool = worker_pool
        self.queue_manager = queue_manager
        
        # Job handlers
        self.job_handlers = {}
        self._initialize_handlers()
        
        # Dispatch statistics
        self.dispatch_stats = {
            'total_dispatched': 0,
            'successful_dispatches': 0,
            'failed_dispatches': 0,
            'average_dispatch_time': 0.0,
            'last_reset': datetime.now()
        }
        
        # Threading
        self._lock = threading.Lock()
    
    def dispatch_job(self, job) -> bool:
        """
        Dispatch a job to the appropriate handler and worker
        
        Args:
            job: Job to dispatch
            
        Returns:
            Success status
        """
        dispatch_start = time.time()
        
        try:
            # Find appropriate handler
            handler = self._find_handler(job.job_type)
            if not handler:
                logger.error(f"No handler found for job type: {job.job_type}")
                return False
            
            # Create job processor function that uses the handler
            def job_processor(job_to_process, worker_id):
                try:
                    # Process job with handler
                    result = handler.process_job(job_to_process, worker_id)
                    
                    # Update job status based on result
                    if self.queue_manager:
                        if result.success:
                            self.queue_manager.update_job_status(job_to_process.job_id, 'completed')
                        else:
                            self.queue_manager.update_job_status(job_to_process.job_id, 'failed')
                    
                    # Log result
                    if result.success:
                        logger.info(f"Job {job_to_process.job_id} completed successfully in {result.processing_time:.2f}s")
                    else:
                        logger.error(f"Job {job_to_process.job_id} failed: {result.error_message}")
                    
                    return result.success
                    
                except Exception as e:
                    logger.error(f"Error in job processor: {str(e)}")
                    
                    # Update job status to failed
                    if self.queue_manager:
                        self.queue_manager.update_job_status(job_to_process.job_id, 'failed')
                    
                    return False
            
            # Set the job processor on the worker pool
            if self.worker_pool:
                # Find workers and set processor (this would need worker pool enhancement)
                success = self.worker_pool.assign_job(job, job.priority.name)
            else:
                logger.error("No worker pool available")
                success = False
            
            # Update statistics
            dispatch_time = time.time() - dispatch_start
            with self._lock:
                self.dispatch_stats['total_dispatched'] += 1
                if success:
                    self.dispatch_stats['successful_dispatches'] += 1
                else:
                    self.dispatch_stats['failed_dispatches'] += 1
                
                # Update average dispatch time
                total_dispatches = self.dispatch_stats['total_dispatched']
                current_avg = self.dispatch_stats['average_dispatch_time']
                self.dispatch_stats['average_dispatch_time'] = (
                    (current_avg * (total_dispatches - 1) + dispatch_time) / total_dispatches
                )
            
            if success:
                logger.debug(f"Job {job.job_id} dispatched successfully")
            else:
                logger.error(f"Failed to dispatch job {job.job_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error dispatching job {job.job_id}: {str(e)}")
            
            with self._lock:
                self.dispatch_stats['total_dispatched'] += 1
                self.dispatch_stats['failed_dispatches'] += 1
            
            return False
    
    def get_dispatch_stats(self) -> Dict[str, Any]:
        """
        Get job dispatch statistics
        
        Returns:
            Dispatch statistics
        """
        try:
            with self._lock:
                stats = self.dispatch_stats.copy()
                
                # Calculate success rate
                total = stats['total_dispatched']
                if total > 0:
                    stats['success_rate'] = (stats['successful_dispatches'] / total) * 100
                    stats['failure_rate'] = (stats['failed_dispatches'] / total) * 100
                else:
                    stats['success_rate'] = 0.0
                    stats['failure_rate'] = 0.0
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting dispatch stats: {str(e)}")
            return {}
    
    def register_handler(self, job_type: str, handler: JobHandler) -> bool:
        """
        Register a custom job handler
        
        Args:
            job_type: Job type to handle
            handler: Handler instance
            
        Returns:
            Success status
        """
        try:
            self.job_handlers[job_type] = handler
            logger.info(f"Registered handler for job type: {job_type}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering handler for {job_type}: {str(e)}")
            return False
    
    def get_supported_job_types(self) -> List[str]:
        """
        Get list of supported job types
        
        Returns:
            List of supported job types
        """
        return list(self.job_handlers.keys())
    
    def _initialize_handlers(self):
        """Initialize built-in job handlers"""
        try:
            # Create handler instances
            facial_handler = FacialRecognitionJobHandler()
            photo_handler = PhotoProcessingJobHandler()
            bulk_handler = BulkUploadJobHandler()
            maintenance_handler = MaintenanceJobHandler()
            
            # Register handlers for their job types
            job_type_mappings = {
                'facial_recognition': facial_handler,
                'face_detection': facial_handler,
                'face_matching': facial_handler,
                'photo_processing': photo_handler,
                'image_resize': photo_handler,
                'thumbnail_generation': photo_handler,
                'bulk_upload': bulk_handler,
                'batch_processing': bulk_handler,
                'maintenance': maintenance_handler,
                'cleanup': maintenance_handler,
                'system_optimization': maintenance_handler,
                'FACIAL_RECOGNITION': facial_handler,  # ADD THIS LINE
            }
            
            for job_type, handler in job_type_mappings.items():
                self.job_handlers[job_type] = handler
            
            logger.info(f"Initialized {len(set(job_type_mappings.values()))} job handlers for {len(job_type_mappings)} job types")
            
        except Exception as e:
            logger.error(f"Error initializing job handlers: {str(e)}")
    
    def _find_handler(self, job_type: str) -> Optional[JobHandler]:
        """Find appropriate handler for job type"""
        try:
            logger.debug(f"Looking for handler for job type: {job_type}")
            logger.debug(f"Available handlers: {list(self.job_handlers.keys())}")

            # First check exact match
            if job_type in self.job_handlers:
                logger.debug(f"Found exact match handler for {job_type}")
                return self.job_handlers[job_type]

            # Then check if any handler can handle this job type
            for handler in set(self.job_handlers.values()):
                if handler.can_handle(job_type):
                    logger.debug(f"Found compatible handler for {job_type}")
                    return handler

            logger.warning(f"No handler found for job type: {job_type}")
            return None

        except Exception as e:
            logger.error(f"Error finding handler for {job_type}: {str(e)}")
            return None