# queue_system/queue_engine/queue_manager.py
"""
Queue Manager for PhotoFish Enhanced Queue System
Central orchestrator for multi-priority queue management
"""

import logging
import threading
import time
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from queue import Queue, PriorityQueue, Empty
from enum import Enum
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)

class JobPriority(Enum):
    """Job priority levels"""
    EMERGENCY = 0
    HIGH = 1
    STANDARD = 2
    LOW = 3
    MAINTENANCE = 4

@dataclass
class QueuedJob:
    """Represents a job in the queue"""
    job_id: str
    job_type: str
    priority: JobPriority
    data: Dict[str, Any]
    options: Dict[str, Any]
    user_id: Optional[int]
    created_at: datetime
    retry_count: int = 0
    max_retries: int = 3
    estimated_duration: Optional[int] = None

@dataclass
class QueueStatus:
    """Queue status information"""
    total_jobs: int
    jobs_by_priority: Dict[str, int]
    jobs_by_status: Dict[str, int]
    active_workers: int
    processing_rate: float
    average_wait_time: float
    last_updated: datetime

class QueueManager:
    """
    Central queue manager for multi-priority job processing
    Orchestrates job submission, worker coordination, and status tracking
    """
    
    def __init__(self):
        # Priority queues for different job types
        self.priority_queues = {
            JobPriority.EMERGENCY: PriorityQueue(),
            JobPriority.HIGH: PriorityQueue(),
            JobPriority.STANDARD: PriorityQueue(),
            JobPriority.LOW: PriorityQueue(),
            JobPriority.MAINTENANCE: PriorityQueue()
        }
        
        # Job tracking
        self.active_jobs = {}  # job_id -> QueuedJob
        self.completed_jobs = {}  # job_id -> completion_info
        self.job_status = {}  # job_id -> current_status
        
        # Worker management
        self.worker_pool = None
        self.job_dispatcher = None
        
        # Threading and synchronization
        self._lock = threading.RLock()
        self.processing_thread = None
        self.is_running = False
        
        # Configuration
        self.config = self._load_configuration()
        
        # Performance tracking
        self.processing_stats = {
            'total_processed': 0,
            'successful_jobs': 0,
            'failed_jobs': 0,
            'average_processing_time': 0.0,
            'last_reset': datetime.now()
        }
        
        # Job callbacks
        self.job_callbacks = {
            'on_job_start': [],
            'on_job_complete': [],
            'on_job_failed': [],
            'on_job_retry': []
        }
    
    def start(self) -> bool:
        """
        Start the queue manager
        
        Returns:
            Success status
        """
        try:
            if self.is_running:
                logger.warning("Queue manager is already running")
                return True
            
            # Initialize components
            self._initialize_worker_pool()
            self._initialize_job_dispatcher()
            
            # Start processing
            self.is_running = True
            self.processing_thread = threading.Thread(
                target=self._processing_loop,
                daemon=True,
                name="QueueManager"
            )
            self.processing_thread.start()
            
            logger.info("Queue manager started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting queue manager: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the queue manager gracefully
        
        Returns:
            Success status
        """
        try:
            logger.info("Stopping queue manager...")
            
            self.is_running = False
            
            # Wait for processing thread to finish
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=30)
            
            # Stop worker pool
            if self.worker_pool:
                self.worker_pool.stop()
            
            logger.info("Queue manager stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping queue manager: {str(e)}")
            return False
    
    def submit_job(self, job_id: Optional[str] = None, job_type: str = '',
                priority: str = 'STANDARD', data: Dict[str, Any] = None,
                options: Dict[str, Any] = None, user_id: Optional[int] = None) -> str:
        """
        Submit a job to the queue
        
        Args:
            job_id: Optional job ID (generated if not provided)
            job_type: Type of job to process
            priority: Job priority level
            data: Job data
            options: Job processing options
            user_id: User ID submitting the job
            
        Returns:
            Job ID
        """

        try:
            # ADDED: Ensure job processors are loaded
            if not hasattr(self, 'job_processors') or not self.job_processors:
                self.job_processors = {}
            
            # ADDED: Check if processor exists for job type
            if job_type not in self.job_processors:
                logger.warning(f"No processor registered for job type: {job_type}")
                # Register default processor
                from queue_system.processors import process_job
                self.job_processors[job_type] = process_job
                logger.info(f"Registered processor for job type: {job_type}")
            
            # EXISTING CODE BELOW (unchanged):
            # Generate job ID if not provided
            if not job_id:
                job_id = str(uuid.uuid4())
            
            # Convert priority string to enum
            try:
                priority_enum = JobPriority[priority.upper()]
            except KeyError:
                priority_enum = JobPriority.STANDARD
                logger.warning(f"Invalid priority '{priority}', using STANDARD")
            
            # Create queued job
            queued_job = QueuedJob(
                job_id=job_id,
                job_type=job_type,
                priority=priority_enum,
                data=data or {},
                options=options or {},
                user_id=user_id,
                created_at=datetime.now(),
                max_retries=options.get('max_retries', 3) if options else 3,
                estimated_duration=options.get('estimated_duration') if options else None
            )
            
            # Add to appropriate priority queue
            with self._lock:
                # Use timestamp as secondary sort key to maintain FIFO within priority
                queue_item = (priority_enum.value, time.time(), queued_job)
                self.priority_queues[priority_enum].put(queue_item)
                
                # Track job
                self.active_jobs[job_id] = queued_job
                self.job_status[job_id] = 'queued'
            
            # Persist job
            self._persist_job(queued_job)
            
            logger.info(f"Job {job_id} ({job_type}) submitted with {priority} priority")
            return job_id
            
        except Exception as e:
            logger.error(f"Error submitting job: {str(e)}")
            raise
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a specific job
        
        Args:
            job_id: Job ID to check
            
        Returns:
            Job status information or None if not found
        """
        try:
            with self._lock:
                # Check active jobs
                if job_id in self.active_jobs:
                    job = self.active_jobs[job_id]
                    status = self.job_status.get(job_id, 'unknown')
                    
                    return {
                        'job_id': job_id,
                        'status': status,
                        'job_type': job.job_type,
                        'priority': job.priority.name,
                        'created_at': job.created_at.isoformat(),
                        'retry_count': job.retry_count,
                        'max_retries': job.max_retries,
                        'estimated_duration': job.estimated_duration
                    }
                
                # Check completed jobs
                if job_id in self.completed_jobs:
                    return self.completed_jobs[job_id]
                
                return None
                
        except Exception as e:
            logger.error(f"Error getting job status for {job_id}: {str(e)}")
            return None
    
    def get_job_position(self, job_id: str) -> Optional[int]:
        """
        Get position of job in queue
        
        Args:
            job_id: Job ID to check
            
        Returns:
            Position in queue (0-based) or None if not in queue
        """
        try:
            with self._lock:
                if job_id not in self.active_jobs:
                    return None
                
                job = self.active_jobs[job_id]
                priority = job.priority
                
                # Count jobs ahead in the same priority queue
                position = 0
                temp_items = []
                
                # Extract all items from queue to count
                while not self.priority_queues[priority].empty():
                    try:
                        item = self.priority_queues[priority].get_nowait()
                        temp_items.append(item)
                        
                        # Check if this is our job
                        if item[2].job_id == job_id:
                            break
                        position += 1
                    except Empty:
                        break
                
                # Put items back
                for item in temp_items:
                    self.priority_queues[priority].put(item)
                
                return position
                
        except Exception as e:
            logger.error(f"Error getting job position for {job_id}: {str(e)}")
            return None
    
    def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a queued job
        
        Args:
            job_id: Job ID to cancel
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                if job_id not in self.active_jobs:
                    return False
                
                # Mark job as cancelled
                self.job_status[job_id] = 'cancelled'
                
                # Move to completed jobs
                job = self.active_jobs[job_id]
                self.completed_jobs[job_id] = {
                    'job_id': job_id,
                    'status': 'cancelled',
                    'cancelled_at': datetime.now().isoformat()
                }
                
                # Remove from active jobs
                del self.active_jobs[job_id]
                
                logger.info(f"Job {job_id} cancelled")
                return True
                
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {str(e)}")
            return False
    
    def update_job_status(self, job_id: str, status: str, progress: float = 0.0) -> bool:
        """
        Update job status and progress
        
        Args:
            job_id: Job ID to update
            status: New status
            progress: Progress percentage (0-100)
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                if job_id in self.job_status:
                    self.job_status[job_id] = status
                    
                    # Trigger callbacks based on status
                    if status == 'processing':
                        self._trigger_callbacks('on_job_start', job_id)
                    elif status == 'completed':
                        self._handle_job_completion(job_id, True)
                    elif status == 'failed':
                        self._handle_job_failure(job_id)
                    
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Error updating job status for {job_id}: {str(e)}")
            return False
    
    def get_queue_status(self) -> QueueStatus:
        """
        Get comprehensive queue status
        
        Returns:
            Current queue status
        """
        try:
            with self._lock:
                # Count jobs by priority
                jobs_by_priority = {}
                total_jobs = 0
                
                for priority, queue in self.priority_queues.items():
                    count = queue.qsize()
                    jobs_by_priority[priority.name] = count
                    total_jobs += count
                
                # Count jobs by status
                jobs_by_status = {}
                for status in self.job_status.values():
                    jobs_by_status[status] = jobs_by_status.get(status, 0) + 1
                
                # Get worker count
                active_workers = 0
                if self.worker_pool:
                    active_workers = self.worker_pool.get_active_worker_count()
                
                # Calculate processing rate
                processing_rate = self._calculate_processing_rate()
                
                return QueueStatus(
                    total_jobs=total_jobs,
                    jobs_by_priority=jobs_by_priority,
                    jobs_by_status=jobs_by_status,
                    active_workers=active_workers,
                    processing_rate=processing_rate,
                    average_wait_time=self._calculate_average_wait_time(),
                    last_updated=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"Error getting queue status: {str(e)}")
            return QueueStatus(
                total_jobs=0, jobs_by_priority={}, jobs_by_status={},
                active_workers=0, processing_rate=0.0, average_wait_time=0.0,
                last_updated=datetime.now()
            )
    
    def add_job_callback(self, event: str, callback: Callable):
        """
        Add callback for job events
        
        Args:
            event: Event type ('on_job_start', 'on_job_complete', etc.)
            callback: Callback function
        """
        try:
            if event in self.job_callbacks:
                self.job_callbacks[event].append(callback)
            else:
                logger.warning(f"Unknown job event: {event}")
                
        except Exception as e:
            logger.error(f"Error adding job callback: {str(e)}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get queue performance statistics
        
        Returns:
            Performance statistics
        """
        try:
            with self._lock:
                stats = self.processing_stats.copy()
                
                # Calculate success rate
                total_processed = stats['total_processed']
                if total_processed > 0:
                    stats['success_rate'] = (stats['successful_jobs'] / total_processed) * 100
                    stats['failure_rate'] = (stats['failed_jobs'] / total_processed) * 100
                else:
                    stats['success_rate'] = 0.0
                    stats['failure_rate'] = 0.0
                
                # Add current queue sizes
                stats['current_queue_sizes'] = {
                    priority.name: queue.qsize()
                    for priority, queue in self.priority_queues.items()
                }
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting performance stats: {str(e)}")
            return {}
    
    def _processing_loop(self):
        """Main processing loop for the queue manager"""
        logger.info("Queue manager processing loop started")
        
        while self.is_running:
            try:
                job_processed = False
                
                # Process jobs in priority order
                for priority in JobPriority:
                    if not self.is_running:
                        break
                    
                    queue = self.priority_queues[priority]
                    
                    # Try to get a job from this priority level
                    try:
                        # Use short timeout to check other priorities
                        queue_item = queue.get(timeout=0.1)
                        
                        # Extract job from queue item
                        _, _, queued_job = queue_item
                        
                        # Process the job
                        self._process_job(queued_job)
                        job_processed = True
                        
                        # Mark queue task as done
                        queue.task_done()
                        
                    except Empty:
                        # No jobs in this priority level, try next
                        continue
                    except Exception as e:
                        logger.error(f"Error processing job from {priority.name} queue: {str(e)}")
                        continue
                
                # If no jobs were processed, sleep briefly
                if not job_processed:
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Error in queue processing loop: {str(e)}")
                time.sleep(1)  # Wait before retrying
    
    def _process_job(self, queued_job: QueuedJob):
        """Process a single job"""
        try:
            job_id = queued_job.job_id
            
            # Update job status
            with self._lock:
                self.job_status[job_id] = 'processing'
            
            # Dispatch job to worker
            if self.job_dispatcher:
                success = self.job_dispatcher.dispatch_job(queued_job)
                
                if success:
                    logger.debug(f"Job {job_id} dispatched to worker")
                else:
                    logger.error(f"Failed to dispatch job {job_id}")
                    self._handle_job_failure(job_id)
            else:
                logger.error("No job dispatcher available")
                self._handle_job_failure(job_id)
                
        except Exception as e:
            logger.error(f"Error processing job {queued_job.job_id}: {str(e)}")
            self._handle_job_failure(queued_job.job_id)
    
    def _handle_job_completion(self, job_id: str, success: bool):
        """Handle job completion"""
        try:
            with self._lock:
                if job_id in self.active_jobs:
                    job = self.active_jobs[job_id]
                    
                    # Update statistics
                    self.processing_stats['total_processed'] += 1
                    if success:
                        self.processing_stats['successful_jobs'] += 1
                    else:
                        self.processing_stats['failed_jobs'] += 1
                    
                    # Move to completed jobs
                    self.completed_jobs[job_id] = {
                        'job_id': job_id,
                        'status': 'completed' if success else 'failed',
                        'completed_at': datetime.now().isoformat(),
                        'job_type': job.job_type,
                        'priority': job.priority.name,
                        'retry_count': job.retry_count
                    }
                    
                    # Remove from active jobs
                    del self.active_jobs[job_id]
                    if job_id in self.job_status:
                        del self.job_status[job_id]
                    
                    # Trigger callbacks
                    event = 'on_job_complete' if success else 'on_job_failed'
                    self._trigger_callbacks(event, job_id)
                    
        except Exception as e:
            logger.error(f"Error handling job completion for {job_id}: {str(e)}")
    
    def _handle_job_failure(self, job_id: str):
        """Handle job failure and potential retry"""
        try:
            with self._lock:
                if job_id not in self.active_jobs:
                    return
                
                job = self.active_jobs[job_id]
                job.retry_count += 1
                
                # Check if we should retry
                if job.retry_count <= job.max_retries:
                    # Requeue the job
                    logger.info(f"Retrying job {job_id} (attempt {job.retry_count}/{job.max_retries})")
                    
                    queue_item = (job.priority.value, time.time(), job)
                    self.priority_queues[job.priority].put(queue_item)
                    
                    self.job_status[job_id] = 'retry'
                    self._trigger_callbacks('on_job_retry', job_id)
                else:
                    # Max retries exceeded
                    logger.error(f"Job {job_id} failed after {job.retry_count} attempts")
                    self._handle_job_completion(job_id, False)
                    
        except Exception as e:
            logger.error(f"Error handling job failure for {job_id}: {str(e)}")
    
    def _trigger_callbacks(self, event: str, job_id: str):
        """Trigger registered callbacks for job events"""
        try:
            callbacks = self.job_callbacks.get(event, [])
            for callback in callbacks:
                try:
                    callback(job_id)
                except Exception as e:
                    logger.error(f"Error in job callback {event}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error triggering callbacks for {event}: {str(e)}")
    
    def _initialize_worker_pool(self):
        """Initialize the worker pool"""
        try:
            from .worker_pool import WorkerPool
            
            self.worker_pool = WorkerPool()
            self.worker_pool.start()
            
        except Exception as e:
            logger.error(f"Error initializing worker pool: {str(e)}")
    
    def _initialize_job_dispatcher(self):
        """Initialize the job dispatcher"""
        try:
            from .job_dispatcher import JobDispatcher
            
            self.job_dispatcher = JobDispatcher(
                worker_pool=self.worker_pool,
                queue_manager=self
            )
            
            # IMPORTANT: Connect the dispatcher to the worker pool
            if self.worker_pool:
                self.worker_pool.set_job_dispatcher(self.job_dispatcher)
            
        except Exception as e:
            logger.error(f"Error initializing job dispatcher: {str(e)}")
    
    def _persist_job(self, job: QueuedJob):
        """Persist job to database"""
        try:
            from .queue_persistence import QueuePersistence
            from .queue_persistence import JobState
            
            persistence = QueuePersistence()
            
            job_state = JobState(
                job_id=job.job_id,
                job_type=job.job_type,
                priority=job.priority.name,
                status='queued',
                data=job.data,
                options=job.options,
                user_id=job.user_id,
                created_at=job.created_at,
                updated_at=job.created_at,
                started_at=None,
                completed_at=None,
                worker_id=None,
                retry_count=job.retry_count,
                max_retries=job.max_retries,
                error_message=None,
                result=None,
                progress_percentage=0.0
            )
            
            persistence.save_job_state(job_state)
            
        except Exception as e:
            logger.error(f"Error persisting job {job.job_id}: {str(e)}")
    
    def _load_configuration(self) -> Dict[str, Any]:
        """Load queue manager configuration"""
        try:
            return getattr(settings, 'ENHANCED_QUEUE_SETTINGS', {
                'PROCESSING_INTERVAL': 0.1,
                'MAX_RETRY_ATTEMPTS': 3,
                'JOB_TIMEOUT': 300,
                'ENABLE_PERSISTENCE': True
            })
        except Exception as e:
            logger.error(f"Error loading configuration: {str(e)}")
            return {}
    
    def _calculate_processing_rate(self) -> float:
        """Calculate current processing rate (jobs per minute)"""
        try:
            # Simple rate calculation based on recent performance
            total_processed = self.processing_stats['total_processed']
            time_elapsed = (datetime.now() - self.processing_stats['last_reset']).total_seconds()
            
            if time_elapsed > 0:
                return (total_processed / time_elapsed) * 60  # jobs per minute
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating processing rate: {str(e)}")
            return 0.0
    
    def _calculate_average_wait_time(self) -> float:
        """Calculate average job wait time"""
        try:
            # This would require tracking job submission and processing times
            # For now, return a simple estimate based on queue size
            total_jobs = sum(queue.qsize() for queue in self.priority_queues.values())
            processing_rate = self._calculate_processing_rate()
            
            if processing_rate > 0:
                return (total_jobs / processing_rate) * 60  # seconds
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating average wait time: {str(e)}")
            return 0.0
        
    def get_status(self) -> Dict[str, Any]:
        """
        Get comprehensive queue status as dictionary
        
        Returns:
            Dictionary with current queue information
        """
        try:
            with self._lock:
                # Count jobs by priority
                jobs_by_priority = {}
                total_jobs = 0
                for priority in JobPriority:
                    count = self.priority_queues[priority].qsize()
                    jobs_by_priority[priority.name] = count
                    total_jobs += count
                
                # Count jobs by status
                jobs_by_status = {
                    'PENDING': 0,
                    'PROCESSING': 0,
                    'COMPLETED': 0,
                    'FAILED': 0
                }
                
                # Count active jobs
                for job_id, status in self.job_status.items():
                    if status.upper() in jobs_by_status:
                        jobs_by_status[status.upper()] += 1
                    else:
                        jobs_by_status['PENDING'] += 1
                
                # Get worker count
                active_workers = 0
                if self.worker_pool:
                    active_workers = self.worker_pool.get_active_worker_count()
                
                # Return as dictionary (not QueueStatus object)
                return {
                    'total_jobs': total_jobs,
                    'pending_jobs': jobs_by_status.get('PENDING', 0),
                    'processing_jobs': jobs_by_status.get('PROCESSING', 0), 
                    'completed_jobs': jobs_by_status.get('COMPLETED', 0),
                    'failed_jobs': jobs_by_status.get('FAILED', 0),
                    'jobs_by_priority': jobs_by_priority,
                    'jobs_by_status': jobs_by_status,
                    'active_workers': active_workers,
                    'is_processing': total_jobs > 0,
                    'status': 'operational',
                    'queue_size': total_jobs,
                    'worker_count': active_workers
                }
                
        except Exception as e:
            logger.error(f"Error getting queue status: {str(e)}")
            return {
                'total_jobs': 0,
                'pending_jobs': 0,
                'processing_jobs': 0,
                'active_workers': 0,
                'is_processing': False,
                'status': 'error',
                'error': str(e),
                'queue_size': 0,
                'worker_count': 0
            }
        
    def _dispatch_job(self, job):
        """Dispatch job to appropriate processor"""
        try:
            # Ensure job_processors exists and has the needed processor
            if not hasattr(self, 'job_processors') or not self.job_processors:
                logger.warning("Job processors not initialized, initializing now...")
                self._initialize_processors_from_submit_job()
            
            # Check if processor exists for this job type
            if job.job_type not in self.job_processors:
                logger.warning(f"Processor not found for {job.job_type}, registering default...")
                self._initialize_processors_from_submit_job()
            
            # Get processor
            processor = self.job_processors.get(job.job_type)
            if processor:
                logger.info(f"Processing job {job.job_id} with {job.job_type} processor")
                result = processor(job.data, getattr(job, 'worker_id', None))
                return result
            else:
                logger.error(f"No handler found for job type: {job.job_type}")
                return {'success': False, 'error': f'No handler for job type: {job.job_type}'}
                
        except Exception as e:
            logger.error(f"Error dispatching job {job.job_id}: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _initialize_processors_from_submit_job(self):
        """Initialize the same processors that submit_job uses"""
        try:
            # Create the same inline processor function that submit_job uses
            def simple_facial_recognition_processor(job_data, worker_id=None):
                """Simple inline processor for facial recognition jobs"""
                try:
                    photo_ids = job_data.get('photo_ids', [])
                    logger.info(f"Processing facial recognition job: {len(photo_ids)} photos")
                    
                    # Import here to avoid circular imports
                    from events.models import EventPhoto
                    from facial_recognition.services import RekognitionService
                    from facial_recognition.models import FacialProfile, FaceMatchResult
                    from events.models import EventAttendance
                    from django.conf import settings
                    from django.utils import timezone
                    
                    processed_count = 0
                    failed_count = 0
                    
                    for photo_id in photo_ids:
                        try:
                            photo = EventPhoto.objects.get(id=photo_id)
                            if photo.processed_for_faces:
                                continue
                            
                            # Get photo image bytes
                            photo_file = photo.image.open('rb')
                            image_bytes = photo_file.read()
                            photo_file.close()
                            
                            # Detect faces
                            success, face_details, error = RekognitionService.detect_faces(image_bytes)
                            
                            if success and face_details:
                                # Store face data
                                photo.detected_faces = {
                                    'faces': [face_details],
                                    'face_count': 1,
                                    'processed_at': timezone.now().isoformat()
                                }
                                photo.save()
                                
                                # Match against attending users
                                attending_profiles = FacialProfile.objects.filter(
                                    user__in=EventAttendance.objects.filter(
                                        event=photo.event, 
                                        is_attending=True
                                    ).values_list('user', flat=True),
                                    is_verified=True,
                                    face_id__isnull=False
                                )
                                
                                for profile in attending_profiles:
                                    try:
                                        match_success, face_matches, _ = RekognitionService.search_faces_by_image(
                                            settings.AWS_REKOGNITION_COLLECTION_ID,
                                            image_bytes,
                                            threshold=70.0
                                        )
                                        
                                        if match_success and face_matches:
                                            for match in face_matches:
                                                if match['Face']['FaceId'] == profile.face_id:
                                                    FaceMatchResult.objects.create(
                                                        user=profile.user,
                                                        event_photo=photo,
                                                        confidence_score=match['Face']['Confidence'],
                                                        similarity_score=match['Similarity'],
                                                        bounding_box=match['Face'].get('BoundingBox', {}),
                                                        is_verified=match['Similarity'] >= 85.0,
                                                    )
                                                    break
                                    except Exception as e:
                                        logger.error(f"Error matching face: {str(e)}")
                            
                            # Mark as processed
                            photo.processed_for_faces = True
                            photo.save()
                            processed_count += 1
                            
                        except Exception as e:
                            logger.error(f"Error processing photo {photo_id}: {str(e)}")
                            failed_count += 1
                    
                    logger.info(f"Facial recognition job completed: {processed_count} processed, {failed_count} failed")
                    return {
                        'success': True,
                        'processed_count': processed_count,
                        'failed_count': failed_count
                    }
                    
                except Exception as e:
                    logger.error(f"Error in facial recognition processor: {str(e)}")
                    return {
                        'success': False,
                        'error': str(e),
                        'processed_count': 0,
                        'failed_count': len(photo_ids) if 'photo_ids' in locals() else 0
                    }
            
            # Register the same processors as submit_job
            self.job_processors = {
                'FACIAL_RECOGNITION': simple_facial_recognition_processor,
                'PHOTO_PROCESSING': simple_facial_recognition_processor,
                'BULK_UPLOAD': simple_facial_recognition_processor,
                'MAINTENANCE': simple_facial_recognition_processor
            }
            logger.info(f"Initialized processors for job dispatcher: {list(self.job_processors.keys())}")
            
        except Exception as e:
            logger.error(f"Error initializing processors: {str(e)}")
    
    def _dispatch_next_job(self):
        """Dispatch next available job from queues"""
        try:
            # Try each priority level in order
            for priority in JobPriority:
                queue = self.priority_queues[priority]
                
                try:
                    # Get next job from queue (non-blocking)
                    _, job = queue.get_nowait()
                    
                    # Use job dispatcher to process the job
                    if self.job_dispatcher:
                        success = self.job_dispatcher.dispatch_job(job)
                        if success:
                            logger.debug(f"Job {job.job_id} dispatched successfully")
                        else:
                            logger.error(f"Failed to dispatch job {job.job_id}")
                            # Re-queue with retry logic
                            self._handle_failed_job(job)
                    else:
                        logger.error("No job dispatcher available")
                        # Re-queue the job
                        queue.put((priority.value, job))
                    
                    return  # Exit after processing one job
                    
                except Empty:
                    continue  # Try next priority level
                    
        except Exception as e:
            logger.error(f"Error dispatching job: {str(e)}")