# queue_system/queue_engine/queue_persistence.py
"""
Queue Persistence Layer
Handles database operations and caching for the queue system
"""

import json
import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from django.core.cache import cache
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Avg, Count

from ..models import QueueJob, QueueMetrics, WorkerStatus

logger = logging.getLogger('queue_system.persistence')


@dataclass
class JobState:
    """Data class for job state information"""
    job_id: str  # UUID as string for JSON serialization
    job_type: str
    priority: str
    status: str
    data: Dict[str, Any]
    options: Dict[str, Any]
    user_id: Optional[str]  # Changed to str to handle UUID strings
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    retry_count: int = 0
    error_message: Optional[str] = None
    worker_id: Optional[str] = None
    max_retries: int = 3  # ADD THIS MISSING FIELD
    timeout_seconds: int = 300  # ADD THIS MISSING FIELD
    estimated_duration: Optional[int] = None  # ADD THIS MISSING FIELD
    result: Optional[Dict[str, Any]] = None  # ADD THIS MISSING FIELD
    progress_percentage: float = 0.0  # ADD THIS LINE

class QueuePersistence:
    """
    Handles all persistence operations for the queue system
    Provides caching layer over database operations
    """
    def save_job_state(self, job_state: JobState) -> bool:
        """
        Save job state to database by converting to QueueJob model

        Args:
            job_state: JobState dataclass instance

        Returns:
            True if successful, False otherwise
        """
        try:
            from ..models import QueueJob

            # Convert JobState to QueueJob model instance with correct field mapping
            queue_job = QueueJob(
                # Map job_id to id (UUID primary key) - convert string to UUID if needed
                id=uuid.UUID(job_state.job_id) if isinstance(job_state.job_id, str) else job_state.job_id,
                job_type=job_state.job_type,
                priority=job_state.priority,
                status=job_state.status.upper(),  # Ensure uppercase
                # Map data to job_data
                job_data=job_state.data,
                # Map result to result_data
                result_data=job_state.result,
                worker_id=job_state.worker_id or '',  # Ensure empty string instead of None
                retry_count=job_state.retry_count,
                max_retries=job_state.max_retries,
                error_message=job_state.error_message or '',  # Ensure empty string instead of None
                timeout_seconds=job_state.timeout_seconds,
                created_at=job_state.created_at,
                updated_at=job_state.updated_at,
                started_at=job_state.started_at,
                completed_at=job_state.completed_at,
                # Store options and other fields in metadata
                metadata={
                    'options': job_state.options,
                    'user_id': job_state.user_id,
                    'progress_percentage': job_state.progress_percentage,
                    'estimated_duration': job_state.estimated_duration,
                }
            )

            # Save using existing save_job method
            return self.save_job(queue_job)

        except Exception as e:
            logger.error(f" Error saving job state {job_state.job_id}: {str(e)}")
            return False
    
    def __init__(self):
        self.cache_prefix = 'queue_persist'
        self.cache_timeout = 300  # 5 minutes
        
        logger.debug("💾 Queue Persistence initialized")
    
    def save_job(self, job: QueueJob) -> bool:
        """
        Save job to database with caching
        
        Args:
            job: QueueJob instance to save
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with transaction.atomic():
                job.save()
                
                # Invalidate related cache entries
                self._invalidate_queue_cache(job.priority)
                
                logger.debug(f"💾 Job {job.id} saved to database")
                return True
                
        except Exception as e:
            logger.error(f" Error saving job {job.id}: {str(e)}")
            return False
    
    def get_next_job(self, priority: str) -> Optional[Dict]:
        """
        Get next available job for processing
        
        Args:
            priority: Priority level to get job from
            
        Returns:
            Job data dictionary or None
        """
        try:
            # First check cache for hot jobs
            cached_job = self._get_cached_next_job(priority)
            if cached_job:
                return cached_job
            
            # Query database for next job
            job = QueueJob.objects.filter(
                priority=priority,
                status='QUEUED'
            ).filter(
                Q(scheduled_at__isnull=True) |
                Q(scheduled_at__lte=timezone.now())
            ).order_by('created_at').first()
            
            if job:
                job_data = self._job_to_dict(job)
                
                # Cache for quick access
                self._cache_next_job(priority, job_data)
                
                return job_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting next job for {priority}: {str(e)}")
            return None
    
    def mark_job_processing(self, job_id: str) -> bool:
        """Mark job as being processed"""
        try:
            with transaction.atomic():
                job = QueueJob.objects.select_for_update().get(id=job_id)
                job.status = 'PROCESSING'
                job.started_at = timezone.now()
                
                # Calculate queue time
                if job.created_at:
                    job.queue_time = (job.started_at - job.created_at).total_seconds()
                
                job.save()
                
                # Invalidate cache
                self._invalidate_job_cache(job_id)
                self._invalidate_queue_cache(job.priority)
                
                logger.debug(f"🔄 Job {job_id} marked as processing")
                return True
                
        except QueueJob.DoesNotExist:
            logger.error(f"Job {job_id} not found for processing")
            return False
        except Exception as e:
            logger.error(f"Error marking job {job_id} as processing: {str(e)}")
            return False
    
    def mark_job_completed(self, job_id: str, result: Optional[Dict] = None) -> bool:
        """Mark job as completed"""
        try:
            with transaction.atomic():
                job = QueueJob.objects.select_for_update().get(id=job_id)
                job.status = 'COMPLETED'
                job.completed_at = timezone.now()
                
                if result:
                    job.result_data = result
                
                # Calculate processing time
                if job.started_at:
                    job.processing_time = (job.completed_at - job.started_at).total_seconds()
                
                job.save()
                
                # Invalidate cache
                self._invalidate_job_cache(job_id)
                self._invalidate_queue_cache(job.priority)
                
                logger.debug(f"✅ Job {job_id} marked as completed")
                return True
                
        except QueueJob.DoesNotExist:
            logger.error(f"Job {job_id} not found for completion")
            return False
        except Exception as e:
            logger.error(f"Error marking job {job_id} as completed: {str(e)}")
            return False
    
    def mark_job_failed(self, job_id: str, error_message: str) -> bool:
        """Mark job as failed"""
        try:
            with transaction.atomic():
                job = QueueJob.objects.select_for_update().get(id=job_id)
                job.status = 'FAILED'
                job.completed_at = timezone.now()
                job.error_message = error_message
                
                # Calculate processing time
                if job.started_at:
                    job.processing_time = (job.completed_at - job.started_at).total_seconds()
                
                job.save()
                
                # Invalidate cache
                self._invalidate_job_cache(job_id)
                self._invalidate_queue_cache(job.priority)
                
                logger.debug(f"Job {job_id} marked as failed")
                return True
                
        except QueueJob.DoesNotExist:
            logger.error(f"Job {job_id} not found for failure")
            return False
        except Exception as e:
            logger.error(f"Error marking job {job_id} as failed: {str(e)}")
            return False
    
    def get_queue_stats(self, priority: str) -> Dict:
        """Get statistics for a specific priority queue"""
        try:
            # Check cache first
            cache_key = f"{self.cache_prefix}_stats_{priority}"
            cached_stats = cache.get(cache_key)
            
            if cached_stats:
                return cached_stats
            
            # Calculate stats from database
            today = timezone.now().date()
            
            stats = {
                'pending': QueueJob.objects.filter(
                    priority=priority, 
                    status='QUEUED'
                ).count(),
                
                'processing': QueueJob.objects.filter(
                    priority=priority, 
                    status='PROCESSING'
                ).count(),
                
                'completed_today': QueueJob.objects.filter(
                    priority=priority,
                    status='COMPLETED',
                    completed_at__date=today
                ).count(),
                
                'failed_today': QueueJob.objects.filter(
                    priority=priority,
                    status='FAILED',
                    completed_at__date=today
                ).count(),
            }
            
            # Calculate average processing time
            avg_processing_time = QueueJob.objects.filter(
                priority=priority,
                status='COMPLETED',
                processing_time__isnull=False,
                completed_at__gte=timezone.now() - timedelta(hours=24)
            ).aggregate(avg=Avg('processing_time'))['avg']
            
            stats['avg_processing_time'] = avg_processing_time or 0
            
            # Calculate average queue time
            avg_queue_time = QueueJob.objects.filter(
                priority=priority,
                status__in=['COMPLETED', 'FAILED'],
                queue_time__isnull=False,
                completed_at__gte=timezone.now() - timedelta(hours=24)
            ).aggregate(avg=Avg('queue_time'))['avg']
            
            stats['avg_queue_time'] = avg_queue_time or 0
            
            # Cache the stats
            cache.set(cache_key, stats, timeout=self.cache_timeout)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting queue stats for {priority}: {str(e)}")
            return {
                'pending': 0,
                'processing': 0,
                'completed_today': 0,
                'failed_today': 0,
                'avg_processing_time': 0,
                'avg_queue_time': 0
            }
    
    def get_job_by_id(self, job_id: str) -> Optional[QueueJob]:
        """Get job by ID with caching"""
        try:
            # Check cache first
            cache_key = f"{self.cache_prefix}_job_{job_id}"
            cached_job_data = cache.get(cache_key)
            
            if cached_job_data:
                # Return job instance from cached data
                try:
                    job = QueueJob.objects.get(id=job_id)
                    return job
                except QueueJob.DoesNotExist:
                    # Cache is stale, invalidate it
                    cache.delete(cache_key)
            
            # Get from database
            job = QueueJob.objects.get(id=job_id)
            
            # Cache the job data
            job_data = self._job_to_dict(job)
            cache.set(cache_key, job_data, timeout=self.cache_timeout)
            
            return job
            
        except QueueJob.DoesNotExist:
            logger.warning(f"⚠️ Job {job_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error getting job {job_id}: {str(e)}")
            return None
    
    def get_jobs_by_status(self, status: str, priority: Optional[str] = None, limit: int = 100) -> List[QueueJob]:
        """Get jobs by status with optional priority filter"""
        try:
            queryset = QueueJob.objects.filter(status=status)
            
            if priority:
                queryset = queryset.filter(priority=priority)
            
            return list(queryset.order_by('-created_at')[:limit])
            
        except Exception as e:
            logger.error(f"Error getting jobs by status {status}: {str(e)}")
            return []
    
    def get_job_history(self, job_type: Optional[str] = None, 
                       priority: Optional[str] = None,
                       hours: int = 24,
                       limit: int = 1000) -> List[QueueJob]:
        """Get job history with filters"""
        try:
            cutoff_time = timezone.now() - timedelta(hours=hours)
            
            queryset = QueueJob.objects.filter(created_at__gte=cutoff_time)
            
            if job_type:
                queryset = queryset.filter(job_type=job_type)
            
            if priority:
                queryset = queryset.filter(priority=priority)
            
            return list(queryset.order_by('-created_at')[:limit])
            
        except Exception as e:
            logger.error(f"Error getting job history: {str(e)}")
            return []
    
    def cleanup_old_jobs(self, days: int = 30) -> int:
        """Clean up old completed and failed jobs"""
        try:
            cutoff_date = timezone.now() - timedelta(days=days)
            
            with transaction.atomic():
                # Delete old completed and failed jobs
                deleted_count, _ = QueueJob.objects.filter(
                    status__in=['COMPLETED', 'FAILED'],
                    completed_at__lt=cutoff_date
                ).delete()
                
                logger.info(f"🧹 Cleaned up {deleted_count} old jobs")
                
                # Invalidate all queue caches
                self._invalidate_all_queue_caches()
                
                return deleted_count
                
        except Exception as e:
            logger.error(f"Error cleaning up old jobs: {str(e)}")
            return 0
    
    def get_performance_metrics(self, hours: int = 24) -> Dict:
        """Get performance metrics for the specified time period"""
        try:
            cutoff_time = timezone.now() - timedelta(hours=hours)
            
            # Base queryset for the time period
            jobs = QueueJob.objects.filter(created_at__gte=cutoff_time)
            
            metrics = {
                'total_jobs': jobs.count(),
                'completed_jobs': jobs.filter(status='COMPLETED').count(),
                'failed_jobs': jobs.filter(status='FAILED').count(),
                'pending_jobs': jobs.filter(status='QUEUED').count(),
                'processing_jobs': jobs.filter(status='PROCESSING').count(),
            }
            
            # Calculate success rate
            total_finished = metrics['completed_jobs'] + metrics['failed_jobs']
            metrics['success_rate'] = (
                (metrics['completed_jobs'] / total_finished * 100) 
                if total_finished > 0 else 0
            )
            
            # Performance metrics
            completed_jobs = jobs.filter(
                status='COMPLETED',
                processing_time__isnull=False
            )
            
            if completed_jobs.exists():
                processing_times = completed_jobs.aggregate(
                    avg=Avg('processing_time'),
                    min=Min('processing_time'),
                    max=Max('processing_time')
                )
                
                metrics.update({
                    'avg_processing_time': processing_times['avg'] or 0,
                    'min_processing_time': processing_times['min'] or 0,
                    'max_processing_time': processing_times['max'] or 0,
                })
                
                # Queue time metrics
                queue_times = completed_jobs.filter(
                    queue_time__isnull=False
                ).aggregate(
                    avg=Avg('queue_time'),
                    min=Min('queue_time'),
                    max=Max('queue_time')
                )
                
                metrics.update({
                    'avg_queue_time': queue_times['avg'] or 0,
                    'min_queue_time': queue_times['min'] or 0,
                    'max_queue_time': queue_times['max'] or 0,
                })
            else:
                metrics.update({
                    'avg_processing_time': 0,
                    'min_processing_time': 0,
                    'max_processing_time': 0,
                    'avg_queue_time': 0,
                    'min_queue_time': 0,
                    'max_queue_time': 0,
                })
            
            # Throughput calculation
            throughput_per_hour = metrics['completed_jobs'] / hours if hours > 0 else 0
            metrics['throughput_per_hour'] = throughput_per_hour
            metrics['throughput_per_minute'] = throughput_per_hour / 60
            
            # Breakdown by priority
            priority_breakdown = {}
            for priority in ['EMERGENCY', 'HIGH', 'STANDARD', 'LOW', 'MAINTENANCE']:
                priority_jobs = jobs.filter(priority=priority)
                priority_breakdown[priority] = {
                    'total': priority_jobs.count(),
                    'completed': priority_jobs.filter(status='COMPLETED').count(),
                    'failed': priority_jobs.filter(status='FAILED').count(),
                    'pending': priority_jobs.filter(status='QUEUED').count(),
                }
            
            metrics['priority_breakdown'] = priority_breakdown
            
            # Breakdown by job type
            job_type_breakdown = {}
            job_types = jobs.values_list('job_type', flat=True).distinct()
            
            for job_type in job_types:
                type_jobs = jobs.filter(job_type=job_type)
                job_type_breakdown[job_type] = {
                    'total': type_jobs.count(),
                    'completed': type_jobs.filter(status='COMPLETED').count(),
                    'failed': type_jobs.filter(status='FAILED').count(),
                    'avg_processing_time': type_jobs.filter(
                        status='COMPLETED',
                        processing_time__isnull=False
                    ).aggregate(avg=Avg('processing_time'))['avg'] or 0
                }
            
            metrics['job_type_breakdown'] = job_type_breakdown
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {}
    
    def _job_to_dict(self, job: QueueJob) -> Dict:
        """Convert QueueJob to dictionary with correct field mapping"""
        return {
            'id': str(job.id),
            'job_type': job.job_type,
            'priority': job.priority,
            'status': job.status,
            'job_data': job.job_data,
            'result_data': job.result_data,
            'metadata': job.metadata,
            'created_at': job.created_at.isoformat(),
            'updated_at': job.updated_at.isoformat(),
            'scheduled_at': job.scheduled_at.isoformat() if job.scheduled_at else None,
            'started_at': job.started_at.isoformat() if job.started_at else None,
            'completed_at': job.completed_at.isoformat() if job.completed_at else None,
            'retry_count': job.retry_count,
            'max_retries': job.max_retries,
            'timeout_seconds': job.timeout_seconds,
            'worker_id': job.worker_id,
            'error_message': job.error_message,
            'processing_time': job.processing_time,
        }
    
    def _get_cached_next_job(self, priority: str) -> Optional[Dict]:
        """Get cached next job for priority"""
        try:
            cache_key = f"{self.cache_prefix}_next_{priority}"
            return cache.get(cache_key)
        except Exception as e:
            logger.error(f"Error getting cached next job: {str(e)}")
            return None
    
    def _cache_next_job(self, priority: str, job_data: Dict):
        """Cache next job for quick access"""
        try:
            cache_key = f"{self.cache_prefix}_next_{priority}"
            cache.set(cache_key, job_data, timeout=60)  # Short timeout for hot cache
        except Exception as e:
            logger.error(f"Error caching next job: {str(e)}")
    
    def _invalidate_job_cache(self, job_id: str):
        """Invalidate cache for specific job"""
        try:
            cache_key = f"{self.cache_prefix}_job_{job_id}"
            cache.delete(cache_key)
        except Exception as e:
            logger.error(f"Error invalidating job cache: {str(e)}")
    
    def _invalidate_queue_cache(self, priority: str):
        """Invalidate cache for specific priority queue"""
        try:
            cache_keys = [
                f"{self.cache_prefix}_stats_{priority}",
                f"{self.cache_prefix}_next_{priority}",
            ]
            
            cache.delete_many(cache_keys)
        except Exception as e:
            logger.error(f"Error invalidating queue cache: {str(e)}")
    
    def _invalidate_all_queue_caches(self):
        """Invalidate all queue-related caches"""
        try:
            # This is a simple implementation - in production you might want
            # more sophisticated cache invalidation
            priorities = ['EMERGENCY', 'HIGH', 'STANDARD', 'LOW', 'MAINTENANCE']
            
            for priority in priorities:
                self._invalidate_queue_cache(priority)
                
        except Exception as e:
            logger.error(f"Error invalidating all queue caches: {str(e)}")

# Import required for performance metrics
from django.db.models import Min, Max