# queue_system/queue_engine/worker_pool.py
"""
Worker Pool for PhotoFish Enhanced Queue System
Manages multiple worker threads for parallel job processing
"""

import logging
import threading
import time
import uuid
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from queue import Queue, Empty
from enum import Enum
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)

class WorkerStatus(Enum):
    """Worker status enumeration"""
    IDLE = "idle"
    BUSY = "busy"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class WorkerInfo:
    """Information about a worker"""
    worker_id: str
    priority_levels: List[str]
    status: WorkerStatus
    current_job_id: Optional[str]
    jobs_processed: int
    jobs_failed: int
    created_at: datetime
    last_activity: datetime
    total_processing_time: float
    average_processing_time: float

class Worker:
    """
    Individual worker thread for processing jobs
    """
    
    def __init__(self, worker_id: str, priority_levels: List[str], 
                 job_processor: Callable = None):
        self.worker_id = worker_id
        self.priority_levels = priority_levels
        self.job_processor = job_processor
        
        # Worker state
        self.status = WorkerStatus.IDLE
        self.current_job = None
        self.thread = None
        self.is_running = False
        
        # Statistics
        self.jobs_processed = 0
        self.jobs_failed = 0
        self.total_processing_time = 0.0
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        
        # Job queue for this worker
        self.job_queue = Queue(maxsize=10)
        
        # Threading
        self._lock = threading.Lock()
    
    def start(self) -> bool:
        """
        Start the worker thread
        
        Returns:
            Success status
        """
        try:
            if self.is_running:
                logger.debug(f"Worker {self.worker_id} is already running")  # Changed to DEBUG
                return True
            
            self.is_running = True
            self.status = WorkerStatus.IDLE
            
            self.thread = threading.Thread(
                target=self._worker_loop,
                name=f"Worker-{self.worker_id}",
                daemon=True
            )
            self.thread.start()
            
            # FIXED: Use debug level instead of info to reduce startup noise
            logger.debug(f"Worker {self.worker_id} started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting worker {self.worker_id}: {str(e)}")
            return False
    
    def stop(self, timeout: int = 30) -> bool:
        """
        Stop the worker gracefully
        
        Args:
            timeout: Maximum time to wait for worker to stop
            
        Returns:
            Success status
        """
        try:
            if not self.is_running:
                return True
            
            logger.info(f"Stopping worker {self.worker_id}")
            
            with self._lock:
                self.is_running = False
                self.status = WorkerStatus.STOPPING
            
            # Wait for current job to complete
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=timeout)
                
                if self.thread.is_alive():
                    logger.warning(f"Worker {self.worker_id} did not stop within timeout")
                    return False
            
            self.status = WorkerStatus.STOPPED
            logger.info(f"Worker {self.worker_id} stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping worker {self.worker_id}: {str(e)}")
            return False
    
    def assign_job(self, job: Any) -> bool:
        """
        Assign a job to this worker
        
        Args:
            job: Job to assign
            
        Returns:
            Success status
        """
        try:
            if self.status != WorkerStatus.IDLE:
                return False
            
            # Add job to worker's queue
            try:
                self.job_queue.put_nowait(job)
                return True
            except:
                return False
                
        except Exception as e:
            logger.error(f"Error assigning job to worker {self.worker_id}: {str(e)}")
            return False
    
    def get_info(self) -> WorkerInfo:
        """
        Get worker information
        
        Returns:
            Worker information
        """
        try:
            with self._lock:
                avg_processing_time = 0.0
                if self.jobs_processed > 0:
                    avg_processing_time = self.total_processing_time / self.jobs_processed
                
                return WorkerInfo(
                    worker_id=self.worker_id,
                    priority_levels=self.priority_levels,
                    status=self.status,
                    current_job_id=self.current_job.job_id if self.current_job else None,
                    jobs_processed=self.jobs_processed,
                    jobs_failed=self.jobs_failed,
                    created_at=self.created_at,
                    last_activity=self.last_activity,
                    total_processing_time=self.total_processing_time,
                    average_processing_time=avg_processing_time
                )
                
        except Exception as e:
            logger.error(f"Error getting worker info for {self.worker_id}: {str(e)}")
            return WorkerInfo(
                worker_id=self.worker_id,
                priority_levels=self.priority_levels,
                status=WorkerStatus.ERROR,
                current_job_id=None,
                jobs_processed=0,
                jobs_failed=0,
                created_at=self.created_at,
                last_activity=datetime.now(),
                total_processing_time=0.0,
                average_processing_time=0.0
            )
    
    def _worker_loop(self):
        """Main worker processing loop"""
        # FIXED: Use debug level for worker loop start
        logger.debug(f"Worker {self.worker_id} loop started")
        
        while self.is_running:
            try:
                # Wait for a job
                try:
                    job = self.job_queue.get(timeout=1.0)
                except Empty:
                    continue
                
                # Process the job
                self._process_job(job)
                
                # Mark task as done
                self.job_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in worker {self.worker_id} loop: {str(e)}")
                with self._lock:
                    self.status = WorkerStatus.ERROR
                time.sleep(1)
        
        logger.debug(f"Worker {self.worker_id} loop ended")
    
    def _process_job(self, job):
        """Process a single job"""
        start_time = time.time()
        
        try:
            with self._lock:
                self.status = WorkerStatus.BUSY
                self.current_job = job
                self.last_activity = datetime.now()
            
            logger.info(f"Worker {self.worker_id} processing job {job.job_id}")
            
            # Process the job using the job processor
            if self.job_processor:
                success = self.job_processor(job, self.worker_id)
            else:
                # Default processing (placeholder)
                success = self._default_job_processing(job)
            
            # Update statistics
            processing_time = time.time() - start_time
            
            with self._lock:
                self.jobs_processed += 1
                self.total_processing_time += processing_time
                
                if not success:
                    self.jobs_failed += 1
                
                self.current_job = None
                self.status = WorkerStatus.IDLE
                self.last_activity = datetime.now()
            
            logger.info(f"Worker {self.worker_id} completed job {job.job_id} in {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Worker {self.worker_id} error processing job {job.job_id}: {str(e)}")
            
            with self._lock:
                self.jobs_failed += 1
                self.current_job = None
                self.status = WorkerStatus.IDLE
                self.last_activity = datetime.now()
    
    def _default_job_processing(self, job) -> bool:
        """Default job processing when no processor is provided"""
        try:
            # Simulate processing time
            time.sleep(0.1)
            
            # Simple job processing based on job type
            if job.job_type == 'facial_recognition' or job.job_type == 'FACIAL_RECOGNITION':
                return self._process_facial_recognition_job(job)
            elif job.job_type == 'photo_processing':
                return self._process_photo_job(job)
            else:
                logger.warning(f"Unknown job type: {job.job_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error in default job processing: {str(e)}")
            return False
    
    def _process_facial_recognition_job(self, job) -> bool:
        """Process facial recognition job"""
        try:
            # This would integrate with the actual facial recognition system
            logger.info(f"Processing facial recognition job {job.id}")

            # Simulate processing
            time.sleep(1.0)

            return True

        except Exception as e:
            logger.error(f"Error processing facial recognition job: {str(e)}")
            return False
    
    def _process_photo_job(self, job) -> bool:
        """Process photo processing job"""
        try:
            # This would integrate with photo processing system
            logger.info(f"Processing photo job {job.id}")

            # Simulate processing
            time.sleep(0.5)

            return True
            
        except Exception as e:
            logger.error(f"Error processing photo job: {str(e)}")
            return False


class WorkerPool:
    """
    Manages a pool of worker threads for job processing
    """
    
    def __init__(self):
        # Worker management
        self.workers = {}  # worker_id -> Worker
        self.worker_configs = self._load_worker_configurations()
        
        # Pool state
        self.is_running = False
        self._lock = threading.RLock()
        
        # Load balancing
        self.round_robin_counters = {}
        
        # Statistics
        self.pool_stats = {
            'total_workers_created': 0,
            'total_jobs_processed': 0,
            'total_jobs_failed': 0,
            'pool_started_at': None
        }
    
    def start(self) -> bool:
        """
        Start the worker pool
        
        Returns:
            Success status
        """
        try:
            if self.is_running:
                logger.warning("Worker pool is already running")
                return True
            
            self.is_running = True
            self.pool_stats['pool_started_at'] = datetime.now()
            
            # Create initial workers
            success = self._create_initial_workers()
            
            if success:
                logger.info(f"Worker pool started with {len(self.workers)} workers")
                return True
            else:
                logger.error("Failed to create initial workers")
                return False
                
        except Exception as e:
            logger.error(f"Error starting worker pool: {str(e)}")
            return False
    
    def stop(self, timeout: int = 60) -> bool:
        """
        Stop the worker pool gracefully
        
        Args:
            timeout: Maximum time to wait for all workers to stop
            
        Returns:
            Success status
        """
        try:
            if not self.is_running:
                return True
            
            logger.info("Stopping worker pool...")
            
            self.is_running = False
            
            # Stop all workers
            with self._lock:
                workers_to_stop = list(self.workers.values())
            
            # Stop workers in parallel
            stop_threads = []
            for worker in workers_to_stop:
                thread = threading.Thread(
                    target=lambda w=worker: w.stop(timeout=30),
                    daemon=True
                )
                thread.start()
                stop_threads.append(thread)
            
            # Wait for all workers to stop
            start_time = time.time()
            for thread in stop_threads:
                remaining_time = max(0, timeout - (time.time() - start_time))
                thread.join(timeout=remaining_time)
            
            # Clear workers
            with self._lock:
                self.workers.clear()
            
            logger.info("Worker pool stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping worker pool: {str(e)}")
            return False
    
    def assign_job(self, job, priority_level: str) -> bool:
        """
        Assign a job to an available worker
        
        Args:
            job: Job to assign
            priority_level: Priority level for worker selection
            
        Returns:
            Success status
        """
        try:
            # Find available worker for this priority level
            worker = self._select_worker(priority_level)
            
            if worker:
                success = worker.assign_job(job)
                if success:
                    logger.debug(f"Job {job.job_id} assigned to worker {worker.worker_id}")
                    return True
                else:
                    logger.warning(f"Failed to assign job {job.job_id} to worker {worker.worker_id}")
            else:
                logger.warning(f"No available worker for priority level {priority_level}")
            
            return False
            
        except Exception as e:
            logger.error(f"Error assigning job: {str(e)}")
            return False
    
    def add_workers(self, priority_level: str, count: int = 1) -> bool:
        """Add workers for a specific priority level"""
        try:
            added_count = 0
            
            for i in range(count):
                worker_id = f"{priority_level.lower()}_{uuid.uuid4().hex[:8]}"
                
                worker = Worker(
                    worker_id=worker_id,
                    priority_levels=[priority_level],
                    job_processor=self._get_job_processor()
                )
                
                if worker.start():
                    with self._lock:
                        self.workers[worker_id] = worker
                        self.pool_stats['total_workers_created'] += 1
                    
                    added_count += 1
                    # REMOVED: Individual worker logging
                    # logger.info(f"Added worker {worker_id} for {priority_level} priority")
                else:
                    logger.error(f"Failed to start worker for {priority_level} priority")
                    return False
            
            # Only log summary when adding multiple workers
            if added_count > 0 and count > 1:
                logger.info(f"Added {added_count} worker(s) for {priority_level} priority")
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding workers for {priority_level}: {str(e)}")
            return False
    
    def remove_workers(self, priority_level: str, count: int) -> bool:
        """
        Remove workers for a specific priority level
        
        Args:
            priority_level: Priority level to remove workers from
            count: Number of workers to remove
            
        Returns:
            Success status
        """
        try:
            # Find workers for this priority level
            with self._lock:
                eligible_workers = [
                    worker for worker in self.workers.values()
                    if (priority_level in worker.priority_levels and 
                        worker.status == WorkerStatus.IDLE)
                ]
            
            # Remove the requested number of workers
            workers_removed = 0
            for worker in eligible_workers[:count]:
                if worker.stop():
                    with self._lock:
                        if worker.worker_id in self.workers:
                            del self.workers[worker.worker_id]
                    
                    workers_removed += 1
                    logger.info(f"Removed worker {worker.worker_id}")
            
            logger.info(f"Removed {workers_removed} workers from {priority_level} priority")
            return workers_removed > 0
            
        except Exception as e:
            logger.error(f"Error removing workers: {str(e)}")
            return False
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """
        Get worker pool statistics
        
        Returns:
            Pool statistics
        """
        try:
            with self._lock:
                workers_by_status = {}
                workers_by_priority = {}
                total_jobs_processed = 0
                total_jobs_failed = 0
                
                for worker in self.workers.values():
                    # Count by status
                    status = worker.status.value
                    workers_by_status[status] = workers_by_status.get(status, 0) + 1
                    
                    # Count by priority
                    for priority in worker.priority_levels:
                        workers_by_priority[priority] = workers_by_priority.get(priority, 0) + 1
                    
                    # Aggregate job stats
                    total_jobs_processed += worker.jobs_processed
                    total_jobs_failed += worker.jobs_failed
                
                return {
                    'total_workers': len(self.workers),
                    'workers_by_status': workers_by_status,
                    'workers_by_priority': workers_by_priority,
                    'total_jobs_processed': total_jobs_processed,
                    'total_jobs_failed': total_jobs_failed,
                    'success_rate': ((total_jobs_processed - total_jobs_failed) / total_jobs_processed * 100) if total_jobs_processed > 0 else 0,
                    'pool_uptime_seconds': (datetime.now() - self.pool_stats['pool_started_at']).total_seconds() if self.pool_stats['pool_started_at'] else 0
                }
                
        except Exception as e:
            logger.error(f"Error getting worker stats: {str(e)}")
            return {}
    
    def get_active_worker_count(self) -> int:
        """
        Get count of active workers
        
        Returns:
            Number of active workers
        """
        try:
            with self._lock:
                return len([
                    worker for worker in self.workers.values()
                    if worker.status in [WorkerStatus.IDLE, WorkerStatus.BUSY]
                ])
                
        except Exception as e:
            logger.error(f"Error getting active worker count: {str(e)}")
            return 0
    
    def get_worker_info(self, worker_id: str) -> Optional[WorkerInfo]:
        """
        Get information about a specific worker
        
        Args:
            worker_id: Worker ID
            
        Returns:
            Worker information or None if not found
        """
        try:
            with self._lock:
                worker = self.workers.get(worker_id)
                if worker:
                    return worker.get_info()
                return None
                
        except Exception as e:
            logger.error(f"Error getting worker info for {worker_id}: {str(e)}")
            return None
    
    def _create_initial_workers(self) -> bool:
        """Create initial set of workers based on configuration"""
        try:
            success = True
            total_workers_created = 0
            
            # Collect worker creation summary instead of logging each worker
            worker_summary = {}
            
            for priority_level, config in self.worker_configs.items():
                min_workers = config.get('min_workers', 1)
                workers_created = 0
                
                for i in range(min_workers):
                    # Create workers silently (without individual logs)
                    worker_id = f"{priority_level.lower()}_{uuid.uuid4().hex[:8]}"
                    
                    worker = Worker(
                        worker_id=worker_id,
                        priority_levels=[priority_level],
                        job_processor=self._get_job_processor()
                    )
                    
                    if worker.start():
                        with self._lock:
                            self.workers[worker_id] = worker
                        workers_created += 1
                        total_workers_created += 1
                    else:
                        success = False
                
                # Store summary for this priority level
                if workers_created > 0:
                    worker_summary[priority_level] = workers_created
            
            # Log summary instead of individual workers
            for priority_level, count in worker_summary.items():
                logger.info(f"Added {count} worker(s) for {priority_level} priority")
            
            # Single summary log
            if total_workers_created > 0:
                logger.info(f"Worker pool started with {total_workers_created} workers")
            
            return success
            
        except Exception as e:
            logger.error(f"Error creating initial workers: {str(e)}")
            return False

    
    def _select_worker(self, priority_level: str) -> Optional[Worker]:
        """Select best available worker for a priority level"""
        try:
            with self._lock:
                # Find idle workers for this priority level
                available_workers = [
                    worker for worker in self.workers.values()
                    if (priority_level in worker.priority_levels and 
                        worker.status == WorkerStatus.IDLE)
                ]
                
                if not available_workers:
                    return None
                
                # Use round-robin selection
                if priority_level not in self.round_robin_counters:
                    self.round_robin_counters[priority_level] = 0
                
                worker_index = self.round_robin_counters[priority_level] % len(available_workers)
                selected_worker = available_workers[worker_index]
                
                self.round_robin_counters[priority_level] += 1
                
                return selected_worker
                
        except Exception as e:
            logger.error(f"Error selecting worker: {str(e)}")
            return None
    
    def _get_job_processor(self) -> Optional[Callable]:
        """Get job processor function"""
        try:
            # This would return the actual job processing function
            # For now, return None to use default processing
            return None
            
        except Exception as e:
            logger.error(f"Error getting job processor: {str(e)}")
            return None
    
    def _load_worker_configurations(self) -> Dict[str, Dict[str, Any]]:
        """Load worker pool configurations from settings"""
        try:
            enhanced_settings = getattr(settings, 'ENHANCED_QUEUE_SETTINGS', {})
            return enhanced_settings.get('WORKER_POOLS', {
                'EMERGENCY': {'min_workers': 2, 'max_workers': 4},
                'HIGH': {'min_workers': 3, 'max_workers': 6},
                'STANDARD': {'min_workers': 2, 'max_workers': 4},
                'LOW': {'min_workers': 1, 'max_workers': 2},
                'MAINTENANCE': {'min_workers': 1, 'max_workers': 1}
            })
        except Exception as e:
            logger.error(f"Error loading worker configurations: {str(e)}")
            return {}
    
    def set_job_dispatcher(self, job_dispatcher):
        """
        Set the job dispatcher for this worker pool

        Args:
            job_dispatcher: JobDispatcher instance
        """
        try:
            self.job_dispatcher = job_dispatcher
            logger.debug(f"Job dispatcher set for worker pool")
        except Exception as e:
            logger.error(f"Error setting job dispatcher for worker pool: {str(e)}")

    # Also update the _default_job_processing method to use the dispatcher:
    def _default_job_processing(self, job) -> bool:
        """Default job processing when no processor is provided"""
        try:
            # Use job dispatcher if available
            if hasattr(self, 'job_dispatcher') and self.job_dispatcher:
                handler = self.job_dispatcher._find_handler(job.job_type)
                if handler:
                    result = handler.process_job(job, 'worker_pool')  # Use generic worker_id
                    return result.success
            
            # Fallback to original logic
            time.sleep(0.1)
            
            if job.job_type == 'facial_recognition' or job.job_type == 'FACIAL_RECOGNITION':
                return self._process_facial_recognition_job(job)
            elif job.job_type == 'photo_processing':
                return self._process_photo_job(job)
            else:
                logger.warning(f"Unknown job type: {job.job_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error in default job processing: {str(e)}")
            return False