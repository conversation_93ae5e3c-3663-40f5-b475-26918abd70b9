# queue_system/resource_manager/cleanup_service.py
"""
Cleanup service for PhotoFish Enhanced Queue System
Manages automated cleanup and maintenance tasks
"""

import logging
import threading
import time
import gc
import os
import tempfile
import shutil
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.db import transaction
from django.utils import timezone

logger = logging.getLogger(__name__)

@dataclass
class CleanupTask:
    """Represents a cleanup task configuration"""
    task_id: str
    name: str
    function: Callable
    interval_seconds: int
    enabled: bool = True
    last_run: Optional[datetime] = None
    run_count: int = 0
    error_count: int = 0
    max_errors: int = 5

@dataclass
class CleanupResult:
    """Result of a cleanup operation"""
    task_id: str
    success: bool
    items_cleaned: int
    bytes_freed: int
    duration_seconds: float
    error_message: Optional[str] = None

class CleanupService:
    """
    Automated cleanup and maintenance service for the queue system
    """
    
    def __init__(self):
        self.cleanup_tasks = {}
        self.is_running = False
        self.cleanup_thread = None
        self._lock = threading.Lock()
        
        # Configuration
        self.check_interval = 60  # Check every minute
        self.temp_file_age_hours = 24
        self.log_retention_days = 30
        self.cache_cleanup_interval = 3600  # 1 hour
        
        # Initialize cleanup tasks
        self._initialize_cleanup_tasks()
    
    def _initialize_cleanup_tasks(self):
        """Initialize all cleanup tasks"""
        try:
            # Temporary files cleanup
            self.register_task(
                'temp_files',
                'Temporary Files Cleanup',
                self._cleanup_temp_files,
                interval_seconds=3600  # Every hour
            )
            
            # Database cleanup
            self.register_task(
                'old_jobs',
                'Old Jobs Cleanup',
                self._cleanup_old_jobs,
                interval_seconds=86400  # Daily
            )
            
            # Cache cleanup
            self.register_task(
                'cache_cleanup',
                'Cache Cleanup',
                self._cleanup_cache,
                interval_seconds=3600  # Every hour
            )
            
            # Memory cleanup
            self.register_task(
                'memory_cleanup',
                'Memory Cleanup',
                self._cleanup_memory,
                interval_seconds=1800  # Every 30 minutes
            )
            
            # Log files cleanup
            self.register_task(
                'log_cleanup',
                'Log Files Cleanup',
                self._cleanup_log_files,
                interval_seconds=86400  # Daily
            )
            
            # Error logs cleanup
            self.register_task(
                'error_logs',
                'Error Logs Cleanup',
                self._cleanup_error_logs,
                interval_seconds=86400  # Daily
            )
            
            # Metrics cleanup
            self.register_task(
                'old_metrics',
                'Old Metrics Cleanup',
                self._cleanup_old_metrics,
                interval_seconds=86400  # Daily
            )
            
            logger.info(f"Initialized {len(self.cleanup_tasks)} cleanup tasks")
            
        except Exception as e:
            logger.error(f"Error initializing cleanup tasks: {str(e)}")
    
    def start(self) -> bool:
        """
        Start the cleanup service
        
        Returns:
            Success status
        """
        try:
            if self.is_running:
                logger.warning("Cleanup service is already running")
                return True
            
            self.is_running = True
            self.cleanup_thread = threading.Thread(
                target=self._cleanup_loop,
                daemon=True,
                name="CleanupService"
            )
            self.cleanup_thread.start()
            
            logger.info("Cleanup service started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting cleanup service: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the cleanup service
        
        Returns:
            Success status
        """
        try:
            self.is_running = False
            
            if self.cleanup_thread and self.cleanup_thread.is_alive():
                self.cleanup_thread.join(timeout=10)
            
            logger.info("Cleanup service stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping cleanup service: {str(e)}")
            return False
    
    def register_task(self, task_id: str, name: str, function: Callable, 
                     interval_seconds: int, enabled: bool = True) -> bool:
        """
        Register a new cleanup task
        
        Args:
            task_id: Unique task identifier
            name: Human-readable task name
            function: Function to execute for cleanup
            interval_seconds: How often to run the task
            enabled: Whether the task is enabled
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                task = CleanupTask(
                    task_id=task_id,
                    name=name,
                    function=function,
                    interval_seconds=interval_seconds,
                    enabled=enabled
                )
                
                self.cleanup_tasks[task_id] = task
                logger.info(f"Registered cleanup task: {name}")
                return True
                
        except Exception as e:
            logger.error(f"Error registering cleanup task {task_id}: {str(e)}")
            return False
    
    def run_task(self, task_id: str) -> Optional[CleanupResult]:
        """
        Run a specific cleanup task immediately
        
        Args:
            task_id: Task to run
            
        Returns:
            Cleanup result or None if failed
        """
        try:
            with self._lock:
                task = self.cleanup_tasks.get(task_id)
                if not task:
                    logger.error(f"Cleanup task not found: {task_id}")
                    return None
                
                if not task.enabled:
                    logger.info(f"Cleanup task disabled: {task_id}")
                    return None
                
                return self._execute_task(task)
                
        except Exception as e:
            logger.error(f"Error running cleanup task {task_id}: {str(e)}")
            return None
    
    def get_task_status(self) -> Dict[str, Dict]:
        """
        Get status of all cleanup tasks
        
        Returns:
            Task status information
        """
        try:
            status = {}
            
            with self._lock:
                for task_id, task in self.cleanup_tasks.items():
                    status[task_id] = {
                        'name': task.name,
                        'enabled': task.enabled,
                        'interval_seconds': task.interval_seconds,
                        'last_run': task.last_run.isoformat() if task.last_run else None,
                        'run_count': task.run_count,
                        'error_count': task.error_count,
                        'max_errors': task.max_errors,
                        'next_run': self._calculate_next_run(task)
                    }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}")
            return {}
    
    def cleanup_temp_files(self) -> CleanupResult:
        """
        Public method to cleanup temporary files
        
        Returns:
            Cleanup result
        """
        return self._cleanup_temp_files()
    
    def cleanup_old_data(self, days: int = 30) -> Dict[str, CleanupResult]:
        """
        Cleanup old data across all systems
        
        Args:
            days: Number of days to keep data
            
        Returns:
            Results for each cleanup operation
        """
        results = {}
        
        try:
            # Update retention period temporarily
            old_retention = self.log_retention_days
            self.log_retention_days = days
            
            # Run all data cleanup tasks
            data_tasks = ['old_jobs', 'error_logs', 'old_metrics', 'log_cleanup']
            
            for task_id in data_tasks:
                result = self.run_task(task_id)
                if result:
                    results[task_id] = result
            
            # Restore original retention
            self.log_retention_days = old_retention
            
            return results
            
        except Exception as e:
            logger.error(f"Error in bulk cleanup: {str(e)}")
            return results
    
    def _cleanup_loop(self):
        """Main cleanup loop"""
        logger.info("Cleanup service loop started")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Check each task
                for task_id, task in list(self.cleanup_tasks.items()):
                    if not task.enabled:
                        continue
                    
                    # Check if task should run
                    if self._should_run_task(task, current_time):
                        logger.debug(f"Running cleanup task: {task.name}")
                        result = self._execute_task(task)
                        
                        if result and result.success:
                            logger.info(f"Cleanup task '{task.name}' completed: "
                                      f"{result.items_cleaned} items, {result.bytes_freed} bytes freed")
                        elif result:
                            logger.warning(f"Cleanup task '{task.name}' failed: {result.error_message}")
                
                # Sleep until next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {str(e)}")
                time.sleep(60)  # Wait before retrying
    
    def _should_run_task(self, task: CleanupTask, current_time: datetime) -> bool:
        """Determine if a task should run"""
        if not task.last_run:
            return True
        
        time_since_last_run = (current_time - task.last_run).total_seconds()
        return time_since_last_run >= task.interval_seconds
    
    def _execute_task(self, task: CleanupTask) -> CleanupResult:
        """Execute a cleanup task"""
        start_time = time.time()
        
        try:
            # Run the task function
            result = task.function()
            
            # Update task statistics
            task.last_run = datetime.now()
            task.run_count += 1
            
            # Ensure result is CleanupResult type
            if not isinstance(result, CleanupResult):
                result = CleanupResult(
                    task_id=task.task_id,
                    success=True,
                    items_cleaned=0,
                    bytes_freed=0,
                    duration_seconds=time.time() - start_time
                )
            
            result.duration_seconds = time.time() - start_time
            return result
            
        except Exception as e:
            # Update error statistics
            task.error_count += 1
            
            # Disable task if too many errors
            if task.error_count >= task.max_errors:
                task.enabled = False
                logger.error(f"Disabled cleanup task '{task.name}' due to excessive errors")
            
            return CleanupResult(
                task_id=task.task_id,
                success=False,
                items_cleaned=0,
                bytes_freed=0,
                duration_seconds=time.time() - start_time,
                error_message=str(e)
            )
    
    def _calculate_next_run(self, task: CleanupTask) -> Optional[str]:
        """Calculate when a task will run next"""
        if not task.enabled or not task.last_run:
            return None
        
        next_run = task.last_run + timedelta(seconds=task.interval_seconds)
        return next_run.isoformat()
    
    # ==================== CLEANUP TASK IMPLEMENTATIONS ====================
    
    def _cleanup_temp_files(self) -> CleanupResult:
        """Cleanup temporary files"""
        items_cleaned = 0
        bytes_freed = 0
        
        try:
            # Get temporary directory
            temp_dir = tempfile.gettempdir()
            cutoff_time = datetime.now() - timedelta(hours=self.temp_file_age_hours)
            
            # Clean up old temporary files
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        
                        # Skip if file is too new
                        if os.path.getmtime(file_path) > cutoff_time.timestamp():
                            continue
                        
                        # Only clean up files with specific patterns (be safe)
                        if any(pattern in file for pattern in ['photofish', 'queue', 'tmp', 'temp']):
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            items_cleaned += 1
                            bytes_freed += file_size
                            
                    except (OSError, IOError):
                        # Skip files we can't access
                        continue
            
            return CleanupResult(
                task_id='temp_files',
                success=True,
                items_cleaned=items_cleaned,
                bytes_freed=bytes_freed,
                duration_seconds=0  # Will be set by caller
            )
            
        except Exception as e:
            logger.error(f"Error cleaning temp files: {str(e)}")
            raise
    
    def _cleanup_old_jobs(self) -> CleanupResult:
        """Cleanup old completed jobs"""
        try:
            from ..models import QueueJob
            
            cutoff_date = timezone.now() - timedelta(days=self.log_retention_days)
            
            # Delete old completed and failed jobs
            old_jobs = QueueJob.objects.filter(
                status__in=['completed', 'failed', 'cancelled'],
                completed_at__lt=cutoff_date
            )
            
            count = old_jobs.count()
            old_jobs.delete()
            
            return CleanupResult(
                task_id='old_jobs',
                success=True,
                items_cleaned=count,
                bytes_freed=count * 1024,  # Estimate
                duration_seconds=0
            )
            
        except Exception as e:
            logger.error(f"Error cleaning old jobs: {str(e)}")
            raise
    
    def _cleanup_cache(self) -> CleanupResult:
        """Cleanup expired cache entries"""
        try:
            # This is cache-backend specific
            # For now, just trigger garbage collection
            cache.clear()
            
            return CleanupResult(
                task_id='cache_cleanup',
                success=True,
                items_cleaned=1,
                bytes_freed=0,
                duration_seconds=0
            )
            
        except Exception as e:
            logger.error(f"Error cleaning cache: {str(e)}")
            raise
    
    def _cleanup_memory(self) -> CleanupResult:
        """Force garbage collection"""
        try:
            # Force garbage collection
            collected = gc.collect()
            
            return CleanupResult(
                task_id='memory_cleanup',
                success=True,
                items_cleaned=collected,
                bytes_freed=0,  # Can't easily measure
                duration_seconds=0
            )
            
        except Exception as e:
            logger.error(f"Error cleaning memory: {str(e)}")
            raise
    
    def _cleanup_log_files(self) -> CleanupResult:
        """Cleanup old log files"""
        items_cleaned = 0
        bytes_freed = 0
        
        try:
            # Look for log files in common locations
            log_dirs = ['/var/log', '/tmp', './logs']
            cutoff_time = datetime.now() - timedelta(days=self.log_retention_days)
            
            for log_dir in log_dirs:
                if not os.path.exists(log_dir):
                    continue
                
                for file in os.listdir(log_dir):
                    if file.endswith('.log') or file.endswith('.log.old'):
                        try:
                            file_path = os.path.join(log_dir, file)
                            
                            if os.path.getmtime(file_path) < cutoff_time.timestamp():
                                file_size = os.path.getsize(file_path)
                                os.remove(file_path)
                                items_cleaned += 1
                                bytes_freed += file_size
                                
                        except (OSError, IOError):
                            continue
            
            return CleanupResult(
                task_id='log_cleanup',
                success=True,
                items_cleaned=items_cleaned,
                bytes_freed=bytes_freed,
                duration_seconds=0
            )
            
        except Exception as e:
            logger.error(f"Error cleaning log files: {str(e)}")
            raise
    
    def _cleanup_error_logs(self) -> CleanupResult:
        """Cleanup old error logs"""
        try:
            from ..models import ErrorLog
            
            cutoff_date = timezone.now() - timedelta(days=self.log_retention_days)
            
            # Delete old resolved errors
            old_errors = ErrorLog.objects.filter(
                resolved=True,
                resolved_at__lt=cutoff_date
            )
            
            count = old_errors.count()
            old_errors.delete()
            
            return CleanupResult(
                task_id='error_logs',
                success=True,
                items_cleaned=count,
                bytes_freed=count * 512,  # Estimate
                duration_seconds=0
            )
            
        except Exception as e:
            logger.error(f"Error cleaning error logs: {str(e)}")
            raise
    
    def _cleanup_old_metrics(self) -> CleanupResult:
        """Cleanup old metrics data"""
        try:
            from ..models import QueueMetrics
            
            cutoff_date = timezone.now() - timedelta(days=self.log_retention_days)
            
            # Delete old metrics
            old_metrics = QueueMetrics.objects.filter(
                timestamp__lt=cutoff_date
            )
            
            count = old_metrics.count()
            old_metrics.delete()
            
            return CleanupResult(
                task_id='old_metrics',
                success=True,
                items_cleaned=count,
                bytes_freed=count * 256,  # Estimate
                duration_seconds=0
            )
            
        except Exception as e:
            logger.error(f"Error cleaning old metrics: {str(e)}")
            raise