# queue_system/resource_manager/connection_pool.py
"""
Connection pooling for PhotoFish Enhanced Queue System
Manages efficient connection pools for external services
"""

import logging
import threading
import time
import boto3
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
from queue import Queue, Empty
from django.conf import settings
from django.db import connections
from django.core.cache import cache

logger = logging.getLogger(__name__)

@dataclass
class ConnectionInfo:
    """Information about a pooled connection"""
    connection_id: str
    service_type: str
    created_at: datetime
    last_used: datetime
    use_count: int
    is_healthy: bool
    connection_object: Any

@dataclass
class PoolStats:
    """Statistics for a connection pool"""
    service_type: str
    total_connections: int
    active_connections: int
    idle_connections: int
    failed_connections: int
    pool_hits: int
    pool_misses: int
    average_wait_time: float

class ConnectionPool:
    """
    Generic connection pool for managing service connections
    """
    
    def __init__(self, service_type: str, min_connections: int = 2, 
                 max_connections: int = 10, connection_timeout: int = 30):
        self.service_type = service_type
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        
        # Connection storage
        self._pool = Queue(maxsize=max_connections)
        self._active_connections = {}
        self._connection_factory = None
        self._health_checker = None
        
        # Threading
        self._lock = threading.Lock()
        self._condition = threading.Condition(self._lock)
        
        # Statistics
        self.stats = PoolStats(
            service_type=service_type,
            total_connections=0,
            active_connections=0,
            idle_connections=0,
            failed_connections=0,
            pool_hits=0,
            pool_misses=0,
            average_wait_time=0.0
        )
        
        # Health monitoring
        self._last_health_check = datetime.now()
        self._health_check_interval = 300  # 5 minutes
    
    def set_connection_factory(self, factory_func):
        """Set the function used to create new connections"""
        self._connection_factory = factory_func
    
    def set_health_checker(self, checker_func):
        """Set the function used to check connection health"""
        self._health_checker = checker_func
    
    def initialize_pool(self) -> bool:
        """
        Initialize the connection pool with minimum connections
        
        Returns:
            Success status
        """
        try:
            if not self._connection_factory:
                logger.error(f"No connection factory set for {self.service_type} pool")
                return False
            
            # Create minimum connections
            for i in range(self.min_connections):
                connection = self._create_connection()
                if connection:
                    self._pool.put(connection)
                    self.stats.total_connections += 1
                    self.stats.idle_connections += 1
            
            logger.info(f"Initialized {self.service_type} connection pool with {self.stats.total_connections} connections")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing {self.service_type} connection pool: {str(e)}")
            return False
    
    def get_connection(self, timeout: Optional[int] = None) -> Optional[ConnectionInfo]:
        """
        Get a connection from the pool
        
        Args:
            timeout: Maximum time to wait for a connection
            
        Returns:
            Connection info or None if unavailable
        """
        start_time = time.time()
        timeout = timeout or self.connection_timeout
        
        try:
            with self._condition:
                # Try to get an existing connection
                try:
                    connection = self._pool.get_nowait()
                    self.stats.pool_hits += 1
                    self.stats.idle_connections -= 1
                    self.stats.active_connections += 1
                    
                    # Check if connection is still healthy
                    if self._is_connection_healthy(connection):
                        connection.last_used = datetime.now()
                        connection.use_count += 1
                        self._active_connections[connection.connection_id] = connection
                        return connection
                    else:
                        # Connection is unhealthy, create a new one
                        self._close_connection(connection)
                        self.stats.failed_connections += 1
                
                except Empty:
                    pass
                
                # No idle connections available, try to create a new one
                if self.stats.total_connections < self.max_connections:
                    connection = self._create_connection()
                    if connection:
                        self.stats.total_connections += 1
                        self.stats.active_connections += 1
                        self.stats.pool_misses += 1
                        self._active_connections[connection.connection_id] = connection
                        return connection
                
                # Pool is full, wait for a connection to be returned
                end_time = start_time + timeout
                while time.time() < end_time:
                    wait_time = end_time - time.time()
                    if self._condition.wait(timeout=wait_time):
                        # A connection was returned, try to get it
                        try:
                            connection = self._pool.get_nowait()
                            self.stats.pool_hits += 1
                            self.stats.idle_connections -= 1
                            self.stats.active_connections += 1
                            
                            if self._is_connection_healthy(connection):
                                connection.last_used = datetime.now()
                                connection.use_count += 1
                                self._active_connections[connection.connection_id] = connection
                                return connection
                            else:
                                self._close_connection(connection)
                                self.stats.failed_connections += 1
                                
                        except Empty:
                            continue
                    else:
                        break
                
                # Update average wait time
                wait_time = time.time() - start_time
                self.stats.average_wait_time = (
                    (self.stats.average_wait_time * (self.stats.pool_hits + self.stats.pool_misses - 1) + wait_time) /
                    (self.stats.pool_hits + self.stats.pool_misses)
                ) if (self.stats.pool_hits + self.stats.pool_misses) > 0 else wait_time
                
                logger.warning(f"Failed to get connection from {self.service_type} pool within timeout")
                return None
                
        except Exception as e:
            logger.error(f"Error getting connection from {self.service_type} pool: {str(e)}")
            return None
    
    def return_connection(self, connection: ConnectionInfo) -> bool:
        """
        Return a connection to the pool
        
        Args:
            connection: Connection to return
            
        Returns:
            Success status
        """
        try:
            with self._condition:
                # Remove from active connections
                if connection.connection_id in self._active_connections:
                    del self._active_connections[connection.connection_id]
                    self.stats.active_connections -= 1
                
                # Check if connection is still healthy
                if self._is_connection_healthy(connection):
                    # Return to pool if there's space
                    if self.stats.idle_connections < self.max_connections:
                        self._pool.put(connection)
                        self.stats.idle_connections += 1
                        self._condition.notify_all()
                        return True
                    else:
                        # Pool is full, close the connection
                        self._close_connection(connection)
                        self.stats.total_connections -= 1
                else:
                    # Connection is unhealthy, close it
                    self._close_connection(connection)
                    self.stats.total_connections -= 1
                    self.stats.failed_connections += 1
                
                return True
                
        except Exception as e:
            logger.error(f"Error returning connection to {self.service_type} pool: {str(e)}")
            return False
    
    def close_pool(self) -> bool:
        """
        Close all connections in the pool
        
        Returns:
            Success status
        """
        try:
            with self._lock:
                # Close all idle connections
                while not self._pool.empty():
                    try:
                        connection = self._pool.get_nowait()
                        self._close_connection(connection)
                    except Empty:
                        break
                
                # Close all active connections
                for connection in list(self._active_connections.values()):
                    self._close_connection(connection)
                
                self._active_connections.clear()
                
                # Reset statistics
                self.stats.total_connections = 0
                self.stats.active_connections = 0
                self.stats.idle_connections = 0
                
                logger.info(f"Closed {self.service_type} connection pool")
                return True
                
        except Exception as e:
            logger.error(f"Error closing {self.service_type} connection pool: {str(e)}")
            return False
    
    def get_pool_stats(self) -> PoolStats:
        """Get current pool statistics"""
        return self.stats
    
    def health_check(self) -> bool:
        """
        Perform health check on the pool
        
        Returns:
            True if pool is healthy
        """
        try:
            current_time = datetime.now()
            
            # Only check if enough time has passed
            if (current_time - self._last_health_check).seconds < self._health_check_interval:
                return True
            
            self._last_health_check = current_time
            
            # Check if we have minimum connections
            if self.stats.total_connections < self.min_connections:
                self._ensure_minimum_connections()
            
            # Remove unhealthy idle connections
            self._cleanup_unhealthy_connections()
            
            return self.stats.total_connections >= self.min_connections
            
        except Exception as e:
            logger.error(f"Error during {self.service_type} pool health check: {str(e)}")
            return False
    
    def _create_connection(self) -> Optional[ConnectionInfo]:
        """Create a new connection"""
        try:
            if not self._connection_factory:
                return None
            
            connection_obj = self._connection_factory()
            if not connection_obj:
                return None
            
            connection_info = ConnectionInfo(
                connection_id=f"{self.service_type}_{int(time.time() * 1000)}_{id(connection_obj)}",
                service_type=self.service_type,
                created_at=datetime.now(),
                last_used=datetime.now(),
                use_count=0,
                is_healthy=True,
                connection_object=connection_obj
            )
            
            return connection_info
            
        except Exception as e:
            logger.error(f"Error creating {self.service_type} connection: {str(e)}")
            return None
    
    def _is_connection_healthy(self, connection: ConnectionInfo) -> bool:
        """Check if a connection is healthy"""
        try:
            # Basic age check (connections shouldn't be too old)
            max_age = timedelta(hours=24)
            if datetime.now() - connection.created_at > max_age:
                return False
            
            # Use custom health checker if available
            if self._health_checker:
                return self._health_checker(connection.connection_object)
            
            # Default: assume healthy if not too old
            return True
            
        except Exception as e:
            logger.error(f"Error checking connection health: {str(e)}")
            return False
    
    def _close_connection(self, connection: ConnectionInfo):
        """Close a connection"""
        try:
            connection.is_healthy = False
            
            # If connection object has a close method, call it
            if hasattr(connection.connection_object, 'close'):
                connection.connection_object.close()
            
        except Exception as e:
            logger.error(f"Error closing connection: {str(e)}")
    
    def _ensure_minimum_connections(self):
        """Ensure pool has minimum number of connections"""
        try:
            needed = self.min_connections - self.stats.total_connections
            
            for _ in range(needed):
                connection = self._create_connection()
                if connection:
                    self._pool.put(connection)
                    self.stats.total_connections += 1
                    self.stats.idle_connections += 1
                
        except Exception as e:
            logger.error(f"Error ensuring minimum connections: {str(e)}")
    
    def _cleanup_unhealthy_connections(self):
        """Remove unhealthy connections from idle pool"""
        try:
            healthy_connections = []
            
            # Check all idle connections
            while not self._pool.empty():
                try:
                    connection = self._pool.get_nowait()
                    if self._is_connection_healthy(connection):
                        healthy_connections.append(connection)
                    else:
                        self._close_connection(connection)
                        self.stats.total_connections -= 1
                        self.stats.idle_connections -= 1
                        self.stats.failed_connections += 1
                        
                except Empty:
                    break
            
            # Put healthy connections back
            for connection in healthy_connections:
                self._pool.put(connection)
                
        except Exception as e:
            logger.error(f"Error cleaning up unhealthy connections: {str(e)}")


class ConnectionManager:
    """
    Manages multiple connection pools for different services
    """
    
    def __init__(self):
        self.pools = {}
        self.pool_configs = self._get_pool_configurations()
        self._initialize_pools()
    
    def _get_pool_configurations(self) -> Dict[str, Dict]:
        """Get connection pool configurations from settings"""
        try:
            enhanced_settings = getattr(settings, 'ENHANCED_QUEUE_SETTINGS', {})
            return enhanced_settings.get('CONNECTION_POOLS', {
                'AWS_REKOGNITION': {'min': 5, 'max': 15, 'timeout': 30},
                'DATABASE': {'min': 10, 'max': 25, 'timeout': 20},
                'REDIS': {'min': 5, 'max': 10, 'timeout': 15}
            })
        except Exception as e:
            logger.error(f"Error getting pool configurations: {str(e)}")
            return {}
    
    def _initialize_pools(self):
        """Initialize all connection pools"""
        try:
            for service_type, config in self.pool_configs.items():
                pool = ConnectionPool(
                    service_type=service_type,
                    min_connections=config.get('min', 2),
                    max_connections=config.get('max', 10),
                    connection_timeout=config.get('timeout', 30)
                )
                
                # Set up service-specific factories and health checkers
                self._setup_pool_handlers(pool, service_type)
                
                # Initialize the pool
                if pool.initialize_pool():
                    self.pools[service_type] = pool
                    logger.info(f"Initialized {service_type} connection pool")
                else:
                    logger.error(f"Failed to initialize {service_type} connection pool")
                    
        except Exception as e:
            logger.error(f"Error initializing connection pools: {str(e)}")
    
    def _setup_pool_handlers(self, pool: ConnectionPool, service_type: str):
        """Set up connection factory and health checker for a pool"""
        try:
            if service_type == 'AWS_REKOGNITION':
                pool.set_connection_factory(self._create_aws_rekognition_connection)
                pool.set_health_checker(self._check_aws_connection_health)
                
            elif service_type == 'DATABASE':
                pool.set_connection_factory(self._create_database_connection)
                pool.set_health_checker(self._check_database_connection_health)
                
            elif service_type == 'REDIS':
                pool.set_connection_factory(self._create_redis_connection)
                pool.set_health_checker(self._check_redis_connection_health)
                
        except Exception as e:
            logger.error(f"Error setting up handlers for {service_type}: {str(e)}")
    
    def get_connection(self, service_type: str, timeout: Optional[int] = None) -> Optional[ConnectionInfo]:
        """
        Get a connection for a specific service
        
        Args:
            service_type: Type of service connection needed
            timeout: Maximum wait time for connection
            
        Returns:
            Connection info or None
        """
        try:
            pool = self.pools.get(service_type)
            if not pool:
                logger.error(f"No connection pool found for service: {service_type}")
                return None
            
            return pool.get_connection(timeout)
            
        except Exception as e:
            logger.error(f"Error getting {service_type} connection: {str(e)}")
            return None
    
    def return_connection(self, service_type: str, connection: ConnectionInfo) -> bool:
        """
        Return a connection to its pool
        
        Args:
            service_type: Type of service
            connection: Connection to return
            
        Returns:
            Success status
        """
        try:
            pool = self.pools.get(service_type)
            if not pool:
                logger.error(f"No connection pool found for service: {service_type}")
                return False
            
            return pool.return_connection(connection)
            
        except Exception as e:
            logger.error(f"Error returning {service_type} connection: {str(e)}")
            return False
    
    def get_all_pool_stats(self) -> Dict[str, PoolStats]:
        """Get statistics for all pools"""
        try:
            stats = {}
            for service_type, pool in self.pools.items():
                stats[service_type] = pool.get_pool_stats()
            return stats
            
        except Exception as e:
            logger.error(f"Error getting pool stats: {str(e)}")
            return {}
    
    def health_check_all_pools(self) -> Dict[str, bool]:
        """Perform health check on all pools"""
        try:
            results = {}
            for service_type, pool in self.pools.items():
                results[service_type] = pool.health_check()
            return results
            
        except Exception as e:
            logger.error(f"Error during pool health checks: {str(e)}")
            return {}
    
    def shutdown_all_pools(self) -> bool:
        """Shutdown all connection pools"""
        try:
            success = True
            for service_type, pool in self.pools.items():
                if not pool.close_pool():
                    success = False
                    logger.error(f"Failed to close {service_type} pool")
            
            self.pools.clear()
            logger.info("All connection pools shutdown")
            return success
            
        except Exception as e:
            logger.error(f"Error shutting down pools: {str(e)}")
            return False
    
    # ==================== CONNECTION FACTORY METHODS ====================
    
    def _create_aws_rekognition_connection(self):
        """Create AWS Rekognition client connection"""
        try:
            return boto3.client(
                'rekognition',
                aws_access_key_id=getattr(settings, 'AWS_ACCESS_KEY_ID', None),
                aws_secret_access_key=getattr(settings, 'AWS_SECRET_ACCESS_KEY', None),
                region_name=getattr(settings, 'AWS_REGION_NAME', 'us-east-1')
            )
        except Exception as e:
            logger.error(f"Error creating AWS Rekognition connection: {str(e)}")
            return None
    
    def _create_database_connection(self):
        """Create database connection"""
        try:
            # Use Django's connection handling
            from django.db import connection
            return connection
        except Exception as e:
            logger.error(f"Error creating database connection: {str(e)}")
            return None
    
    def _create_redis_connection(self):
        """Create Redis connection"""
        try:
            import redis
            return redis.Redis(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_DB', 0),
                decode_responses=True
            )
        except ImportError:
            logger.warning("Redis not available - install redis-py package")
            return None
        except Exception as e:
            logger.error(f"Error creating Redis connection: {str(e)}")
            return None
    
    # ==================== HEALTH CHECK METHODS ====================
    
    def _check_aws_connection_health(self, connection) -> bool:
        """Check AWS Rekognition connection health"""
        try:
            # Simple operation to verify connection
            connection.describe_collection(CollectionId='health-check-collection')
            return True
        except connection.exceptions.ResourceNotFoundException:
            # Collection not found is expected - connection is healthy
            return True
        except Exception as e:
            logger.warning(f"AWS connection health check failed: {str(e)}")
            return False
    
    def _check_database_connection_health(self, connection) -> bool:
        """Check database connection health"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
        except Exception as e:
            logger.warning(f"Database connection health check failed: {str(e)}")
            return False
    
    def _check_redis_connection_health(self, connection) -> bool:
        """Check Redis connection health"""
        try:
            return connection.ping()
        except Exception as e:
            logger.warning(f"Redis connection health check failed: {str(e)}")
            return False


# Global connection manager instance
connection_manager = ConnectionManager()


# ==================== CONVENIENCE FUNCTIONS ====================

def get_aws_rekognition_connection(timeout: Optional[int] = None) -> Optional[ConnectionInfo]:
    """Get an AWS Rekognition connection from the pool"""
    return connection_manager.get_connection('AWS_REKOGNITION', timeout)

def return_aws_rekognition_connection(connection: ConnectionInfo) -> bool:
    """Return an AWS Rekognition connection to the pool"""
    return connection_manager.return_connection('AWS_REKOGNITION', connection)

def get_database_connection(timeout: Optional[int] = None) -> Optional[ConnectionInfo]:
    """Get a database connection from the pool"""
    return connection_manager.get_connection('DATABASE', timeout)

def return_database_connection(connection: ConnectionInfo) -> bool:
    """Return a database connection to the pool"""
    return connection_manager.return_connection('DATABASE', connection)

def get_redis_connection(timeout: Optional[int] = None) -> Optional[ConnectionInfo]:
    """Get a Redis connection from the pool"""
    return connection_manager.get_connection('REDIS', timeout)

def return_redis_connection(connection: ConnectionInfo) -> bool:
    """Return a Redis connection to the pool"""
    return connection_manager.return_connection('REDIS', connection)

def get_all_connection_stats() -> Dict[str, PoolStats]:
    """Get statistics for all connection pools"""
    return connection_manager.get_all_pool_stats()

def health_check_all_connections() -> Dict[str, bool]:
    """Perform health check on all connection pools"""
    return connection_manager.health_check_all_pools()