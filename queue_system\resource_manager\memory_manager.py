# queue_system/resource_manager/memory_manager.py
"""
Memory management for PhotoFish Enhanced Queue System
Optimizes memory usage and prevents memory leaks
"""

import logging
import threading
import time
import gc
import psutil
import os
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

@dataclass
class MemoryStats:
    """Memory usage statistics"""
    total_memory_mb: float
    available_memory_mb: float
    used_memory_mb: float
    used_percentage: float
    process_memory_mb: float
    process_memory_percentage: float
    gc_collections: Dict[int, int]
    timestamp: datetime

@dataclass
class MemoryAlert:
    """Memory usage alert"""
    alert_type: str  # 'high_usage', 'leak_detected', 'gc_issues'
    severity: str   # 'warning', 'critical'
    message: str
    current_usage: float
    threshold: float
    timestamp: datetime

class MemoryManager:
    """
    Advanced memory management for queue system optimization
    """
    
    def __init__(self):
        self.is_monitoring = False
        self.monitoring_thread = None
        self._lock = threading.Lock()
        
        # Memory thresholds
        self.warning_threshold = 80.0  # % of system memory
        self.critical_threshold = 95.0  # % of system memory
        self.process_warning_threshold = 512  # MB
        self.process_critical_threshold = 1024  # MB
        
        # Monitoring configuration
        self.monitoring_interval = 30  # seconds
        self.gc_optimization_enabled = True
        self.leak_detection_enabled = True
        
        # Memory tracking
        self.memory_history = []
        self.max_history_size = 1000
        self.leak_detection_window = 300  # 5 minutes
        
        # Alert system
        self.alert_callbacks = []
        self.last_alerts = {}
        self.alert_cooldown = 300  # 5 minutes between same alerts
        
        # Cleanup strategies
        self.cleanup_strategies = self._initialize_cleanup_strategies()
    
    def start_monitoring(self) -> bool:
        """
        Start memory monitoring
        
        Returns:
            Success status
        """
        try:
            if self.is_monitoring:
                logger.warning("Memory monitoring is already running")
                return True
            
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="MemoryMonitor"
            )
            self.monitoring_thread.start()
            
            logger.info("Memory monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting memory monitoring: {str(e)}")
            return False
    
    def stop_monitoring(self) -> bool:
        """
        Stop memory monitoring
        
        Returns:
            Success status
        """
        try:
            self.is_monitoring = False
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=10)
            
            logger.info("Memory monitoring stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping memory monitoring: {str(e)}")
            return False
    
    def get_memory_stats(self) -> MemoryStats:
        """
        Get current memory statistics
        
        Returns:
            Current memory statistics
        """
        try:
            # System memory info
            system_memory = psutil.virtual_memory()
            
            # Process memory info
            process = psutil.Process()
            process_memory = process.memory_info()
            
            # Garbage collection stats
            gc_stats = {}
            for generation in range(3):
                gc_stats[generation] = gc.get_count()[generation]
            
            return MemoryStats(
                total_memory_mb=system_memory.total / (1024 * 1024),
                available_memory_mb=system_memory.available / (1024 * 1024),
                used_memory_mb=system_memory.used / (1024 * 1024),
                used_percentage=system_memory.percent,
                process_memory_mb=process_memory.rss / (1024 * 1024),
                process_memory_percentage=(process_memory.rss / system_memory.total) * 100,
                gc_collections=gc_stats,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {str(e)}")
            return MemoryStats(
                total_memory_mb=0, available_memory_mb=0, used_memory_mb=0,
                used_percentage=0, process_memory_mb=0, process_memory_percentage=0,
                gc_collections={}, timestamp=datetime.now()
            )
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        Perform memory optimization
        
        Returns:
            Optimization results
        """
        try:
            start_stats = self.get_memory_stats()
            results = {
                'start_memory_mb': start_stats.process_memory_mb,
                'optimizations_performed': [],
                'memory_freed_mb': 0,
                'gc_collections': 0
            }
            
            # Force garbage collection
            if self.gc_optimization_enabled:
                gc_before = sum(gc.get_count())
                collected = gc.collect()
                gc_after = sum(gc.get_count())
                
                results['gc_collections'] = collected
                results['optimizations_performed'].append('garbage_collection')
                logger.debug(f"Garbage collection freed {collected} objects")
            
            # Clear caches
            self._clear_internal_caches()
            results['optimizations_performed'].append('cache_cleanup')
            
            # Run cleanup strategies
            for strategy_name, strategy_func in self.cleanup_strategies.items():
                try:
                    strategy_func()
                    results['optimizations_performed'].append(strategy_name)
                except Exception as e:
                    logger.error(f"Error in cleanup strategy {strategy_name}: {str(e)}")
            
            # Calculate memory freed
            end_stats = self.get_memory_stats()
            memory_freed = start_stats.process_memory_mb - end_stats.process_memory_mb
            results['memory_freed_mb'] = max(0, memory_freed)
            results['end_memory_mb'] = end_stats.process_memory_mb
            
            logger.info(f"Memory optimization completed: freed {memory_freed:.1f}MB")
            return results
            
        except Exception as e:
            logger.error(f"Error during memory optimization: {str(e)}")
            return {'error': str(e)}
    
    def detect_memory_leaks(self) -> List[Dict[str, Any]]:
        """
        Detect potential memory leaks
        
        Returns:
            List of detected issues
        """
        try:
            issues = []
            
            if not self.leak_detection_enabled or len(self.memory_history) < 10:
                return issues
            
            # Analyze memory trend over time
            recent_history = self.memory_history[-60:]  # Last 60 measurements
            if len(recent_history) < 10:
                return issues
            
            # Check for consistent memory growth
            memory_values = [stat.process_memory_mb for stat in recent_history]
            
            # Simple trend analysis
            start_memory = sum(memory_values[:5]) / 5
            end_memory = sum(memory_values[-5:]) / 5
            growth_rate = (end_memory - start_memory) / start_memory * 100
            
            if growth_rate > 10:  # More than 10% growth
                issues.append({
                    'type': 'memory_growth',
                    'severity': 'warning' if growth_rate < 25 else 'critical',
                    'description': f'Consistent memory growth detected: {growth_rate:.1f}%',
                    'start_memory_mb': start_memory,
                    'end_memory_mb': end_memory,
                    'growth_rate': growth_rate
                })
            
            # Check for GC issues
            gc_counts = [stat.gc_collections for stat in recent_history if stat.gc_collections]
            if gc_counts:
                # High number of gen-2 collections might indicate issues
                gen2_collections = [counts.get(2, 0) for counts in gc_counts[-10:]]
                if sum(gen2_collections) > 50:  # Many gen-2 collections
                    issues.append({
                        'type': 'gc_pressure',
                        'severity': 'warning',
                        'description': 'High garbage collection pressure detected',
                        'gen2_collections': sum(gen2_collections)
                    })
            
            return issues
            
        except Exception as e:
            logger.error(f"Error detecting memory leaks: {str(e)}")
            return []
    
    def add_alert_callback(self, callback: Callable[[MemoryAlert], None]):
        """Add a callback function for memory alerts"""
        self.alert_callbacks.append(callback)
    
    def get_memory_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive memory report
        
        Returns:
            Memory usage report
        """
        try:
            current_stats = self.get_memory_stats()
            
            report = {
                'current_stats': {
                    'system_memory_gb': current_stats.total_memory_mb / 1024,
                    'system_used_percentage': current_stats.used_percentage,
                    'process_memory_mb': current_stats.process_memory_mb,
                    'process_percentage': current_stats.process_memory_percentage
                },
                'thresholds': {
                    'system_warning': self.warning_threshold,
                    'system_critical': self.critical_threshold,
                    'process_warning_mb': self.process_warning_threshold,
                    'process_critical_mb': self.process_critical_threshold
                },
                'monitoring_status': self.is_monitoring,
                'optimization_enabled': self.gc_optimization_enabled,
                'leak_detection_enabled': self.leak_detection_enabled
            }
            
            # Add trend analysis if history available
            if len(self.memory_history) > 10:
                recent_memory = [s.process_memory_mb for s in self.memory_history[-30:]]
                report['trends'] = {
                    'average_memory_mb': sum(recent_memory) / len(recent_memory),
                    'peak_memory_mb': max(recent_memory),
                    'min_memory_mb': min(recent_memory),
                    'data_points': len(self.memory_history)
                }
            
            # Add detected issues
            issues = self.detect_memory_leaks()
            if issues:
                report['detected_issues'] = issues
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating memory report: {str(e)}")
            return {'error': str(e)}
    
    def force_cleanup(self) -> Dict[str, Any]:
        """
        Force aggressive memory cleanup
        
        Returns:
            Cleanup results
        """
        try:
            logger.info("Performing aggressive memory cleanup")
            
            # Multiple garbage collection passes
            total_collected = 0
            for _ in range(3):
                collected = gc.collect()
                total_collected += collected
                time.sleep(0.1)
            
            # Clear all possible caches
            self._aggressive_cache_cleanup()
            
            # Run optimization
            optimization_result = self.optimize_memory()
            optimization_result['aggressive_gc_collections'] = total_collected
            
            logger.info(f"Aggressive cleanup completed: {total_collected} objects collected")
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error during aggressive cleanup: {str(e)}")
            return {'error': str(e)}
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("Memory monitoring loop started")
        
        while self.is_monitoring:
            try:
                # Collect memory stats
                stats = self.get_memory_stats()
                
                # Add to history
                with self._lock:
                    self.memory_history.append(stats)
                    
                    # Limit history size
                    if len(self.memory_history) > self.max_history_size:
                        self.memory_history = self.memory_history[-self.max_history_size:]
                
                # Check for alerts
                self._check_memory_alerts(stats)
                
                # Detect memory issues
                if self.leak_detection_enabled:
                    issues = self.detect_memory_leaks()
                    for issue in issues:
                        self._send_memory_alert(MemoryAlert(
                            alert_type=issue['type'],
                            severity=issue['severity'],
                            message=issue['description'],
                            current_usage=stats.process_memory_mb,
                            threshold=0,
                            timestamp=datetime.now()
                        ))
                
                # Automatic optimization if needed
                if stats.used_percentage > self.critical_threshold:
                    logger.warning("Critical memory usage detected, performing optimization")
                    self.optimize_memory()
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in memory monitoring loop: {str(e)}")
                time.sleep(60)  # Wait before retrying
    
    def _check_memory_alerts(self, stats: MemoryStats):
        """Check if memory usage requires alerts"""
        try:
            current_time = datetime.now()
            
            # System memory alerts
            if stats.used_percentage > self.critical_threshold:
                self._send_memory_alert(MemoryAlert(
                    alert_type='high_usage',
                    severity='critical',
                    message=f'Critical system memory usage: {stats.used_percentage:.1f}%',
                    current_usage=stats.used_percentage,
                    threshold=self.critical_threshold,
                    timestamp=current_time
                ))
            elif stats.used_percentage > self.warning_threshold:
                self._send_memory_alert(MemoryAlert(
                    alert_type='high_usage',
                    severity='warning',
                    message=f'High system memory usage: {stats.used_percentage:.1f}%',
                    current_usage=stats.used_percentage,
                    threshold=self.warning_threshold,
                    timestamp=current_time
                ))
            
            # Process memory alerts
            if stats.process_memory_mb > self.process_critical_threshold:
                self._send_memory_alert(MemoryAlert(
                    alert_type='process_memory',
                    severity='critical',
                    message=f'Critical process memory usage: {stats.process_memory_mb:.1f}MB',
                    current_usage=stats.process_memory_mb,
                    threshold=self.process_critical_threshold,
                    timestamp=current_time
                ))
            elif stats.process_memory_mb > self.process_warning_threshold:
                self._send_memory_alert(MemoryAlert(
                    alert_type='process_memory',
                    severity='warning',
                    message=f'High process memory usage: {stats.process_memory_mb:.1f}MB',
                    current_usage=stats.process_memory_mb,
                    threshold=self.process_warning_threshold,
                    timestamp=current_time
                ))
                
        except Exception as e:
            logger.error(f"Error checking memory alerts: {str(e)}")
    
    def _send_memory_alert(self, alert: MemoryAlert):
        """Send memory alert to registered callbacks"""
        try:
            # Check cooldown
            alert_key = f"{alert.alert_type}_{alert.severity}"
            last_alert_time = self.last_alerts.get(alert_key)
            
            if last_alert_time:
                time_since_last = (alert.timestamp - last_alert_time).total_seconds()
                if time_since_last < self.alert_cooldown:
                    return  # Skip due to cooldown
            
            # Update last alert time
            self.last_alerts[alert_key] = alert.timestamp
            
            # Send to callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"Error in memory alert callback: {str(e)}")
            
            # Log alert
            log_level = logging.CRITICAL if alert.severity == 'critical' else logging.WARNING
            logger.log(log_level, f"Memory Alert: {alert.message}")
            
        except Exception as e:
            logger.error(f"Error sending memory alert: {str(e)}")
    
    def _initialize_cleanup_strategies(self) -> Dict[str, Callable]:
        """Initialize memory cleanup strategies"""
        return {
            'clear_django_cache': self._clear_django_cache,
            'clear_query_cache': self._clear_query_cache,
            'optimize_imports': self._optimize_imports,
            'clear_temp_objects': self._clear_temp_objects
        }
    
    def _clear_internal_caches(self):
        """Clear internal caches"""
        try:
            # Clear various internal caches
            if hasattr(cache, 'clear'):
                cache.clear()
            
        except Exception as e:
            logger.error(f"Error clearing internal caches: {str(e)}")
    
    def _aggressive_cache_cleanup(self):
        """Aggressive cache cleanup for critical memory situations"""
        try:
            # Clear Django cache
            cache.clear()
            
            # Clear query cache
            from django.db import reset_queries
            reset_queries()
            
            # Clear any application-specific caches
            from django.core.cache import caches
            for cache_name in caches:
                try:
                    caches[cache_name].clear()
                except Exception:
                    pass
            
            logger.info("Aggressive cache cleanup completed")
            
        except Exception as e:
            logger.error(f"Error in aggressive cache cleanup: {str(e)}")
    
    def _clear_django_cache(self):
        """Clear Django cache"""
        try:
            cache.clear()
        except Exception as e:
            logger.error(f"Error clearing Django cache: {str(e)}")
    
    def _clear_query_cache(self):
        """Clear database query cache"""
        try:
            from django.db import reset_queries
            reset_queries()
        except Exception as e:
            logger.error(f"Error clearing query cache: {str(e)}")
    
    def _optimize_imports(self):
        """Optimize import statements and module loading"""
        try:
            # Force garbage collection of unused modules
            import sys
            module_count_before = len(sys.modules)
            
            # Remove any modules that might be safe to reload
            # (Be very careful here - only remove specific temporary modules)
            temp_modules = [name for name in sys.modules.keys() if 'temp_' in name]
            for module_name in temp_modules:
                try:
                    del sys.modules[module_name]
                except KeyError:
                    pass
            
            module_count_after = len(sys.modules)
            logger.debug(f"Module optimization: {module_count_before} -> {module_count_after}")
            
        except Exception as e:
            logger.error(f"Error optimizing imports: {str(e)}")
    
    def _clear_temp_objects(self):
        """Clear temporary objects from memory"""
        try:
            # Force cleanup of temporary objects
            gc.collect()
            
            # Clear any temporary data structures
            # This would be application-specific
            
        except Exception as e:
            logger.error(f"Error clearing temp objects: {str(e)}")


class MemoryOptimizer:
    """
    High-level memory optimization utilities
    """
    
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.optimization_history = []
        self.auto_optimization_enabled = False
        self.optimization_threshold = 85.0  # % memory usage
    
    def start_auto_optimization(self) -> bool:
        """
        Start automatic memory optimization
        
        Returns:
            Success status
        """
        try:
            self.auto_optimization_enabled = True
            
            # Add alert callback for automatic optimization
            self.memory_manager.add_alert_callback(self._auto_optimization_callback)
            
            # Start memory monitoring
            return self.memory_manager.start_monitoring()
            
        except Exception as e:
            logger.error(f"Error starting auto optimization: {str(e)}")
            return False
    
    def stop_auto_optimization(self) -> bool:
        """
        Stop automatic memory optimization
        
        Returns:
            Success status
        """
        try:
            self.auto_optimization_enabled = False
            return self.memory_manager.stop_monitoring()
            
        except Exception as e:
            logger.error(f"Error stopping auto optimization: {str(e)}")
            return False
    
    def optimize_for_job_processing(self) -> Dict[str, Any]:
        """
        Optimize memory specifically for job processing
        
        Returns:
            Optimization results
        """
        try:
            logger.info("Optimizing memory for job processing")
            
            # Pre-job optimization
            results = self.memory_manager.optimize_memory()
            
            # Set stricter GC thresholds for job processing
            old_thresholds = gc.get_threshold()
            gc.set_threshold(700, 10, 10)  # More aggressive GC
            
            results['gc_thresholds_adjusted'] = True
            results['old_gc_thresholds'] = old_thresholds
            
            # Record optimization
            self.optimization_history.append({
                'type': 'job_processing',
                'timestamp': datetime.now(),
                'results': results
            })
            
            return results
            
        except Exception as e:
            logger.error(f"Error optimizing for job processing: {str(e)}")
            return {'error': str(e)}
    
    def restore_normal_operation(self) -> bool:
        """
        Restore normal memory operation after job processing
        
        Returns:
            Success status
        """
        try:
            # Restore default GC thresholds
            gc.set_threshold(700, 10, 10)  # Python defaults
            
            # Final cleanup
            self.memory_manager.optimize_memory()
            
            logger.info("Restored normal memory operation")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring normal operation: {str(e)}")
            return False
    
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """
        Get memory optimization recommendations
        
        Returns:
            List of recommendations
        """
        try:
            recommendations = []
            stats = self.memory_manager.get_memory_stats()
            
            # High memory usage
            if stats.used_percentage > 80:
                recommendations.append({
                    'type': 'high_usage',
                    'priority': 'high',
                    'description': 'System memory usage is high',
                    'action': 'Consider running memory optimization',
                    'current_usage': stats.used_percentage
                })
            
            # High process memory
            if stats.process_memory_mb > 512:
                recommendations.append({
                    'type': 'process_memory',
                    'priority': 'medium',
                    'description': 'Process memory usage is elevated',
                    'action': 'Monitor for memory leaks and optimize regularly',
                    'current_usage': stats.process_memory_mb
                })
            
            # Memory leak detection
            issues = self.memory_manager.detect_memory_leaks()
            for issue in issues:
                recommendations.append({
                    'type': 'memory_leak',
                    'priority': 'high' if issue['severity'] == 'critical' else 'medium',
                    'description': issue['description'],
                    'action': 'Investigate and fix memory leak',
                    'details': issue
                })
            
            # GC optimization
            if stats.gc_collections.get(2, 0) > 50:
                recommendations.append({
                    'type': 'gc_optimization',
                    'priority': 'low',
                    'description': 'High garbage collection activity detected',
                    'action': 'Consider tuning GC thresholds',
                    'gen2_collections': stats.gc_collections.get(2, 0)
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {str(e)}")
            return []
    
    def generate_memory_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive memory optimization report
        
        Returns:
            Detailed memory report
        """
        try:
            report = self.memory_manager.get_memory_report()
            
            # Add optimization history
            report['optimization_history'] = self.optimization_history[-10:]  # Last 10 optimizations
            
            # Add recommendations
            report['recommendations'] = self.get_optimization_recommendations()
            
            # Add auto-optimization status
            report['auto_optimization'] = {
                'enabled': self.auto_optimization_enabled,
                'threshold': self.optimization_threshold
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating memory report: {str(e)}")
            return {'error': str(e)}
    
    def _auto_optimization_callback(self, alert: MemoryAlert):
        """Callback for automatic optimization on memory alerts"""
        try:
            if not self.auto_optimization_enabled:
                return
            
            # Only auto-optimize on critical alerts
            if alert.severity == 'critical':
                logger.warning(f"Auto-optimization triggered by: {alert.message}")
                
                result = self.memory_manager.optimize_memory()
                
                # Record auto-optimization
                self.optimization_history.append({
                    'type': 'auto_optimization',
                    'trigger': alert.alert_type,
                    'timestamp': datetime.now(),
                    'results': result
                })
                
                logger.info(f"Auto-optimization completed: freed {result.get('memory_freed_mb', 0):.1f}MB")
                
        except Exception as e:
            logger.error(f"Error in auto-optimization callback: {str(e)}")


# Global memory manager instances
memory_manager = MemoryManager()
memory_optimizer = MemoryOptimizer()


# ==================== CONVENIENCE FUNCTIONS ====================

def get_current_memory_stats() -> MemoryStats:
    """Get current memory statistics"""
    return memory_manager.get_memory_stats()

def optimize_memory() -> Dict[str, Any]:
    """Perform memory optimization"""
    return memory_manager.optimize_memory()

def start_memory_monitoring() -> bool:
    """Start memory monitoring"""
    return memory_manager.start_monitoring()

def stop_memory_monitoring() -> bool:
    """Stop memory monitoring"""
    return memory_manager.stop_monitoring()

def force_memory_cleanup() -> Dict[str, Any]:
    """Force aggressive memory cleanup"""
    return memory_manager.force_cleanup()

def get_memory_optimization_report() -> Dict[str, Any]:
    """Get comprehensive memory report"""
    return memory_optimizer.generate_memory_report()

def start_auto_memory_optimization() -> bool:
    """Start automatic memory optimization"""
    return memory_optimizer.start_auto_optimization()

def stop_auto_memory_optimization() -> bool:
    """Stop automatic memory optimization"""
    return memory_optimizer.stop_auto_optimization()