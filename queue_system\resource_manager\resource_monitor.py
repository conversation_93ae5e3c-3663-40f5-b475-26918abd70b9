# queue_system/resource_manager/resource_monitor.py
"""
Resource monitoring for PhotoFish Enhanced Queue System
Real-time monitoring of system resources and performance metrics
"""

import logging
import threading
import time
import psutil
import platform
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """Comprehensive system metrics"""
    # CPU metrics
    cpu_percent: float
    cpu_count: int
    cpu_freq_current: float
    cpu_freq_max: float
    load_average: List[float]
    
    # Memory metrics
    memory_total: int
    memory_available: int
    memory_used: int
    memory_percent: float
    
    # Disk metrics
    disk_total: int
    disk_used: int
    disk_free: int
    disk_percent: float
    disk_read_bytes: int
    disk_write_bytes: int
    
    # Network metrics
    network_bytes_sent: int
    network_bytes_recv: int
    network_packets_sent: int
    network_packets_recv: int
    
    # Process metrics
    process_count: int
    thread_count: int
    fd_count: int
    
    # System info
    uptime: float
    timestamp: datetime

@dataclass
class ResourceAlert:
    """Resource usage alert"""
    resource_type: str
    alert_level: str  # 'warning', 'critical'
    message: str
    current_value: float
    threshold: float
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class PerformanceBaseline:
    """Performance baseline for comparison"""
    metric_name: str
    baseline_value: float
    current_value: float
    deviation_percent: float
    last_updated: datetime

class ResourceMonitor:
    """
    Comprehensive resource monitoring system
    """
    
    def __init__(self):
        self.is_monitoring = False
        self.monitoring_thread = None
        self._lock = threading.Lock()
        
        # Monitoring configuration
        self.monitoring_interval = 10  # seconds
        self.metrics_history_size = 1000
        self.alert_callbacks = []
        
        # Resource thresholds
        self.thresholds = {
            'cpu_warning': 75.0,
            'cpu_critical': 90.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'load_warning': psutil.cpu_count() * 0.8,
            'load_critical': psutil.cpu_count() * 1.5
        }
        
        # Metrics storage
        self.metrics_history = deque(maxlen=self.metrics_history_size)
        self.performance_baselines = {}
        self.last_alerts = {}
        self.alert_cooldown = 300  # 5 minutes
        
        # System information
        self.system_info = self._collect_system_info()
    
    def start_monitoring(self) -> bool:
        """
        Start resource monitoring
        
        Returns:
            Success status
        """
        try:
            if self.is_monitoring:
                logger.warning("Resource monitoring is already running")
                return True
            
            self.is_monitoring = True
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="ResourceMonitor"
            )
            self.monitoring_thread.start()
            
            logger.info("Resource monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting resource monitoring: {str(e)}")
            return False
    
    def stop_monitoring(self) -> bool:
        """
        Stop resource monitoring
        
        Returns:
            Success status
        """
        try:
            self.is_monitoring = False
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=10)
            
            logger.info("Resource monitoring stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping resource monitoring: {str(e)}")
            return False
    
    def get_current_metrics(self) -> SystemMetrics:
        """
        Get current system metrics
        
        Returns:
            Current system metrics
        """
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            try:
                load_avg = list(psutil.getloadavg())
            except AttributeError:
                # Windows doesn't have getloadavg
                load_avg = [0.0, 0.0, 0.0]
            
            # Memory metrics
            memory = psutil.virtual_memory()
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            # Network metrics
            net_io = psutil.net_io_counters()
            
            # Process metrics
            process_count = len(psutil.pids())
            current_process = psutil.Process()
            thread_count = current_process.num_threads()
            
            try:
                fd_count = current_process.num_fds()
            except AttributeError:
                # Windows doesn't have num_fds
                fd_count = 0
            
            # System uptime
            uptime = time.time() - psutil.boot_time()
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                cpu_count=cpu_count,
                cpu_freq_current=cpu_freq.current if cpu_freq else 0,
                cpu_freq_max=cpu_freq.max if cpu_freq else 0,
                load_average=load_avg,
                memory_total=memory.total,
                memory_available=memory.available,
                memory_used=memory.used,
                memory_percent=memory.percent,
                disk_total=disk.total,
                disk_used=disk.used,
                disk_free=disk.free,
                disk_percent=(disk.used / disk.total) * 100,
                disk_read_bytes=disk_io.read_bytes if disk_io else 0,
                disk_write_bytes=disk_io.write_bytes if disk_io else 0,
                network_bytes_sent=net_io.bytes_sent,
                network_bytes_recv=net_io.bytes_recv,
                network_packets_sent=net_io.packets_sent,
                network_packets_recv=net_io.packets_recv,
                process_count=process_count,
                thread_count=thread_count,
                fd_count=fd_count,
                uptime=uptime,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {str(e)}")
            # Return empty metrics on error
            return SystemMetrics(
                cpu_percent=0, cpu_count=0, cpu_freq_current=0, cpu_freq_max=0,
                load_average=[0, 0, 0], memory_total=0, memory_available=0,
                memory_used=0, memory_percent=0, disk_total=0, disk_used=0,
                disk_free=0, disk_percent=0, disk_read_bytes=0, disk_write_bytes=0,
                network_bytes_sent=0, network_bytes_recv=0, network_packets_sent=0,
                network_packets_recv=0, process_count=0, thread_count=0,
                fd_count=0, uptime=0, timestamp=datetime.now()
            )
    
    def get_resource_utilization_summary(self) -> Dict[str, Any]:
        """
        Get summary of resource utilization
        
        Returns:
            Resource utilization summary
        """
        try:
            metrics = self.get_current_metrics()
            
            summary = {
                'cpu': {
                    'current_percent': metrics.cpu_percent,
                    'status': self._get_resource_status('cpu', metrics.cpu_percent),
                    'cores': metrics.cpu_count,
                    'load_average': metrics.load_average
                },
                'memory': {
                    'current_percent': metrics.memory_percent,
                    'status': self._get_resource_status('memory', metrics.memory_percent),
                    'total_gb': metrics.memory_total / (1024**3),
                    'available_gb': metrics.memory_available / (1024**3),
                    'used_gb': metrics.memory_used / (1024**3)
                },
                'disk': {
                    'current_percent': metrics.disk_percent,
                    'status': self._get_resource_status('disk', metrics.disk_percent),
                    'total_gb': metrics.disk_total / (1024**3),
                    'used_gb': metrics.disk_used / (1024**3),
                    'free_gb': metrics.disk_free / (1024**3)
                },
                'system': {
                    'uptime_hours': metrics.uptime / 3600,
                    'process_count': metrics.process_count,
                    'thread_count': metrics.thread_count,
                    'timestamp': metrics.timestamp.isoformat()
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting resource utilization summary: {str(e)}")
            return {}
    
    def get_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get performance trends over specified time period
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Performance trend analysis
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Filter metrics by time
            recent_metrics = [
                m for m in self.metrics_history 
                if m.timestamp > cutoff_time
            ]
            
            if not recent_metrics:
                return {'error': 'Insufficient data for trend analysis'}
            
            # Calculate trends
            trends = {}
            
            # CPU trend
            cpu_values = [m.cpu_percent for m in recent_metrics]
            trends['cpu'] = {
                'average': sum(cpu_values) / len(cpu_values),
                'peak': max(cpu_values),
                'minimum': min(cpu_values),
                'trend': self._calculate_trend(cpu_values),
                'data_points': len(cpu_values)
            }
            
            # Memory trend
            memory_values = [m.memory_percent for m in recent_metrics]
            trends['memory'] = {
                'average': sum(memory_values) / len(memory_values),
                'peak': max(memory_values),
                'minimum': min(memory_values),
                'trend': self._calculate_trend(memory_values),
                'data_points': len(memory_values)
            }
            
            # Disk trend
            disk_values = [m.disk_percent for m in recent_metrics]
            trends['disk'] = {
                'average': sum(disk_values) / len(disk_values),
                'peak': max(disk_values),
                'minimum': min(disk_values),
                'trend': self._calculate_trend(disk_values),
                'data_points': len(disk_values)
            }
            
            # Load average trend
            load_values = [m.load_average[0] for m in recent_metrics if m.load_average]
            if load_values:
                trends['load'] = {
                    'average': sum(load_values) / len(load_values),
                    'peak': max(load_values),
                    'minimum': min(load_values),
                    'trend': self._calculate_trend(load_values),
                    'data_points': len(load_values)
                }
            
            trends['analysis_period_hours'] = hours
            trends['analysis_timestamp'] = datetime.now().isoformat()
            
            return trends
            
        except Exception as e:
            logger.error(f"Error getting performance trends: {str(e)}")
            return {'error': str(e)}
    
    def set_performance_baseline(self, duration_minutes: int = 30) -> bool:
        """
        Set performance baseline based on current metrics
        
        Args:
            duration_minutes: Duration to collect baseline data
            
        Returns:
            Success status
        """
        try:
            logger.info(f"Setting performance baseline over {duration_minutes} minutes")
            
            # Collect metrics for baseline period
            baseline_metrics = []
            end_time = datetime.now() + timedelta(minutes=duration_minutes)
            
            while datetime.now() < end_time:
                metrics = self.get_current_metrics()
                baseline_metrics.append(metrics)
                time.sleep(30)  # Collect every 30 seconds
            
            if not baseline_metrics:
                return False
            
            # Calculate baseline values
            baselines = {
                'cpu_percent': sum(m.cpu_percent for m in baseline_metrics) / len(baseline_metrics),
                'memory_percent': sum(m.memory_percent for m in baseline_metrics) / len(baseline_metrics),
                'disk_percent': sum(m.disk_percent for m in baseline_metrics) / len(baseline_metrics),
                'load_average': sum(m.load_average[0] for m in baseline_metrics if m.load_average) / len(baseline_metrics)
            }
            
            # Store baselines
            for metric_name, baseline_value in baselines.items():
                self.performance_baselines[metric_name] = PerformanceBaseline(
                    metric_name=metric_name,
                    baseline_value=baseline_value,
                    current_value=baseline_value,
                    deviation_percent=0.0,
                    last_updated=datetime.now()
                )
            
            logger.info("Performance baseline established")
            return True
            
        except Exception as e:
            logger.error(f"Error setting performance baseline: {str(e)}")
            return False
    
    def check_resource_health(self) -> Dict[str, Any]:
        """
        Check overall resource health
        
        Returns:
            Resource health status
        """
        try:
            metrics = self.get_current_metrics()
            health_status = {
                'overall_status': 'healthy',
                'issues': [],
                'warnings': [],
                'recommendations': []
            }
            
            # Check CPU health
            if metrics.cpu_percent > self.thresholds['cpu_critical']:
                health_status['overall_status'] = 'critical'
                health_status['issues'].append(f'Critical CPU usage: {metrics.cpu_percent:.1f}%')
            elif metrics.cpu_percent > self.thresholds['cpu_warning']:
                health_status['overall_status'] = 'warning' if health_status['overall_status'] == 'healthy' else health_status['overall_status']
                health_status['warnings'].append(f'High CPU usage: {metrics.cpu_percent:.1f}%')
            
            # Check memory health
            if metrics.memory_percent > self.thresholds['memory_critical']:
                health_status['overall_status'] = 'critical'
                health_status['issues'].append(f'Critical memory usage: {metrics.memory_percent:.1f}%')
            elif metrics.memory_percent > self.thresholds['memory_warning']:
                health_status['overall_status'] = 'warning' if health_status['overall_status'] == 'healthy' else health_status['overall_status']
                health_status['warnings'].append(f'High memory usage: {metrics.memory_percent:.1f}%')
            
            # Check disk health
            if metrics.disk_percent > self.thresholds['disk_critical']:
                health_status['overall_status'] = 'critical'
                health_status['issues'].append(f'Critical disk usage: {metrics.disk_percent:.1f}%')
            elif metrics.disk_percent > self.thresholds['disk_warning']:
                health_status['overall_status'] = 'warning' if health_status['overall_status'] == 'healthy' else health_status['overall_status']
                health_status['warnings'].append(f'High disk usage: {metrics.disk_percent:.1f}%')
            
            # Check load average
            if metrics.load_average and metrics.load_average[0] > self.thresholds['load_critical']:
                health_status['overall_status'] = 'critical'
                health_status['issues'].append(f'Critical system load: {metrics.load_average[0]:.2f}')
            elif metrics.load_average and metrics.load_average[0] > self.thresholds['load_warning']:
                health_status['overall_status'] = 'warning' if health_status['overall_status'] == 'healthy' else health_status['overall_status']
                health_status['warnings'].append(f'High system load: {metrics.load_average[0]:.2f}')
            
            # Generate recommendations
            if metrics.cpu_percent > 80:
                health_status['recommendations'].append('Consider optimizing CPU-intensive processes')
            if metrics.memory_percent > 80:
                health_status['recommendations'].append('Consider memory optimization or adding more RAM')
            if metrics.disk_percent > 80:
                health_status['recommendations'].append('Consider cleaning up disk space or adding storage')
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error checking resource health: {str(e)}")
            return {'overall_status': 'error', 'error': str(e)}
    
    def add_alert_callback(self, callback: Callable[[ResourceAlert], None]):
        """Add callback for resource alerts"""
        self.alert_callbacks.append(callback)
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        return self.system_info
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        logger.info("Resource monitoring loop started")
        
        while self.is_monitoring:
            try:
                # Collect metrics
                metrics = self.get_current_metrics()
                
                # Store in history
                with self._lock:
                    self.metrics_history.append(metrics)
                
                # Check for alerts
                self._check_resource_alerts(metrics)
                
                # Update performance baselines
                self._update_performance_baselines(metrics)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in resource monitoring loop: {str(e)}")
                time.sleep(60)  # Wait before retrying
    
    def _check_resource_alerts(self, metrics: SystemMetrics):
        """Check for resource alerts"""
        try:
            current_time = datetime.now()
            
            # CPU alerts
            if metrics.cpu_percent > self.thresholds['cpu_critical']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='cpu',
                    alert_level='critical',
                    message=f'Critical CPU usage: {metrics.cpu_percent:.1f}%',
                    current_value=metrics.cpu_percent,
                    threshold=self.thresholds['cpu_critical'],
                    timestamp=current_time,
                    metadata={'cpu_count': metrics.cpu_count}
                ))
            elif metrics.cpu_percent > self.thresholds['cpu_warning']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='cpu',
                    alert_level='warning',
                    message=f'High CPU usage: {metrics.cpu_percent:.1f}%',
                    current_value=metrics.cpu_percent,
                    threshold=self.thresholds['cpu_warning'],
                    timestamp=current_time,
                    metadata={'cpu_count': metrics.cpu_count}
                ))
            
            # Memory alerts
            if metrics.memory_percent > self.thresholds['memory_critical']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='memory',
                    alert_level='critical',
                    message=f'Critical memory usage: {metrics.memory_percent:.1f}%',
                    current_value=metrics.memory_percent,
                    threshold=self.thresholds['memory_critical'],
                    timestamp=current_time,
                    metadata={'total_gb': metrics.memory_total / (1024**3)}
                ))
            elif metrics.memory_percent > self.thresholds['memory_warning']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='memory',
                    alert_level='warning',
                    message=f'High memory usage: {metrics.memory_percent:.1f}%',
                    current_value=metrics.memory_percent,
                    threshold=self.thresholds['memory_warning'],
                    timestamp=current_time,
                    metadata={'total_gb': metrics.memory_total / (1024**3)}
                ))
            
            # Disk alerts
            if metrics.disk_percent > self.thresholds['disk_critical']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='disk',
                    alert_level='critical',
                    message=f'Critical disk usage: {metrics.disk_percent:.1f}%',
                    current_value=metrics.disk_percent,
                    threshold=self.thresholds['disk_critical'],
                    timestamp=current_time,
                    metadata={'total_gb': metrics.disk_total / (1024**3)}
                ))
            elif metrics.disk_percent > self.thresholds['disk_warning']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='disk',
                    alert_level='warning',
                    message=f'High disk usage: {metrics.disk_percent:.1f}%',
                    current_value=metrics.disk_percent,
                    threshold=self.thresholds['disk_warning'],
                    timestamp=current_time,
                    metadata={'total_gb': metrics.disk_total / (1024**3)}
                ))
            
            # Load average alerts
            if metrics.load_average and metrics.load_average[0] > self.thresholds['load_critical']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='load',
                    alert_level='critical',
                    message=f'Critical system load: {metrics.load_average[0]:.2f}',
                    current_value=metrics.load_average[0],
                    threshold=self.thresholds['load_critical'],
                    timestamp=current_time,
                    metadata={'cpu_count': metrics.cpu_count}
                ))
            elif metrics.load_average and metrics.load_average[0] > self.thresholds['load_warning']:
                self._send_resource_alert(ResourceAlert(
                    resource_type='load',
                    alert_level='warning',
                    message=f'High system load: {metrics.load_average[0]:.2f}',
                    current_value=metrics.load_average[0],
                    threshold=self.thresholds['load_warning'],
                    timestamp=current_time,
                    metadata={'cpu_count': metrics.cpu_count}
                ))
                
        except Exception as e:
            logger.error(f"Error checking resource alerts: {str(e)}")
    
    def _send_resource_alert(self, alert: ResourceAlert):
        """Send resource alert to callbacks"""
        try:
            # Check cooldown
            alert_key = f"{alert.resource_type}_{alert.alert_level}"
            last_alert_time = self.last_alerts.get(alert_key)
            
            if last_alert_time:
                time_since_last = (alert.timestamp - last_alert_time).total_seconds()
                if time_since_last < self.alert_cooldown:
                    return  # Skip due to cooldown
            
            # Update last alert time
            self.last_alerts[alert_key] = alert.timestamp
            
            # Send to callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"Error in resource alert callback: {str(e)}")
            
            # Log alert
            log_level = logging.CRITICAL if alert.alert_level == 'critical' else logging.WARNING
            logger.log(log_level, f"Resource Alert: {alert.message}")
            
        except Exception as e:
            logger.error(f"Error sending resource alert: {str(e)}")
    
    def _update_performance_baselines(self, metrics: SystemMetrics):
        """Update performance baselines with current metrics"""
        try:
            current_time = datetime.now()
            
            # Update baselines if they exist
            if 'cpu_percent' in self.performance_baselines:
                baseline = self.performance_baselines['cpu_percent']
                baseline.current_value = metrics.cpu_percent
                baseline.deviation_percent = ((metrics.cpu_percent - baseline.baseline_value) / baseline.baseline_value) * 100
                baseline.last_updated = current_time
            
            if 'memory_percent' in self.performance_baselines:
                baseline = self.performance_baselines['memory_percent']
                baseline.current_value = metrics.memory_percent
                baseline.deviation_percent = ((metrics.memory_percent - baseline.baseline_value) / baseline.baseline_value) * 100
                baseline.last_updated = current_time
            
            if 'disk_percent' in self.performance_baselines:
                baseline = self.performance_baselines['disk_percent']
                baseline.current_value = metrics.disk_percent
                baseline.deviation_percent = ((metrics.disk_percent - baseline.baseline_value) / baseline.baseline_value) * 100
                baseline.last_updated = current_time
            
            if 'load_average' in self.performance_baselines and metrics.load_average:
                baseline = self.performance_baselines['load_average']
                baseline.current_value = metrics.load_average[0]
                baseline.deviation_percent = ((metrics.load_average[0] - baseline.baseline_value) / baseline.baseline_value) * 100
                baseline.last_updated = current_time
                
        except Exception as e:
            logger.error(f"Error updating performance baselines: {str(e)}")
    
    def _get_resource_status(self, resource_type: str, value: float) -> str:
        """Get status string for a resource"""
        try:
            if resource_type == 'cpu':
                if value > self.thresholds['cpu_critical']:
                    return 'critical'
                elif value > self.thresholds['cpu_warning']:
                    return 'warning'
                else:
                    return 'healthy'
            elif resource_type == 'memory':
                if value > self.thresholds['memory_critical']:
                    return 'critical'
                elif value > self.thresholds['memory_warning']:
                    return 'warning'
                else:
                    return 'healthy'
            elif resource_type == 'disk':
                if value > self.thresholds['disk_critical']:
                    return 'critical'
                elif value > self.thresholds['disk_warning']:
                    return 'warning'
                else:
                    return 'healthy'
            else:
                return 'unknown'
                
        except Exception:
            return 'unknown'
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from list of values"""
        try:
            if len(values) < 2:
                return 'stable'
            
            # Simple trend calculation using first and last values
            start_avg = sum(values[:len(values)//4]) / (len(values)//4) if len(values) >= 4 else values[0]
            end_avg = sum(values[-len(values)//4:]) / (len(values)//4) if len(values) >= 4 else values[-1]
            
            change_percent = ((end_avg - start_avg) / start_avg) * 100 if start_avg > 0 else 0
            
            if change_percent > 5:
                return 'increasing'
            elif change_percent < -5:
                return 'decreasing'
            else:
                return 'stable'
                
        except Exception:
            return 'unknown'
    
    def _collect_system_info(self) -> Dict[str, Any]:
        """Collect comprehensive system information"""
        try:
            return {
                'platform': {
                    'system': platform.system(),
                    'release': platform.release(),
                    'version': platform.version(),
                    'machine': platform.machine(),
                    'processor': platform.processor(),
                    'architecture': platform.architecture()
                },
                'cpu': {
                    'physical_cores': psutil.cpu_count(logical=False),
                    'logical_cores': psutil.cpu_count(logical=True),
                    'max_frequency': psutil.cpu_freq().max if psutil.cpu_freq() else 0,
                    'min_frequency': psutil.cpu_freq().min if psutil.cpu_freq() else 0
                },
                'memory': {
                    'total_gb': psutil.virtual_memory().total / (1024**3),
                    'swap_total_gb': psutil.swap_memory().total / (1024**3)
                },
                'disk': {
                    'partitions': [
                        {
                            'device': partition.device,
                            'mountpoint': partition.mountpoint,
                            'filesystem': partition.fstype
                        }
                        for partition in psutil.disk_partitions()
                    ]
                },
                'network': {
                    'interfaces': list(psutil.net_if_addrs().keys())
                },
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat(),
                'collection_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error collecting system info: {str(e)}")
            return {'error': str(e)}


# Global resource monitor instance
resource_monitor = ResourceMonitor()


# ==================== CONVENIENCE FUNCTIONS ====================

def get_current_system_metrics() -> SystemMetrics:
    """Get current system metrics"""
    return resource_monitor.get_current_metrics()

def get_resource_utilization() -> Dict[str, Any]:
    """Get resource utilization summary"""
    return resource_monitor.get_resource_utilization_summary()

def start_resource_monitoring() -> bool:
    """Start resource monitoring"""
    return resource_monitor.start_monitoring()

def stop_resource_monitoring() -> bool:
    """Stop resource monitoring"""
    return resource_monitor.stop_monitoring()

def check_system_health() -> Dict[str, Any]:
    """Check overall system health"""
    return resource_monitor.check_resource_health()

def get_performance_trends(hours: int = 24) -> Dict[str, Any]:
    """Get performance trends"""
    return resource_monitor.get_performance_trends(hours)

def set_performance_baseline(duration_minutes: int = 30) -> bool:
    """Set performance baseline"""
    return resource_monitor.set_performance_baseline(duration_minutes)

def add_resource_alert_callback(callback: Callable[[ResourceAlert], None]):
    """Add callback for resource alerts"""
    resource_monitor.add_alert_callback(callback)

def get_system_information() -> Dict[str, Any]:
    """Get comprehensive system information"""
    return resource_monitor.get_system_info()