# queue_system/scaling/__init__.py
"""
Scaling submodule for PhotoFish Enhanced Queue System
Provides horizontal scaling, load balancing, and auto-scaling capabilities
"""

from .horizontal_scaler import HorizontalScaler, ScalingMetrics, ScalingEvent
from .load_balancer import LoadBalancer, WorkerInfo, LoadBalancingRule
from .auto_scaler import AutoScaler, ScalingPolicy, ScalingDecision, PredictiveMetrics
from .cluster_manager import ClusterManager, ClusterNode, ClusterConfiguration, ClusterEvent

__all__ = [
    'HorizontalScaler',
    'ScalingMetrics', 
    'ScalingEvent',
    'LoadBalancer',
    'WorkerInfo',
    'LoadBalancingRule',
    'AutoScaler',
    'ScalingPolicy',
    'ScalingDecision',
    'PredictiveMetrics',
    'ClusterManager',
    'ClusterNode',
    'ClusterConfiguration',
    'ClusterEvent'
]