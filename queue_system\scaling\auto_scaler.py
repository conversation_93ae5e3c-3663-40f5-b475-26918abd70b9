# queue_system/scaling/auto_scaler.py
"""
Auto-scaling module for PhotoFish Enhanced Queue System
Provides intelligent, metric-based automatic scaling decisions
"""

import logging
import threading
import time
import statistics
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import deque, defaultdict
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

@dataclass
class ScalingPolicy:
    """Auto-scaling policy configuration"""
    name: str
    priority_level: str
    scale_out_threshold: float
    scale_in_threshold: float
    evaluation_period: int  # seconds
    cooldown_period: int  # seconds
    min_instances: int
    max_instances: int
    scaling_step: int
    enabled: bool

@dataclass
class ScalingDecision:
    """Represents a scaling decision"""
    priority_level: str
    action: str  # 'scale_out', 'scale_in', 'maintain'
    current_workers: int
    target_workers: int
    reason: str
    confidence: float
    timestamp: datetime
    policy_name: str

@dataclass
class PredictiveMetrics:
    """Predictive metrics for proactive scaling"""
    predicted_load: float
    confidence_interval: Tuple[float, float]
    time_horizon: int  # minutes
    trend_direction: str  # 'increasing', 'decreasing', 'stable'
    seasonality_factor: float

class AutoScaler:
    """
    Intelligent auto-scaling system with predictive capabilities
    """
    
    def __init__(self):
        self.policies = self._initialize_policies()
        self.metrics_history = defaultdict(lambda: deque(maxlen=1000))
        self.scaling_history = deque(maxlen=500)
        self.is_running = False
        self.evaluation_thread = None
        self._lock = threading.Lock()
        
        # Predictive scaling parameters
        self.enable_predictive_scaling = True
        self.prediction_window = 15  # minutes
        self.learning_period = 7  # days
        
        # Performance tracking
        self.scaling_effectiveness = {}
        self.false_positive_rate = {}
    
    def _initialize_policies(self) -> Dict[str, ScalingPolicy]:
        """Initialize auto-scaling policies for different priority levels"""
        policies = {}
        
        policy_configs = {
            'EMERGENCY': {
                'scale_out_threshold': 60.0,  # % utilization
                'scale_in_threshold': 20.0,
                'evaluation_period': 30,  # 30 seconds
                'cooldown_period': 120,  # 2 minutes
                'min_instances': 2,
                'max_instances': 8,
                'scaling_step': 2,
                'enabled': True
            },
            'HIGH': {
                'scale_out_threshold': 70.0,
                'scale_in_threshold': 25.0,
                'evaluation_period': 60,  # 1 minute
                'cooldown_period': 300,  # 5 minutes
                'min_instances': 3,
                'max_instances': 10,
                'scaling_step': 1,
                'enabled': True
            },
            'STANDARD': {
                'scale_out_threshold': 80.0,
                'scale_in_threshold': 30.0,
                'evaluation_period': 120,  # 2 minutes
                'cooldown_period': 300,  # 5 minutes
                'min_instances': 2,
                'max_instances': 8,
                'scaling_step': 1,
                'enabled': True
            },
            'LOW': {
                'scale_out_threshold': 85.0,
                'scale_in_threshold': 35.0,
                'evaluation_period': 300,  # 5 minutes
                'cooldown_period': 600,  # 10 minutes
                'min_instances': 1,
                'max_instances': 4,
                'scaling_step': 1,
                'enabled': True
            },
            'MAINTENANCE': {
                'scale_out_threshold': 90.0,
                'scale_in_threshold': 40.0,
                'evaluation_period': 600,  # 10 minutes
                'cooldown_period': 1200,  # 20 minutes
                'min_instances': 1,
                'max_instances': 2,
                'scaling_step': 1,
                'enabled': True
            }
        }
        
        for priority, config in policy_configs.items():
            policies[priority] = ScalingPolicy(
                name=f"auto_scale_{priority.lower()}",
                priority_level=priority,
                scale_out_threshold=config['scale_out_threshold'],
                scale_in_threshold=config['scale_in_threshold'],
                evaluation_period=config['evaluation_period'],
                cooldown_period=config['cooldown_period'],
                min_instances=config['min_instances'],
                max_instances=config['max_instances'],
                scaling_step=config['scaling_step'],
                enabled=config['enabled']
            )
        
        return policies
    
    def start(self) -> bool:
        """
        Start the auto-scaling evaluation loop
        
        Returns:
            Success status
        """
        try:
            if self.is_running:
                logger.warning("Auto-scaler is already running")
                return True
            
            self.is_running = True
            self.evaluation_thread = threading.Thread(target=self._evaluation_loop, daemon=True)
            self.evaluation_thread.start()
            
            logger.info("Auto-scaler started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting auto-scaler: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """
        Stop the auto-scaling evaluation loop
        
        Returns:
            Success status
        """
        try:
            self.is_running = False
            
            if self.evaluation_thread and self.evaluation_thread.is_alive():
                self.evaluation_thread.join(timeout=10)
            
            logger.info("Auto-scaler stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping auto-scaler: {str(e)}")
            return False
    
    def evaluate_scaling_metrics(self, metrics_by_priority: Dict[str, Any]) -> List[ScalingDecision]:
        """
        Evaluate current metrics and generate scaling decisions
        
        Args:
            metrics_by_priority: Current metrics for each priority level
            
        Returns:
            List of scaling decisions
        """
        decisions = []
        
        try:
            with self._lock:
                # Update metrics history
                self._update_metrics_history(metrics_by_priority)
                
                for priority, metrics in metrics_by_priority.items():
                    policy = self.policies.get(priority)
                    if not policy or not policy.enabled:
                        continue
                    
                    # Evaluate reactive scaling
                    reactive_decision = self._evaluate_reactive_scaling(priority, metrics, policy)
                    
                    # Evaluate predictive scaling if enabled
                    if self.enable_predictive_scaling:
                        predictive_decision = self._evaluate_predictive_scaling(priority, metrics, policy)
                        
                        # Choose the more conservative decision
                        final_decision = self._merge_scaling_decisions(reactive_decision, predictive_decision)
                    else:
                        final_decision = reactive_decision
                    
                    if final_decision and final_decision.action != 'maintain':
                        decisions.append(final_decision)
            
            return decisions
            
        except Exception as e:
            logger.error(f"Error evaluating scaling metrics: {str(e)}")
            return []
    
    def execute_scaling_decision(self, decision: ScalingDecision) -> bool:
        """
        Execute a scaling decision
        
        Args:
            decision: Scaling decision to execute
            
        Returns:
            Success status
        """
        try:
            # Check cooldown period
            if not self._check_cooldown(decision.priority_level):
                logger.info(f"Scaling blocked by cooldown for {decision.priority_level}")
                return False
            
            # Execute the scaling action
            if decision.action == 'scale_out':
                success = self._execute_scale_out(decision)
            elif decision.action == 'scale_in':
                success = self._execute_scale_in(decision)
            else:
                logger.warning(f"Unknown scaling action: {decision.action}")
                return False
            
            if success:
                # Record scaling event
                self._record_scaling_decision(decision)
                
                # Update cooldown
                self._update_cooldown(decision.priority_level)
                
                logger.info(f"Successfully executed {decision.action} for {decision.priority_level}: "
                           f"{decision.current_workers} -> {decision.target_workers}")
            else:
                logger.error(f"Failed to execute {decision.action} for {decision.priority_level}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error executing scaling decision: {str(e)}")
            return False
    
    def predict_scaling_needs(self, priority_level: str, time_horizon_minutes: int = 15) -> Optional[PredictiveMetrics]:
        """
        Predict future scaling needs based on historical patterns
        
        Args:
            priority_level: Priority level to predict for
            time_horizon_minutes: How far into the future to predict
            
        Returns:
            Predictive metrics or None if insufficient data
        """
        try:
            history = self.metrics_history.get(priority_level)
            if not history or len(history) < 50:  # Need sufficient data
                return None
            
            # Extract utilization values
            utilizations = [m.get('utilization', 0) for m in list(history)[-100:]]
            
            if not utilizations:
                return None
            
            # Simple trend analysis
            recent_trend = self._calculate_trend(utilizations[-20:])
            seasonal_factor = self._calculate_seasonality_factor(priority_level)
            
            # Predict future load
            current_load = utilizations[-1]
            predicted_load = current_load + (recent_trend * time_horizon_minutes) * seasonal_factor
            
            # Calculate confidence interval
            variance = statistics.variance(utilizations[-50:]) if len(utilizations) >= 50 else 10.0
            confidence_interval = (
                max(0, predicted_load - variance),
                min(100, predicted_load + variance)
            )
            
            # Determine trend direction
            if recent_trend > 2:
                trend_direction = 'increasing'
            elif recent_trend < -2:
                trend_direction = 'decreasing'
            else:
                trend_direction = 'stable'
            
            return PredictiveMetrics(
                predicted_load=predicted_load,
                confidence_interval=confidence_interval,
                time_horizon=time_horizon_minutes,
                trend_direction=trend_direction,
                seasonality_factor=seasonal_factor
            )
            
        except Exception as e:
            logger.error(f"Error predicting scaling needs for {priority_level}: {str(e)}")
            return None
    
    def optimize_scaling_policies(self) -> Dict[str, Dict]:
        """
        Optimize scaling policies based on historical performance
        
        Returns:
            Optimization recommendations
        """
        recommendations = {}
        
        try:
            for priority, policy in self.policies.items():
                effectiveness = self.scaling_effectiveness.get(priority, {})
                false_positive_rate = self.false_positive_rate.get(priority, 0)
                
                recommendation = {
                    'current_thresholds': {
                        'scale_out': policy.scale_out_threshold,
                        'scale_in': policy.scale_in_threshold
                    },
                    'recommended_changes': [],
                    'performance_metrics': effectiveness,
                    'false_positive_rate': false_positive_rate
                }
                
                # Analyze scaling effectiveness
                if effectiveness.get('scale_out_success_rate', 0) < 0.7:
                    recommendation['recommended_changes'].append({
                        'parameter': 'scale_out_threshold',
                        'change': 'increase',
                        'reason': 'Low scale-out success rate'
                    })
                
                if false_positive_rate > 0.3:
                    recommendation['recommended_changes'].append({
                        'parameter': 'evaluation_period',
                        'change': 'increase',
                        'reason': 'High false positive rate'
                    })
                
                recommendations[priority] = recommendation
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error optimizing scaling policies: {str(e)}")
            return {}
    
    def get_scaling_status(self) -> Dict[str, Any]:
        """
        Get current auto-scaling status and statistics
        
        Returns:
            Scaling status information
        """
        try:
            status = {
                'is_running': self.is_running,
                'policies': {},
                'recent_decisions': list(self.scaling_history)[-10:],
                'performance_summary': {}
            }
            
            # Policy status
            for priority, policy in self.policies.items():
                cooldown_remaining = self._get_cooldown_remaining(priority)
                
                status['policies'][priority] = {
                    'enabled': policy.enabled,
                    'scale_out_threshold': policy.scale_out_threshold,
                    'scale_in_threshold': policy.scale_in_threshold,
                    'cooldown_remaining_seconds': cooldown_remaining,
                    'min_instances': policy.min_instances,
                    'max_instances': policy.max_instances
                }
            
            # Performance summary
            for priority in self.policies.keys():
                effectiveness = self.scaling_effectiveness.get(priority, {})
                status['performance_summary'][priority] = {
                    'total_scaling_events': effectiveness.get('total_events', 0),
                    'success_rate': effectiveness.get('overall_success_rate', 0),
                    'average_response_time': effectiveness.get('avg_response_time', 0),
                    'false_positive_rate': self.false_positive_rate.get(priority, 0)
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting scaling status: {str(e)}")
            return {'error': str(e)}
    
    def _evaluation_loop(self):
        """Main evaluation loop for auto-scaling"""
        logger.info("Auto-scaling evaluation loop started")
        
        while self.is_running:
            try:
                # Get current metrics
                metrics = self._collect_current_metrics()
                
                if metrics:
                    # Evaluate scaling decisions
                    decisions = self.evaluate_scaling_metrics(metrics)
                    
                    # Execute decisions
                    for decision in decisions:
                        self.execute_scaling_decision(decision)
                
                # Sleep for shortest evaluation period
                min_period = min(policy.evaluation_period for policy in self.policies.values() if policy.enabled)
                time.sleep(min_period)
                
            except Exception as e:
                logger.error(f"Error in auto-scaling evaluation loop: {str(e)}")
                time.sleep(60)  # Wait before retrying
    
    def _evaluate_reactive_scaling(self, priority: str, metrics: Dict, policy: ScalingPolicy) -> Optional[ScalingDecision]:
        """Evaluate reactive scaling based on current metrics"""
        try:
            current_workers = metrics.get('current_workers', 1)
            utilization = metrics.get('utilization', 0)
            queue_length = metrics.get('queue_length', 0)
            
            # Determine scaling action
            action = 'maintain'
            target_workers = current_workers
            reason = "Metrics within normal range"
            confidence = 0.5
            
            if utilization > policy.scale_out_threshold and current_workers < policy.max_instances:
                action = 'scale_out'
                target_workers = min(policy.max_instances, current_workers + policy.scaling_step)
                reason = f"High utilization: {utilization:.1f}% > {policy.scale_out_threshold}%"
                confidence = min(1.0, (utilization - policy.scale_out_threshold) / 20.0)
                
            elif utilization < policy.scale_in_threshold and current_workers > policy.min_instances:
                action = 'scale_in'
                target_workers = max(policy.min_instances, current_workers - policy.scaling_step)
                reason = f"Low utilization: {utilization:.1f}% < {policy.scale_in_threshold}%"
                confidence = min(1.0, (policy.scale_in_threshold - utilization) / 20.0)
            
            if action != 'maintain':
                return ScalingDecision(
                    priority_level=priority,
                    action=action,
                    current_workers=current_workers,
                    target_workers=target_workers,
                    reason=reason,
                    confidence=confidence,
                    timestamp=datetime.now(),
                    policy_name=policy.name
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in reactive scaling evaluation for {priority}: {str(e)}")
            return None
    
    def _evaluate_predictive_scaling(self, priority: str, metrics: Dict, policy: ScalingPolicy) -> Optional[ScalingDecision]:
        """Evaluate predictive scaling based on trends and patterns"""
        try:
            prediction = self.predict_scaling_needs(priority, self.prediction_window)
            
            if not prediction:
                return None
            
            current_workers = metrics.get('current_workers', 1)
            predicted_utilization = prediction.predicted_load
            
            # More conservative thresholds for predictive scaling
            scale_out_threshold = policy.scale_out_threshold + 10
            scale_in_threshold = policy.scale_in_threshold - 10
            
            action = 'maintain'
            target_workers = current_workers
            reason = "Predicted metrics within range"
            confidence = 0.3  # Lower confidence for predictive scaling
            
            if (prediction.trend_direction == 'increasing' and 
                predicted_utilization > scale_out_threshold and 
                current_workers < policy.max_instances):
                
                action = 'scale_out'
                target_workers = min(policy.max_instances, current_workers + 1)  # More conservative
                reason = f"Predicted high utilization: {predicted_utilization:.1f}% in {self.prediction_window}min"
                confidence = 0.4
                
            elif (prediction.trend_direction == 'decreasing' and 
                  predicted_utilization < scale_in_threshold and 
                  current_workers > policy.min_instances):
                
                action = 'scale_in'
                target_workers = max(policy.min_instances, current_workers - 1)
                reason = f"Predicted low utilization: {predicted_utilization:.1f}% in {self.prediction_window}min"
                confidence = 0.3
            
            if action != 'maintain':
                return ScalingDecision(
                    priority_level=priority,
                    action=action,
                    current_workers=current_workers,
                    target_workers=target_workers,
                    reason=f"PREDICTIVE: {reason}",
                    confidence=confidence,
                    timestamp=datetime.now(),
                    policy_name=f"{policy.name}_predictive"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error in predictive scaling evaluation for {priority}: {str(e)}")
            return None
    
    def _merge_scaling_decisions(self, reactive: Optional[ScalingDecision], 
                               predictive: Optional[ScalingDecision]) -> Optional[ScalingDecision]:
        """Merge reactive and predictive scaling decisions"""
        if not reactive and not predictive:
            return None
        
        if not predictive:
            return reactive
        
        if not reactive:
            # Only use predictive if confidence is reasonable
            return predictive if predictive.confidence > 0.3 else None
        
        # Both exist - choose more conservative action
        if reactive.action == predictive.action:
            # Same action - use reactive decision with higher confidence
            reactive.confidence = min(1.0, reactive.confidence + 0.1)
            reactive.reason += f" (Confirmed by prediction: {predictive.reason})"
            return reactive
        
        # Different actions - be conservative, prefer reactive
        return reactive
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend slope for given values"""
        if len(values) < 2:
            return 0.0
        
        n = len(values)
        x_values = list(range(n))
        
        # Simple linear regression
        x_mean = sum(x_values) / n
        y_mean = sum(values) / n
        
        numerator = sum((x_values[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    def _calculate_seasonality_factor(self, priority: str) -> float:
        """Calculate seasonality factor based on time of day/week"""
        now = datetime.now()
        hour = now.hour
        day_of_week = now.weekday()
        
        # Simple seasonality model
        # Higher activity during business hours and weekdays
        if priority in ['HIGH', 'EMERGENCY']:
            if 9 <= hour <= 17 and day_of_week < 5:  # Business hours, weekdays
                return 1.2
            elif 18 <= hour <= 22:  # Evening hours
                return 1.1
            else:
                return 0.9
        else:
            # Standard and low priority less affected by time
            return 1.0
    
    def _collect_current_metrics(self) -> Optional[Dict]:
        """Collect current system metrics"""
        try:
            from ..monitoring.metrics_collector import MetricsCollector
            
            metrics_collector = MetricsCollector()
            return metrics_collector.get_current_metrics_by_priority()
            
        except Exception as e:
            logger.error(f"Error collecting current metrics: {str(e)}")
            return None
    
    def _execute_scale_out(self, decision: ScalingDecision) -> bool:
        """Execute scale out decision"""
        try:
            from ..scaling.horizontal_scaler import HorizontalScaler
            
            scaler = HorizontalScaler()
            return scaler.scale_out(
                decision.priority_level,
                decision.target_workers,
                f"AUTO: {decision.reason}"
            )
            
        except Exception as e:
            logger.error(f"Error executing scale out: {str(e)}")
            return False
    
    def _execute_scale_in(self, decision: ScalingDecision) -> bool:
        """Execute scale in decision"""
        try:
            from ..scaling.horizontal_scaler import HorizontalScaler
            
            scaler = HorizontalScaler()
            return scaler.scale_in(
                decision.priority_level,
                decision.target_workers,
                f"AUTO: {decision.reason}"
            )
            
        except Exception as e:
            logger.error(f"Error executing scale in: {str(e)}")
            return False
    
    def _check_cooldown(self, priority_level: str) -> bool:
        """Check if cooldown period has passed"""
        policy = self.policies.get(priority_level)
        if not policy:
            return False
        
        last_scaling = cache.get(f"auto_scale_cooldown_{priority_level}")
        if not last_scaling:
            return True
        
        return (datetime.now() - last_scaling).seconds >= policy.cooldown_period
    
    def _update_cooldown(self, priority_level: str):
        """Update cooldown timer"""
        policy = self.policies.get(priority_level)
        if policy:
            cache.set(
                f"auto_scale_cooldown_{priority_level}",
                datetime.now(),
                timeout=policy.cooldown_period
            )
    
    def _get_cooldown_remaining(self, priority_level: str) -> int:
        """Get remaining cooldown time in seconds"""
        policy = self.policies.get(priority_level)
        if not policy:
            return 0
        
        last_scaling = cache.get(f"auto_scale_cooldown_{priority_level}")
        if not last_scaling:
            return 0
        
        elapsed = (datetime.now() - last_scaling).seconds
        return max(0, policy.cooldown_period - elapsed)
    
    def _update_metrics_history(self, metrics_by_priority: Dict[str, Any]):
        """Update metrics history for trend analysis"""
        timestamp = datetime.now()
        
        for priority, metrics in metrics_by_priority.items():
            history_entry = {
                'timestamp': timestamp,
                'utilization': metrics.get('utilization', 0),
                'queue_length': metrics.get('queue_length', 0),
                'processing_rate': metrics.get('processing_rate', 0),
                'error_rate': metrics.get('error_rate', 0)
            }
            
            self.metrics_history[priority].append(history_entry)
    
    def _record_scaling_decision(self, decision: ScalingDecision):
        """Record scaling decision for analysis"""
        self.scaling_history.append(decision)
        
        # Update effectiveness metrics
        self._update_effectiveness_metrics(decision)
    
    def _update_effectiveness_metrics(self, decision: ScalingDecision):
        """Update scaling effectiveness metrics"""
        try:
            priority = decision.priority_level
            
            if priority not in self.scaling_effectiveness:
                self.scaling_effectiveness[priority] = {
                    'total_events': 0,
                    'successful_events': 0,
                    'scale_out_success_rate': 0,
                    'scale_in_success_rate': 0,
                    'overall_success_rate': 0,
                    'avg_response_time': 0
                }
            
            metrics = self.scaling_effectiveness[priority]
            metrics['total_events'] += 1
            
            # This would be updated later when we can measure the actual success
            # For now, we assume the decision execution was successful
            metrics['successful_events'] += 1
            metrics['overall_success_rate'] = metrics['successful_events'] / metrics['total_events']
            
        except Exception as e:
            logger.error(f"Error updating effectiveness metrics: {str(e)}")