# queue_system/scaling/cluster_manager.py
"""
Cluster management for PhotoFish Enhanced Queue System
Manages multi-node queue clusters for enterprise-level scaling
"""

import logging
import threading
import time
import uuid
import json
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)

@dataclass
class ClusterNode:
    """Represents a node in the queue cluster"""
    node_id: str
    hostname: str
    ip_address: str
    port: int
    status: str  # 'active', 'inactive', 'maintenance', 'failed'
    capabilities: List[str]  # Priority levels this node can handle
    current_load: int
    max_capacity: int
    last_heartbeat: datetime
    cluster_role: str  # 'leader', 'follower', 'candidate'
    version: str
    
@dataclass
class ClusterConfiguration:
    """Cluster configuration settings"""
    cluster_id: str
    cluster_name: str
    leader_node_id: Optional[str]
    total_nodes: int
    active_nodes: int
    replication_factor: int
    load_balancing_algorithm: str
    failover_enabled: bool
    auto_scaling_enabled: bool

@dataclass
class ClusterEvent:
    """Cluster event for monitoring and logging"""
    event_id: str
    event_type: str  # 'node_join', 'node_leave', 'leader_election', 'failover', 'rebalance'
    node_id: str
    timestamp: datetime
    details: Dict[str, Any]
    severity: str  # 'info', 'warning', 'error', 'critical'

class ClusterManager:
    """
    Manages a distributed cluster of queue processing nodes
    Provides high availability, fault tolerance, and horizontal scaling
    """
    
    def __init__(self, node_id: Optional[str] = None):
        self.node_id = node_id or str(uuid.uuid4())
        self.cluster_id = self._get_cluster_id()
        self.nodes = {}  # node_id -> ClusterNode
        self.cluster_config = self._initialize_cluster_config()
        
        # Leadership and consensus
        self.is_leader = False
        self.leader_node_id = None
        self.election_in_progress = False
        self.last_leader_heartbeat = datetime.min
        
        # Cluster coordination
        self.cluster_events = []
        self.node_health_checks = {}
        self.load_distribution_strategy = 'round_robin'
        
        # Thread safety
        self._lock = threading.Lock()
        self.heartbeat_thread = None
        self.monitoring_thread = None
        self.is_running = False
        
        # Cluster settings
        self.heartbeat_interval = 30  # seconds
        self.leader_timeout = 90  # seconds
        self.node_timeout = 120  # seconds
        self.rebalance_threshold = 0.3  # 30% load imbalance triggers rebalance
    
    def join_cluster(self, hostname: str, ip_address: str, port: int, 
                    capabilities: List[str]) -> bool:
        """
        Join this node to the cluster
        
        Args:
            hostname: Node hostname
            ip_address: Node IP address
            port: Node port
            capabilities: List of capabilities (priority levels)
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                # Create node info
                node_info = ClusterNode(
                    node_id=self.node_id,
                    hostname=hostname,
                    ip_address=ip_address,
                    port=port,
                    status='active',
                    capabilities=capabilities,
                    current_load=0,
                    max_capacity=self._calculate_node_capacity(),
                    last_heartbeat=datetime.now(),
                    cluster_role='follower',
                    version=self._get_version()
                )
                
                # Register with cluster
                success = self._register_node(node_info)
                
                if success:
                    self.nodes[self.node_id] = node_info
                    
                    # Start cluster services
                    self._start_cluster_services()
                    
                    # Log cluster join event
                    self._log_cluster_event(
                        'node_join',
                        self.node_id,
                        {'hostname': hostname, 'capabilities': capabilities},
                        'info'
                    )
                    
                    logger.info(f"Successfully joined cluster {self.cluster_id} as node {self.node_id}")
                    
                    # Trigger leader election if no leader
                    if not self.leader_node_id:
                        self._trigger_leader_election()
                
                return success
                
        except Exception as e:
            logger.error(f"Error joining cluster: {str(e)}")
            return False
    
    def leave_cluster(self, graceful: bool = True) -> bool:
        """
        Remove this node from the cluster
        
        Args:
            graceful: Whether to perform graceful shutdown
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                if graceful:
                    # Redistribute jobs from this node
                    self._redistribute_node_jobs(self.node_id)
                    
                    # Transfer leadership if this is the leader
                    if self.is_leader:
                        self._transfer_leadership()
                
                # Stop cluster services
                self._stop_cluster_services()
                
                # Unregister from cluster
                success = self._unregister_node(self.node_id)
                
                if success:
                    # Log cluster leave event
                    self._log_cluster_event(
                        'node_leave',
                        self.node_id,
                        {'graceful': graceful},
                        'info'
                    )
                    
                    logger.info(f"Successfully left cluster {self.cluster_id}")
                
                return success
                
        except Exception as e:
            logger.error(f"Error leaving cluster: {str(e)}")
            return False
    
    def coordinate_cluster(self) -> bool:
        """
        Coordinate cluster operations (leader responsibility)
        
        Returns:
            Success status
        """
        try:
            if not self.is_leader:
                logger.warning("Only leader node can coordinate cluster")
                return False
            
            with self._lock:
                # Update cluster configuration
                self._update_cluster_config()
                
                # Monitor node health
                self._check_node_health()
                
                # Rebalance load if needed
                if self._should_rebalance_cluster():
                    self._rebalance_cluster_load()
                
                # Handle failed nodes
                failed_nodes = self._identify_failed_nodes()
                if failed_nodes:
                    self._handle_node_failures(failed_nodes)
                
                logger.debug(f"Cluster coordination completed for {len(self.nodes)} nodes")
                return True
                
        except Exception as e:
            logger.error(f"Error coordinating cluster: {str(e)}")
            return False
    
    def monitor_node_health(self, node_id: str) -> Dict[str, Any]:
        """
        Monitor health of a specific node
        
        Args:
            node_id: Node to monitor
            
        Returns:
            Node health information
        """
        try:
            node = self.nodes.get(node_id)
            if not node:
                return {'status': 'unknown', 'error': 'Node not found'}
            
            # Calculate health metrics
            time_since_heartbeat = (datetime.now() - node.last_heartbeat).seconds
            utilization = (node.current_load / node.max_capacity) * 100 if node.max_capacity > 0 else 0
            
            health_status = 'healthy'
            if time_since_heartbeat > self.node_timeout:
                health_status = 'failed'
            elif time_since_heartbeat > self.heartbeat_interval * 2:
                health_status = 'warning'
            elif utilization > 95:
                health_status = 'overloaded'
            
            return {
                'node_id': node_id,
                'status': health_status,
                'last_heartbeat': node.last_heartbeat.isoformat(),
                'time_since_heartbeat': time_since_heartbeat,
                'utilization': utilization,
                'current_load': node.current_load,
                'max_capacity': node.max_capacity,
                'cluster_role': node.cluster_role
            }
            
        except Exception as e:
            logger.error(f"Error monitoring node health {node_id}: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def handle_node_failure(self, failed_node_id: str) -> bool:
        """
        Handle failure of a cluster node
        
        Args:
            failed_node_id: ID of failed node
            
        Returns:
            Success status
        """
        try:
            if not self.is_leader:
                logger.warning("Only leader can handle node failures")
                return False
            
            node = self.nodes.get(failed_node_id)
            if not node:
                return False
            
            logger.warning(f"Handling failure of node {failed_node_id}")
            
            # Mark node as failed
            node.status = 'failed'
            
            # Redistribute jobs from failed node
            self._redistribute_node_jobs(failed_node_id)
            
            # Update cluster configuration
            self.cluster_config.active_nodes -= 1
            
            # Trigger rebalancing
            self._rebalance_cluster_load()
            
            # Log failure event
            self._log_cluster_event(
                'node_failure',
                failed_node_id,
                {'failure_time': datetime.now().isoformat()},
                'error'
            )
            
            # Check if we need to start replacement node
            if self.cluster_config.auto_scaling_enabled:
                self._request_replacement_node(failed_node_id)
            
            logger.info(f"Successfully handled failure of node {failed_node_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling node failure {failed_node_id}: {str(e)}")
            return False
    
    def distribute_cluster_load(self, job: Dict[str, Any], priority_level: str) -> Optional[str]:
        """
        Distribute job across cluster nodes
        
        Args:
            job: Job to distribute
            priority_level: Job priority level
            
        Returns:
            Selected node ID or None
        """
        try:
            # Get eligible nodes for this priority level
            eligible_nodes = [
                node for node in self.nodes.values()
                if (node.status == 'active' and 
                    priority_level in node.capabilities and
                    node.current_load < node.max_capacity)
            ]
            
            if not eligible_nodes:
                logger.warning(f"No eligible nodes for {priority_level} priority job")
                return None
            
            # Select node based on load balancing algorithm
            selected_node = self._select_cluster_node(eligible_nodes, job)
            
            if selected_node:
                # Update node load
                selected_node.current_load += 1
                
                logger.debug(f"Distributed job to node {selected_node.node_id}")
                return selected_node.node_id
            
            return None
            
        except Exception as e:
            logger.error(f"Error distributing cluster load: {str(e)}")
            return None
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """
        Get comprehensive cluster status
        
        Returns:
            Cluster status information
        """
        try:
            active_nodes = [n for n in self.nodes.values() if n.status == 'active']
            total_capacity = sum(node.max_capacity for node in active_nodes)
            total_load = sum(node.current_load for node in active_nodes)
            
            status = {
                'cluster_id': self.cluster_id,
                'cluster_name': self.cluster_config.cluster_name,
                'leader_node_id': self.leader_node_id,
                'is_leader': self.is_leader,
                'total_nodes': len(self.nodes),
                'active_nodes': len(active_nodes),
                'failed_nodes': len([n for n in self.nodes.values() if n.status == 'failed']),
                'cluster_utilization': (total_load / total_capacity * 100) if total_capacity > 0 else 0,
                'total_capacity': total_capacity,
                'total_load': total_load,
                'load_balancing_algorithm': self.load_distribution_strategy,
                'auto_scaling_enabled': self.cluster_config.auto_scaling_enabled,
                'replication_factor': self.cluster_config.replication_factor,
                'nodes': {}
            }
            
            # Add individual node status
            for node_id, node in self.nodes.items():
                status['nodes'][node_id] = {
                    'hostname': node.hostname,
                    'status': node.status,
                    'cluster_role': node.cluster_role,
                    'current_load': node.current_load,
                    'max_capacity': node.max_capacity,
                    'utilization': (node.current_load / node.max_capacity * 100) if node.max_capacity > 0 else 0,
                    'capabilities': node.capabilities,
                    'last_heartbeat': node.last_heartbeat.isoformat()
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting cluster status: {str(e)}")
            return {'error': str(e)}
    
    def _initialize_cluster_config(self) -> ClusterConfiguration:
        """Initialize cluster configuration"""
        return ClusterConfiguration(
            cluster_id=self.cluster_id,
            cluster_name=f"photofish_queue_cluster_{self.cluster_id[:8]}",
            leader_node_id=None,
            total_nodes=0,
            active_nodes=0,
            replication_factor=2,
            load_balancing_algorithm='least_connections',
            failover_enabled=True,
            auto_scaling_enabled=True
        )
    
    def _get_cluster_id(self) -> str:
        """Get or create cluster ID"""
        cluster_id = cache.get('photofish_cluster_id')
        if not cluster_id:
            cluster_id = str(uuid.uuid4())
            cache.set('photofish_cluster_id', cluster_id, timeout=None)
        return cluster_id
    
    def _calculate_node_capacity(self) -> int:
        """Calculate this node's capacity based on system resources"""
        # Simple capacity calculation - could be enhanced with actual system metrics
        import os
        cpu_count = os.cpu_count() or 4
        return min(20, max(5, cpu_count * 2))  # 2 workers per CPU, min 5, max 20
    
    def _get_version(self) -> str:
        """Get system version"""
        return "2.0.0"  # PhotoFish version
    
    def _register_node(self, node_info: ClusterNode) -> bool:
        """Register node with cluster"""
        try:
            cluster_nodes = cache.get(f'cluster_nodes_{self.cluster_id}', {})
            cluster_nodes[node_info.node_id] = asdict(node_info)
            cache.set(f'cluster_nodes_{self.cluster_id}', cluster_nodes, timeout=None)
            
            # Update cluster config
            self.cluster_config.total_nodes = len(cluster_nodes)
            self.cluster_config.active_nodes = len([
                n for n in cluster_nodes.values() 
                if n['status'] == 'active'
            ])
            
            return True
            
        except Exception as e:
            logger.error(f"Error registering node: {str(e)}")
            return False
    
    def _unregister_node(self, node_id: str) -> bool:
        """Unregister node from cluster"""
        try:
            cluster_nodes = cache.get(f'cluster_nodes_{self.cluster_id}', {})
            if node_id in cluster_nodes:
                del cluster_nodes[node_id]
                cache.set(f'cluster_nodes_{self.cluster_id}', cluster_nodes, timeout=None)
            
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering node: {str(e)}")
            return False
    
    def _start_cluster_services(self):
        """Start cluster background services"""
        if not self.is_running:
            self.is_running = True
            
            # Start heartbeat thread
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            self.heartbeat_thread.start()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
    
    def _stop_cluster_services(self):
        """Stop cluster background services"""
        self.is_running = False
        
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=5)
        
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)
    
    def _heartbeat_loop(self):
        """Send periodic heartbeats to cluster"""
        while self.is_running:
            try:
                self._send_heartbeat()
                time.sleep(self.heartbeat_interval)
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {str(e)}")
                time.sleep(5)
    
    def _monitoring_loop(self):
        """Monitor cluster health and perform maintenance"""
        while self.is_running:
            try:
                # Load cluster nodes
                self._load_cluster_nodes()
                
                # Check for leader election needs
                if not self.leader_node_id or self._leader_timeout_expired():
                    self._trigger_leader_election()
                
                # Perform leader duties
                if self.is_leader:
                    self.coordinate_cluster()
                
                time.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(10)
    
    def _send_heartbeat(self):
        """Send heartbeat to cluster"""
        try:
            if self.node_id in self.nodes:
                self.nodes[self.node_id].last_heartbeat = datetime.now()
                
                # Update in cache
                cluster_nodes = cache.get(f'cluster_nodes_{self.cluster_id}', {})
                if self.node_id in cluster_nodes:
                    cluster_nodes[self.node_id]['last_heartbeat'] = datetime.now().isoformat()
                    cache.set(f'cluster_nodes_{self.cluster_id}', cluster_nodes, timeout=None)
                
                # Send leader heartbeat if we're the leader
                if self.is_leader:
                    cache.set(f'cluster_leader_heartbeat_{self.cluster_id}', {
                        'leader_id': self.node_id,
                        'timestamp': datetime.now().isoformat()
                    }, timeout=self.leader_timeout)
                
        except Exception as e:
            logger.error(f"Error sending heartbeat: {str(e)}")
    
    def _load_cluster_nodes(self):
        """Load current cluster nodes from cache"""
        try:
            cluster_nodes = cache.get(f'cluster_nodes_{self.cluster_id}', {})
            
            for node_id, node_data in cluster_nodes.items():
                # Convert back to ClusterNode object
                node_data['last_heartbeat'] = datetime.fromisoformat(node_data['last_heartbeat'])
                self.nodes[node_id] = ClusterNode(**node_data)
            
        except Exception as e:
            logger.error(f"Error loading cluster nodes: {str(e)}")
    
    def _trigger_leader_election(self):
        """Trigger leader election process"""
        if self.election_in_progress:
            return
        
        try:
            self.election_in_progress = True
            logger.info("Starting leader election")
            
            # Simple leader election - node with lowest ID becomes leader
            active_nodes = [
                node_id for node_id, node in self.nodes.items()
                if node.status == 'active' and
                (datetime.now() - node.last_heartbeat).seconds < self.node_timeout
            ]
            
            if active_nodes:
                new_leader = min(active_nodes)
                
                if new_leader == self.node_id:
                    self._become_leader()
                else:
                    self._become_follower(new_leader)
            
        except Exception as e:
            logger.error(f"Error in leader election: {str(e)}")
        finally:
            self.election_in_progress = False
    
    def _become_leader(self):
        """Become cluster leader"""
        self.is_leader = True
        self.leader_node_id = self.node_id
        
        if self.node_id in self.nodes:
            self.nodes[self.node_id].cluster_role = 'leader'
        
        # Update cluster configuration
        self.cluster_config.leader_node_id = self.node_id
        
        self._log_cluster_event(
            'leader_election',
            self.node_id,
            {'action': 'became_leader'},
            'info'
        )
        
        logger.info(f"Node {self.node_id} became cluster leader")
    
    def _become_follower(self, leader_id: str):
        """Become cluster follower"""
        self.is_leader = False
        self.leader_node_id = leader_id
        
        if self.node_id in self.nodes:
            self.nodes[self.node_id].cluster_role = 'follower'
        
        logger.info(f"Node {self.node_id} following leader {leader_id}")
    
    def _leader_timeout_expired(self) -> bool:
        """Check if leader heartbeat has expired"""
        leader_heartbeat = cache.get(f'cluster_leader_heartbeat_{self.cluster_id}')
        
        if not leader_heartbeat:
            return True
        
        last_heartbeat = datetime.fromisoformat(leader_heartbeat['timestamp'])
        return (datetime.now() - last_heartbeat).seconds > self.leader_timeout
    
    def _log_cluster_event(self, event_type: str, node_id: str, details: Dict, severity: str):
        """Log cluster event"""
        event = ClusterEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            node_id=node_id,
            timestamp=datetime.now(),
            details=details,
            severity=severity
        )
        
        self.cluster_events.append(event)
        
        # Keep only recent events
        if len(self.cluster_events) > 1000:
            self.cluster_events = self.cluster_events[-500:]