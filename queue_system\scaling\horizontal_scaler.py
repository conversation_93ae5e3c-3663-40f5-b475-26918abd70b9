# queue_system/scaling/horizontal_scaler.py
"""
Horizontal scaling module for PhotoFish Enhanced Queue System
Manages dynamic worker scaling based on load and performance metrics
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)

@dataclass
class ScalingMetrics:
    """Metrics used for scaling decisions"""
    queue_length: int
    processing_rate: float
    worker_utilization: float
    memory_usage: float
    cpu_usage: float
    error_rate: float
    timestamp: datetime

@dataclass
class ScalingEvent:
    """Record of scaling actions"""
    event_type: str  # 'scale_out', 'scale_in'
    worker_count_before: int
    worker_count_after: int
    reason: str
    timestamp: datetime
    success: bool

class HorizontalScaler:
    """
    Manages horizontal scaling of queue workers based on real-time metrics
    """
    
    def __init__(self):
        self.scaling_config = getattr(settings, 'ENHANCED_QUEUE_SETTINGS', {}).get('WORKER_POOLS', {})
        self.scaling_history_key = "queue_scaling_history"
        self.scaling_lock = threading.Lock()
        self.last_scaling_time = {}
        self.cooldown_period = 300  # 5 minutes between scaling events
        
        # Scaling thresholds
        self.scale_out_thresholds = {
            'queue_length': 50,
            'worker_utilization': 85.0,
            'processing_rate_drop': 30.0,  # % drop in processing rate
        }
        
        self.scale_in_thresholds = {
            'queue_length': 10,
            'worker_utilization': 30.0,
            'idle_time': 600,  # 10 minutes of low utilization
        }
    
    def scale_out(self, priority: str, target_workers: int, reason: str) -> bool:
        """
        Scale out workers for a specific priority queue
        
        Args:
            priority: Priority level (HIGH, STANDARD, LOW, etc.)
            target_workers: Target number of workers
            reason: Reason for scaling
            
        Returns:
            Success status
        """
        try:
            with self.scaling_lock:
                current_config = self.scaling_config.get(priority, {})
                current_workers = current_config.get('current_workers', current_config.get('min_workers', 1))
                max_workers = current_config.get('max_workers', 10)
                
                # Validate scaling request
                if target_workers <= current_workers:
                    logger.info(f"Scale out not needed for {priority}: target={target_workers}, current={current_workers}")
                    return True
                
                if target_workers > max_workers:
                    target_workers = max_workers
                    logger.warning(f"Scale out limited to max workers for {priority}: {max_workers}")
                
                # Check cooldown period
                last_scaling = self.last_scaling_time.get(priority, datetime.min)
                if datetime.now() - last_scaling < timedelta(seconds=self.cooldown_period):
                    logger.info(f"Scale out blocked by cooldown for {priority}")
                    return False
                
                # Execute scaling
                success = self._execute_scale_out(priority, current_workers, target_workers, reason)
                
                if success:
                    # Update configuration
                    self.scaling_config[priority]['current_workers'] = target_workers
                    self.last_scaling_time[priority] = datetime.now()
                    
                    # Record scaling event
                    self._record_scaling_event(
                        'scale_out', current_workers, target_workers, reason, success
                    )
                    
                    logger.info(f"Successfully scaled out {priority} from {current_workers} to {target_workers} workers")
                else:
                    logger.error(f"Failed to scale out {priority} workers")
                
                return success
                
        except Exception as e:
            logger.error(f"Error during scale out for {priority}: {str(e)}")
            return False
    
    def scale_in(self, priority: str, target_workers: int, reason: str) -> bool:
        """
        Scale in workers for a specific priority queue
        
        Args:
            priority: Priority level
            target_workers: Target number of workers
            reason: Reason for scaling
            
        Returns:
            Success status
        """
        try:
            with self.scaling_lock:
                current_config = self.scaling_config.get(priority, {})
                current_workers = current_config.get('current_workers', current_config.get('min_workers', 1))
                min_workers = current_config.get('min_workers', 1)
                
                # Validate scaling request
                if target_workers >= current_workers:
                    logger.info(f"Scale in not needed for {priority}: target={target_workers}, current={current_workers}")
                    return True
                
                if target_workers < min_workers:
                    target_workers = min_workers
                    logger.warning(f"Scale in limited to min workers for {priority}: {min_workers}")
                
                # Check cooldown period
                last_scaling = self.last_scaling_time.get(priority, datetime.min)
                if datetime.now() - last_scaling < timedelta(seconds=self.cooldown_period):
                    logger.info(f"Scale in blocked by cooldown for {priority}")
                    return False
                
                # Execute scaling
                success = self._execute_scale_in(priority, current_workers, target_workers, reason)
                
                if success:
                    # Update configuration
                    self.scaling_config[priority]['current_workers'] = target_workers
                    self.last_scaling_time[priority] = datetime.now()
                    
                    # Record scaling event
                    self._record_scaling_event(
                        'scale_in', current_workers, target_workers, reason, success
                    )
                    
                    logger.info(f"Successfully scaled in {priority} from {current_workers} to {target_workers} workers")
                else:
                    logger.error(f"Failed to scale in {priority} workers")
                
                return success
                
        except Exception as e:
            logger.error(f"Error during scale in for {priority}: {str(e)}")
            return False
    
    def calculate_optimal_workers(self, priority: str, metrics: ScalingMetrics) -> int:
        """
        Calculate optimal number of workers based on current metrics
        
        Args:
            priority: Priority level
            metrics: Current system metrics
            
        Returns:
            Optimal number of workers
        """
        try:
            current_config = self.scaling_config.get(priority, {})
            current_workers = current_config.get('current_workers', current_config.get('min_workers', 1))
            min_workers = current_config.get('min_workers', 1)
            max_workers = current_config.get('max_workers', 10)
            
            # Base calculation on queue length and processing rate
            target_processing_time = 60  # Target: process queue in 1 minute
            estimated_workers_needed = max(1, metrics.queue_length / target_processing_time)
            
            # Adjust based on worker utilization
            if metrics.worker_utilization > 80:
                utilization_factor = 1.2  # Scale up
            elif metrics.worker_utilization < 40:
                utilization_factor = 0.8  # Scale down
            else:
                utilization_factor = 1.0  # Maintain
            
            estimated_workers_needed *= utilization_factor
            
            # Adjust based on error rate
            if metrics.error_rate > 5.0:  # High error rate
                estimated_workers_needed *= 1.1  # Slight increase for redundancy
            
            # Round and constrain to limits
            optimal_workers = max(min_workers, min(max_workers, round(estimated_workers_needed)))
            
            logger.debug(f"Calculated optimal workers for {priority}: {optimal_workers} "
                        f"(queue_length={metrics.queue_length}, utilization={metrics.worker_utilization}%)")
            
            return optimal_workers
            
        except Exception as e:
            logger.error(f"Error calculating optimal workers for {priority}: {str(e)}")
            return current_config.get('current_workers', min_workers)
    
    def should_scale_out(self, priority: str, metrics: ScalingMetrics) -> bool:
        """
        Determine if scaling out is needed
        
        Args:
            priority: Priority level
            metrics: Current metrics
            
        Returns:
            True if should scale out
        """
        try:
            # Check queue length threshold
            if metrics.queue_length > self.scale_out_thresholds['queue_length']:
                return True
            
            # Check worker utilization
            if metrics.worker_utilization > self.scale_out_thresholds['worker_utilization']:
                return True
            
            # Check processing rate drop
            historical_rate = self._get_historical_processing_rate(priority)
            if historical_rate and metrics.processing_rate < (historical_rate * 0.7):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error determining scale out for {priority}: {str(e)}")
            return False
    
    def should_scale_in(self, priority: str, metrics: ScalingMetrics) -> bool:
        """
        Determine if scaling in is appropriate
        
        Args:
            priority: Priority level
            metrics: Current metrics
            
        Returns:
            True if should scale in
        """
        try:
            # Don't scale in if queue is growing
            if metrics.queue_length > self.scale_in_thresholds['queue_length']:
                return False
            
            # Check if workers are underutilized
            if metrics.worker_utilization < self.scale_in_thresholds['worker_utilization']:
                # Check if this has been consistent
                if self._check_sustained_low_utilization(priority, 300):  # 5 minutes
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error determining scale in for {priority}: {str(e)}")
            return False
    
    def monitor_scaling_events(self) -> List[ScalingEvent]:
        """
        Get recent scaling events for monitoring
        
        Returns:
            List of recent scaling events
        """
        try:
            history = cache.get(self.scaling_history_key, [])
            
            # Return events from last 24 hours
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_events = [
                event for event in history 
                if event.timestamp > cutoff_time
            ]
            
            return recent_events
            
        except Exception as e:
            logger.error(f"Error retrieving scaling events: {str(e)}")
            return []
    
    def get_scaling_recommendations(self, metrics_by_priority: Dict[str, ScalingMetrics]) -> Dict[str, Dict]:
        """
        Get scaling recommendations for all priority levels
        
        Args:
            metrics_by_priority: Metrics for each priority level
            
        Returns:
            Scaling recommendations
        """
        recommendations = {}
        
        try:
            for priority, metrics in metrics_by_priority.items():
                current_workers = self.scaling_config.get(priority, {}).get('current_workers', 1)
                optimal_workers = self.calculate_optimal_workers(priority, metrics)
                
                recommendation = {
                    'current_workers': current_workers,
                    'optimal_workers': optimal_workers,
                    'action': 'maintain',
                    'reason': 'Current scaling is optimal',
                    'confidence': 'high'
                }
                
                if optimal_workers > current_workers:
                    if self.should_scale_out(priority, metrics):
                        recommendation.update({
                            'action': 'scale_out',
                            'reason': f'High load detected (queue: {metrics.queue_length}, utilization: {metrics.worker_utilization}%)',
                            'confidence': 'high' if metrics.queue_length > 20 else 'medium'
                        })
                
                elif optimal_workers < current_workers:
                    if self.should_scale_in(priority, metrics):
                        recommendation.update({
                            'action': 'scale_in',
                            'reason': f'Low utilization detected ({metrics.worker_utilization}%)',
                            'confidence': 'medium'  # Always medium for scale in
                        })
                
                recommendations[priority] = recommendation
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating scaling recommendations: {str(e)}")
            return {}
    
    def _execute_scale_out(self, priority: str, current_workers: int, target_workers: int, reason: str) -> bool:
        """Execute the actual scale out operation"""
        try:
            # This would interface with the worker pool to add workers
            from ..queue_engine.worker_pool import WorkerPool
            
            worker_pool = WorkerPool()
            additional_workers = target_workers - current_workers
            
            success = worker_pool.add_workers(priority, additional_workers)
            
            if success:
                logger.info(f"Added {additional_workers} workers to {priority} pool")
            
            return success
            
        except Exception as e:
            logger.error(f"Error executing scale out: {str(e)}")
            return False
    
    def _execute_scale_in(self, priority: str, current_workers: int, target_workers: int, reason: str) -> bool:
        """Execute the actual scale in operation"""
        try:
            # This would interface with the worker pool to remove workers
            from ..queue_engine.worker_pool import WorkerPool
            
            worker_pool = WorkerPool()
            workers_to_remove = current_workers - target_workers
            
            success = worker_pool.remove_workers(priority, workers_to_remove)
            
            if success:
                logger.info(f"Removed {workers_to_remove} workers from {priority} pool")
            
            return success
            
        except Exception as e:
            logger.error(f"Error executing scale in: {str(e)}")
            return False
    
    def _record_scaling_event(self, event_type: str, before: int, after: int, reason: str, success: bool):
        """Record scaling event for monitoring"""
        try:
            event = ScalingEvent(
                event_type=event_type,
                worker_count_before=before,
                worker_count_after=after,
                reason=reason,
                timestamp=datetime.now(),
                success=success
            )
            
            history = cache.get(self.scaling_history_key, [])
            history.append(event)
            
            # Keep only last 100 events
            if len(history) > 100:
                history = history[-100:]
            
            cache.set(self.scaling_history_key, history, timeout=86400)  # 24 hours
            
        except Exception as e:
            logger.error(f"Error recording scaling event: {str(e)}")
    
    def _get_historical_processing_rate(self, priority: str) -> Optional[float]:
        """Get historical processing rate for comparison"""
        try:
            # This would retrieve historical data from metrics
            from ..monitoring.metrics_collector import MetricsCollector
            
            metrics_collector = MetricsCollector()
            return metrics_collector.get_average_processing_rate(priority, hours=24)
            
        except Exception as e:
            logger.error(f"Error getting historical processing rate: {str(e)}")
            return None
    
    def _check_sustained_low_utilization(self, priority: str, duration_seconds: int) -> bool:
        """Check if low utilization has been sustained for given duration"""
        try:
            # This would check metrics history
            from ..monitoring.metrics_collector import MetricsCollector
            
            metrics_collector = MetricsCollector()
            return metrics_collector.check_sustained_low_utilization(priority, duration_seconds)
            
        except Exception as e:
            logger.error(f"Error checking sustained low utilization: {str(e)}")
            return False