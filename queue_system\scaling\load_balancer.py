# queue_system/scaling/load_balancer.py
"""
Load balancer for PhotoFish Enhanced Queue System
Distributes workload intelligently across available workers
"""

import logging
import threading
import time
import random
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

@dataclass
class WorkerInfo:
    """Information about a worker"""
    worker_id: str
    priority_level: str
    current_load: int
    max_capacity: int
    last_heartbeat: datetime
    processing_time_avg: float
    error_count: int
    status: str  # 'active', 'busy', 'idle', 'error'

@dataclass
class LoadBalancingRule:
    """Load balancing configuration rule"""
    algorithm: str  # 'round_robin', 'least_connections', 'weighted', 'fastest_response'
    priority_level: str
    weight_factor: float
    max_queue_per_worker: int
    health_check_interval: int

class LoadBalancer:
    """
    Intelligent load balancer for distributing jobs across workers
    Supports multiple algorithms and real-time optimization
    """
    
    def __init__(self):
        self.workers = {}  # worker_id -> WorkerInfo
        self.worker_queues = defaultdict(list)  # worker_id -> job queue
        self.load_balancing_rules = self._initialize_rules()
        self.worker_stats = defaultdict(lambda: defaultdict(int))
        self.round_robin_counters = defaultdict(int)
        self._lock = threading.Lock()
        
        # Performance tracking
        self.job_assignments = []
        self.performance_window = 300  # 5 minutes
    
    def _initialize_rules(self) -> Dict[str, LoadBalancingRule]:
        """Initialize load balancing rules for different priority levels"""
        rules = {}
        
        priority_configs = {
            'EMERGENCY': {
                'algorithm': 'fastest_response',
                'weight_factor': 1.0,
                'max_queue_per_worker': 2,
                'health_check_interval': 10
            },
            'HIGH': {
                'algorithm': 'least_connections',
                'weight_factor': 0.9,
                'max_queue_per_worker': 5,
                'health_check_interval': 30
            },
            'STANDARD': {
                'algorithm': 'weighted',
                'weight_factor': 0.7,
                'max_queue_per_worker': 10,
                'health_check_interval': 60
            },
            'LOW': {
                'algorithm': 'round_robin',
                'weight_factor': 0.5,
                'max_queue_per_worker': 20,
                'health_check_interval': 120
            },
            'MAINTENANCE': {
                'algorithm': 'round_robin',
                'weight_factor': 0.3,
                'max_queue_per_worker': 5,
                'health_check_interval': 300
            }
        }
        
        for priority, config in priority_configs.items():
            rules[priority] = LoadBalancingRule(
                algorithm=config['algorithm'],
                priority_level=priority,
                weight_factor=config['weight_factor'],
                max_queue_per_worker=config['max_queue_per_worker'],
                health_check_interval=config['health_check_interval']
            )
        
        return rules
    
    def register_worker(self, worker_id: str, priority_level: str, max_capacity: int = 10) -> bool:
        """
        Register a new worker with the load balancer
        
        Args:
            worker_id: Unique worker identifier
            priority_level: Priority level this worker handles
            max_capacity: Maximum jobs this worker can handle
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                worker_info = WorkerInfo(
                    worker_id=worker_id,
                    priority_level=priority_level,
                    current_load=0,
                    max_capacity=max_capacity,
                    last_heartbeat=datetime.now(),
                    processing_time_avg=0.0,
                    error_count=0,
                    status='idle'
                )
                
                self.workers[worker_id] = worker_info
                logger.info(f"Registered worker {worker_id} for {priority_level} priority")
                return True
                
        except Exception as e:
            logger.error(f"Error registering worker {worker_id}: {str(e)}")
            return False
    
    def unregister_worker(self, worker_id: str) -> bool:
        """
        Unregister a worker from the load balancer
        
        Args:
            worker_id: Worker to unregister
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                if worker_id in self.workers:
                    # Redistribute any pending jobs
                    pending_jobs = self.worker_queues.get(worker_id, [])
                    if pending_jobs:
                        priority_level = self.workers[worker_id].priority_level
                        for job in pending_jobs:
                            self.assign_job(job, priority_level)
                    
                    # Clean up worker data
                    del self.workers[worker_id]
                    if worker_id in self.worker_queues:
                        del self.worker_queues[worker_id]
                    
                    logger.info(f"Unregistered worker {worker_id}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Error unregistering worker {worker_id}: {str(e)}")
            return False
    
    def assign_job(self, job: Dict[str, Any], priority_level: str) -> Optional[str]:
        """
        Assign a job to the best available worker
        
        Args:
            job: Job data to assign
            priority_level: Priority level for the job
            
        Returns:
            Worker ID that received the job, or None if no worker available
        """
        try:
            with self._lock:
                # Get available workers for this priority
                available_workers = self._get_available_workers(priority_level)
                
                if not available_workers:
                    logger.warning(f"No available workers for {priority_level} priority")
                    return None
                
                # Select worker based on load balancing algorithm
                rule = self.load_balancing_rules.get(priority_level)
                if not rule:
                    logger.error(f"No load balancing rule for {priority_level}")
                    return None
                
                selected_worker = self._select_worker(available_workers, rule, job)
                
                if selected_worker:
                    # Assign job to worker
                    self.worker_queues[selected_worker].append(job)
                    self.workers[selected_worker].current_load += 1
                    
                    # Update worker status
                    if self.workers[selected_worker].current_load >= self.workers[selected_worker].max_capacity:
                        self.workers[selected_worker].status = 'busy'
                    else:
                        self.workers[selected_worker].status = 'active'
                    
                    # Track assignment
                    self._track_job_assignment(selected_worker, priority_level, job)
                    
                    logger.debug(f"Assigned job to worker {selected_worker} ({priority_level})")
                    return selected_worker
                
                return None
                
        except Exception as e:
            logger.error(f"Error assigning job: {str(e)}")
            return None
    
    def report_job_completion(self, worker_id: str, job_id: str, processing_time: float, success: bool) -> bool:
        """
        Report job completion from a worker
        
        Args:
            worker_id: Worker that completed the job
            job_id: ID of completed job
            processing_time: Time taken to process
            success: Whether job completed successfully
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                if worker_id not in self.workers:
                    logger.warning(f"Unknown worker reported completion: {worker_id}")
                    return False
                
                worker = self.workers[worker_id]
                
                # Update worker load
                worker.current_load = max(0, worker.current_load - 1)
                
                # Update processing time average
                if worker.processing_time_avg == 0:
                    worker.processing_time_avg = processing_time
                else:
                    # Moving average
                    worker.processing_time_avg = (worker.processing_time_avg * 0.9) + (processing_time * 0.1)
                
                # Update error count
                if not success:
                    worker.error_count += 1
                
                # Update status
                if worker.current_load == 0:
                    worker.status = 'idle'
                elif worker.current_load < worker.max_capacity:
                    worker.status = 'active'
                
                # Update heartbeat
                worker.last_heartbeat = datetime.now()
                
                # Track performance
                self._track_job_completion(worker_id, processing_time, success)
                
                logger.debug(f"Worker {worker_id} completed job {job_id} in {processing_time:.2f}s")
                return True
                
        except Exception as e:
            logger.error(f"Error reporting job completion: {str(e)}")
            return False
    
    def get_load_distribution(self) -> Dict[str, Dict]:
        """
        Get current load distribution across workers
        
        Returns:
            Load distribution information
        """
        try:
            distribution = {}
            
            with self._lock:
                for worker_id, worker in self.workers.items():
                    distribution[worker_id] = {
                        'priority_level': worker.priority_level,
                        'current_load': worker.current_load,
                        'max_capacity': worker.max_capacity,
                        'utilization_percentage': (worker.current_load / worker.max_capacity) * 100,
                        'status': worker.status,
                        'avg_processing_time': worker.processing_time_avg,
                        'error_count': worker.error_count,
                        'last_heartbeat': worker.last_heartbeat.isoformat()
                    }
            
            return distribution
            
        except Exception as e:
            logger.error(f"Error getting load distribution: {str(e)}")
            return {}
    
    def rebalance_workload(self, priority_level: Optional[str] = None) -> bool:
        """
        Rebalance workload across workers
        
        Args:
            priority_level: Specific priority to rebalance, or None for all
            
        Returns:
            Success status
        """
        try:
            with self._lock:
                if priority_level:
                    priorities_to_balance = [priority_level]
                else:
                    priorities_to_balance = list(self.load_balancing_rules.keys())
                
                for priority in priorities_to_balance:
                    workers = self._get_workers_by_priority(priority)
                    if len(workers) < 2:
                        continue  # Need at least 2 workers to rebalance
                    
                    # Calculate target load per worker
                    total_jobs = sum(worker.current_load for worker in workers)
                    target_load = total_jobs // len(workers)
                    
                    # Identify overloaded and underloaded workers
                    overloaded = [w for w in workers if w.current_load > target_load + 1]
                    underloaded = [w for w in workers if w.current_load < target_load]
                    
                    # Redistribute jobs
                    for overloaded_worker in overloaded:
                        excess_jobs = overloaded_worker.current_load - target_load
                        jobs_to_move = self.worker_queues[overloaded_worker.worker_id][:excess_jobs]
                        
                        for job in jobs_to_move:
                            if underloaded:
                                target_worker = underloaded[0]
                                
                                # Move job
                                self.worker_queues[overloaded_worker.worker_id].remove(job)
                                self.worker_queues[target_worker.worker_id].append(job)
                                
                                # Update loads
                                overloaded_worker.current_load -= 1
                                target_worker.current_load += 1
                                
                                # Remove from underloaded if now at target
                                if target_worker.current_load >= target_load:
                                    underloaded.remove(target_worker)
                
                logger.info(f"Rebalanced workload for {len(priorities_to_balance)} priority levels")
                return True
                
        except Exception as e:
            logger.error(f"Error rebalancing workload: {str(e)}")
            return False
    
    def get_worker_recommendations(self) -> Dict[str, str]:
        """
        Get recommendations for worker optimization
        
        Returns:
            Worker optimization recommendations
        """
        recommendations = {}
        
        try:
            with self._lock:
                for worker_id, worker in self.workers.items():
                    # Check utilization
                    utilization = (worker.current_load / worker.max_capacity) * 100
                    
                    if utilization > 90:
                        recommendations[worker_id] = "Consider increasing worker capacity or adding more workers"
                    elif utilization < 20 and worker.current_load > 0:
                        recommendations[worker_id] = "Worker underutilized, consider reducing capacity"
                    elif worker.error_count > 5:
                        recommendations[worker_id] = "High error rate detected, investigate worker health"
                    elif (datetime.now() - worker.last_heartbeat).seconds > 300:
                        recommendations[worker_id] = "Worker appears unresponsive, check connectivity"
                    else:
                        recommendations[worker_id] = "Worker performing optimally"
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating worker recommendations: {str(e)}")
            return {}
    
    def _get_available_workers(self, priority_level: str) -> List[WorkerInfo]:
        """Get available workers for a priority level"""
        return [
            worker for worker in self.workers.values()
            if (worker.priority_level == priority_level and 
                worker.status in ['idle', 'active'] and
                worker.current_load < worker.max_capacity and
                (datetime.now() - worker.last_heartbeat).seconds < 300)
        ]
    
    def _get_workers_by_priority(self, priority_level: str) -> List[WorkerInfo]:
        """Get all workers for a priority level"""
        return [
            worker for worker in self.workers.values()
            if worker.priority_level == priority_level
        ]
    
    def _select_worker(self, available_workers: List[WorkerInfo], rule: LoadBalancingRule, job: Dict) -> Optional[str]:
        """Select worker based on load balancing algorithm"""
        if not available_workers:
            return None
        
        if rule.algorithm == 'round_robin':
            return self._round_robin_selection(available_workers, rule.priority_level)
        elif rule.algorithm == 'least_connections':
            return self._least_connections_selection(available_workers)
        elif rule.algorithm == 'weighted':
            return self._weighted_selection(available_workers, rule.weight_factor)
        elif rule.algorithm == 'fastest_response':
            return self._fastest_response_selection(available_workers)
        else:
            # Default to round robin
            return self._round_robin_selection(available_workers, rule.priority_level)
    
    def _round_robin_selection(self, workers: List[WorkerInfo], priority_level: str) -> str:
        """Round robin worker selection"""
        counter = self.round_robin_counters[priority_level]
        selected_worker = workers[counter % len(workers)]
        self.round_robin_counters[priority_level] = (counter + 1) % len(workers)
        return selected_worker.worker_id
    
    def _least_connections_selection(self, workers: List[WorkerInfo]) -> str:
        """Select worker with least current load"""
        return min(workers, key=lambda w: w.current_load).worker_id
    
    def _weighted_selection(self, workers: List[WorkerInfo], weight_factor: float) -> str:
        """Select worker based on weighted capacity"""
        # Calculate weights based on available capacity and performance
        weights = []
        for worker in workers:
            available_capacity = worker.max_capacity - worker.current_load
            performance_factor = 1.0 / max(worker.processing_time_avg, 0.1)  # Avoid division by zero
            weight = available_capacity * performance_factor * weight_factor
            weights.append(weight)
        
        # Weighted random selection
        total_weight = sum(weights)
        if total_weight == 0:
            return random.choice(workers).worker_id
        
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        for i, weight in enumerate(weights):
            cumulative_weight += weight
            if r <= cumulative_weight:
                return workers[i].worker_id
        
        return workers[-1].worker_id
    
    def _fastest_response_selection(self, workers: List[WorkerInfo]) -> str:
        """Select worker with fastest average response time"""
        # Filter out workers with no processing history
        workers_with_history = [w for w in workers if w.processing_time_avg > 0]
        if not workers_with_history:
            return min(workers, key=lambda w: w.current_load).worker_id
        
        return min(workers_with_history, key=lambda w: w.processing_time_avg).worker_id
    
    def _track_job_assignment(self, worker_id: str, priority_level: str, job: Dict):
        """Track job assignment for performance analysis"""
        assignment = {
            'worker_id': worker_id,
            'priority_level': priority_level,
            'job_id': job.get('id'),
            'timestamp': datetime.now(),
            'job_type': job.get('type')
        }
        
        self.job_assignments.append(assignment)
        
        # Keep only recent assignments
        cutoff_time = datetime.now() - timedelta(seconds=self.performance_window)
        self.job_assignments = [
            a for a in self.job_assignments 
            if a['timestamp'] > cutoff_time
        ]
    
    def _track_job_completion(self, worker_id: str, processing_time: float, success: bool):
        """Track job completion for performance analysis"""
        # Update worker statistics
        self.worker_stats[worker_id]['total_jobs'] += 1
        self.worker_stats[worker_id]['total_time'] += processing_time
        
        if success:
            self.worker_stats[worker_id]['successful_jobs'] += 1
        else:
            self.worker_stats[worker_id]['failed_jobs'] += 1