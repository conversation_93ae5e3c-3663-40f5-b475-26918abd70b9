# queue_system/serializers.py
"""
DRF serializers for PhotoFish Enhanced Queue System
API serializers for queue jobs, metrics, and monitoring data
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from .models import QueueJob, QueueMetrics, WorkerStatus, ErrorLog, QueueConfiguration, JobDependency


class QueueJobSerializer(serializers.ModelSerializer):
    """
    Serializer for QueueJob model with comprehensive job information
    """

    processing_time = serializers.ReadOnlyField()
    # Extract user_id and other fields from metadata
    user_id = serializers.SerializerMethodField()
    progress_percentage = serializers.SerializerMethodField()
    estimated_duration = serializers.SerializerMethodField()
    options = serializers.SerializerMethodField()

    class Meta:
        model = QueueJob
        fields = [
            'id', 'job_type', 'priority', 'status',
            'job_data', 'result_data', 'user_id', 'options',
            'worker_id', 'progress_percentage', 'retry_count', 'max_retries',
            'error_message', 'created_at', 'updated_at', 'started_at',
            'completed_at', 'estimated_duration', 'timeout_seconds',
            'processing_time', 'metadata'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'processing_time'
        ]

    def get_user_id(self, obj):
        """Extract user_id from metadata"""
        return obj.metadata.get('user_id') if obj.metadata else None

    def get_progress_percentage(self, obj):
        """Extract progress_percentage from metadata"""
        return obj.metadata.get('progress_percentage', 0.0) if obj.metadata else 0.0

    def get_estimated_duration(self, obj):
        """Extract estimated_duration from metadata"""
        return obj.metadata.get('estimated_duration') if obj.metadata else None

    def get_options(self, obj):
        """Extract options from metadata"""
        return obj.metadata.get('options', {}) if obj.metadata else {}


class JobSubmissionSerializer(serializers.Serializer):
    """
    Serializer for job submission requests
    """
    
    job_type = serializers.ChoiceField(choices=QueueJob.JOB_TYPES)
    priority = serializers.ChoiceField(
        choices=QueueJob.PRIORITY_LEVELS,
        default='STANDARD'
    )
    job_data = serializers.JSONField()
    options = serializers.JSONField(required=False, default=dict)
    max_retries = serializers.IntegerField(min_value=0, max_value=10, default=3)
    estimated_duration = serializers.IntegerField(required=False, allow_null=True)

    def validate_job_data(self, value):
        """Validate job data based on job type"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("Job data must be a JSON object")

        # Add job-type specific validation here
        return value


class JobUpdateSerializer(serializers.Serializer):
    """
    Serializer for job status and progress updates
    """
    
    status = serializers.ChoiceField(
        choices=QueueJob.STATUS_CHOICES,
        required=False
    )
    progress_percentage = serializers.FloatField(
        min_value=0.0,
        max_value=100.0,
        required=False
    )
    result = serializers.JSONField(required=False)
    error_message = serializers.CharField(required=False, allow_blank=True)
    worker_id = serializers.CharField(required=False, allow_blank=True)


class QueueStatusSerializer(serializers.Serializer):
    """
    Serializer for overall queue status information
    """
    
    total_jobs = serializers.IntegerField()
    pending_jobs = serializers.IntegerField()
    processing_jobs = serializers.IntegerField()
    completed_jobs = serializers.IntegerField()
    failed_jobs = serializers.IntegerField()
    
    jobs_by_priority = serializers.DictField()
    jobs_by_type = serializers.DictField()
    
    average_queue_time = serializers.FloatField()
    average_processing_time = serializers.FloatField()
    
    active_workers = serializers.IntegerField()
    total_workers = serializers.IntegerField()
    worker_utilization = serializers.FloatField()
    
    system_health = serializers.CharField()
    last_updated = serializers.DateTimeField()


class QueueMetricsSerializer(serializers.ModelSerializer):
    """
    Serializer for QueueMetrics model
    """
    
    class Meta:
        model = QueueMetrics
        fields = [
            'id', 'metric_id', 'metric_type', 'priority_level',
            'value', 'unit', 'metadata', 'source', 'timestamp'
        ]
        read_only_fields = ['id', 'metric_id', 'timestamp']


class MetricsAggregationSerializer(serializers.Serializer):
    """
    Serializer for aggregated metrics data
    """
    
    metric_type = serializers.CharField()
    time_period = serializers.CharField()
    
    average = serializers.FloatField()
    minimum = serializers.FloatField()
    maximum = serializers.FloatField()
    total = serializers.FloatField()
    count = serializers.IntegerField()
    
    data_points = serializers.ListField(
        child=serializers.DictField(),
        required=False
    )


class WorkerStatusSerializer(serializers.ModelSerializer):
    """
    Serializer for WorkerStatus model
    """
    
    utilization_percentage = serializers.ReadOnlyField()
    success_rate = serializers.ReadOnlyField()
    is_healthy = serializers.ReadOnlyField()
    
    class Meta:
        model = WorkerStatus
        fields = [
            'id', 'worker_id', 'hostname', 'process_id', 'status',
            'priority_levels', 'current_load', 'max_capacity',
            'jobs_processed', 'jobs_failed', 'average_processing_time',
            'last_heartbeat', 'memory_usage_mb', 'cpu_usage_percent',
            'worker_version', 'configuration', 'started_at', 'updated_at',
            'utilization_percentage', 'success_rate', 'is_healthy'
        ]
        read_only_fields = [
            'id', 'started_at', 'updated_at', 'utilization_percentage',
            'success_rate', 'is_healthy'
        ]


class WorkerRegistrationSerializer(serializers.Serializer):
    """
    Serializer for worker registration
    """
    
    worker_id = serializers.CharField(max_length=100)
    hostname = serializers.CharField(max_length=255)
    process_id = serializers.IntegerField()
    priority_levels = serializers.ListField(
        child=serializers.ChoiceField(choices=QueueJob.PRIORITY_LEVELS)
    )
    max_capacity = serializers.IntegerField(min_value=1, max_value=100, default=10)
    worker_version = serializers.CharField(max_length=50, default='1.0.0')
    configuration = serializers.JSONField(default=dict)


class ErrorLogSerializer(serializers.ModelSerializer):
    """
    Serializer for ErrorLog model
    """
    
    job_id = serializers.CharField(source='job.job_id', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    resolved_by_username = serializers.CharField(source='resolved_by.username', read_only=True)
    
    class Meta:
        model = ErrorLog
        fields = [
            'id', 'error_id', 'error_type', 'severity', 'title', 'description',
            'stack_trace', 'job', 'job_id', 'worker_id', 'user', 'user_username',
            'error_data', 'environment_info', 'resolved', 'resolution_notes',
            'resolved_at', 'resolved_by', 'resolved_by_username', 'timestamp',
            'first_occurrence', 'last_occurrence', 'occurrence_count'
        ]
        read_only_fields = [
            'id', 'error_id', 'timestamp', 'first_occurrence', 'job_id',
            'user_username', 'resolved_by_username'
        ]


class ErrorSummarySerializer(serializers.Serializer):
    """
    Serializer for error summary statistics
    """
    
    total_errors = serializers.IntegerField()
    unresolved_errors = serializers.IntegerField()
    critical_errors = serializers.IntegerField()
    
    errors_by_type = serializers.DictField()
    errors_by_severity = serializers.DictField()
    errors_by_worker = serializers.DictField()
    
    error_rate_percentage = serializers.FloatField()
    most_common_errors = serializers.ListField(
        child=serializers.DictField()
    )


class QueueConfigurationSerializer(serializers.ModelSerializer):
    """
    Serializer for QueueConfiguration model
    """
    
    updated_by_username = serializers.CharField(source='updated_by.username', read_only=True)
    
    class Meta:
        model = QueueConfiguration
        fields = [
            'id', 'config_key', 'config_type', 'value', 'description',
            'is_active', 'is_system', 'created_at', 'updated_at',
            'updated_by', 'updated_by_username'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'updated_by_username'
        ]
    
    def validate_config_key(self, value):
        """Validate configuration key format"""
        if not value.replace('_', '').replace('-', '').isalnum():
            raise serializers.ValidationError(
                "Configuration key must contain only letters, numbers, underscores, and hyphens"
            )
        return value


class JobDependencySerializer(serializers.ModelSerializer):
    """
    Serializer for JobDependency model
    """
    
    parent_job_id = serializers.CharField(source='parent_job.job_id', read_only=True)
    child_job_id = serializers.CharField(source='child_job.job_id', read_only=True)
    parent_job_status = serializers.CharField(source='parent_job.status', read_only=True)
    child_job_status = serializers.CharField(source='child_job.status', read_only=True)
    
    class Meta:
        model = JobDependency
        fields = [
            'id', 'parent_job', 'child_job', 'parent_job_id', 'child_job_id',
            'parent_job_status', 'child_job_status', 'dependency_type',
            'condition', 'is_satisfied', 'satisfied_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'parent_job_id', 'child_job_id', 'parent_job_status',
            'child_job_status', 'created_at'
        ]


class BulkJobSubmissionSerializer(serializers.Serializer):
    """
    Serializer for bulk job submission
    """
    
    jobs = serializers.ListField(
        child=JobSubmissionSerializer(),
        min_length=1,
        max_length=100
    )
    batch_name = serializers.CharField(max_length=255, required=False)
    priority_override = serializers.ChoiceField(
        choices=QueueJob.PRIORITY_LEVELS,
        required=False
    )
    
    def validate_jobs(self, value):
        """Validate bulk job submission"""
        if len(value) > 100:
            raise serializers.ValidationError(
                "Cannot submit more than 100 jobs in a single batch"
            )
        return value


class PerformanceReportSerializer(serializers.Serializer):
    """
    Serializer for performance report data
    """
    
    report_period = serializers.CharField()
    generated_at = serializers.DateTimeField()
    
    job_statistics = serializers.DictField()
    worker_statistics = serializers.DictField()
    performance_metrics = serializers.DictField()
    error_analysis = serializers.DictField()
    recommendations = serializers.ListField(
        child=serializers.DictField()
    )


class HealthCheckSerializer(serializers.Serializer):
    """
    Serializer for system health check results
    """
    
    overall_status = serializers.CharField()
    timestamp = serializers.DateTimeField()
    
    queue_health = serializers.DictField()
    worker_health = serializers.DictField()
    database_health = serializers.DictField()
    external_services_health = serializers.DictField()
    
    issues = serializers.ListField(
        child=serializers.DictField(),
        required=False
    )
    recommendations = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )


class ScalingRecommendationSerializer(serializers.Serializer):
    """
    Serializer for scaling recommendations
    """
    
    priority_level = serializers.CharField()
    current_workers = serializers.IntegerField()
    recommended_workers = serializers.IntegerField()
    action = serializers.ChoiceField(choices=['scale_up', 'scale_down', 'maintain'])
    reason = serializers.CharField()
    confidence = serializers.FloatField(min_value=0.0, max_value=1.0)
    estimated_impact = serializers.DictField()