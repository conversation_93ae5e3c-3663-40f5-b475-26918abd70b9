# queue_system/urls.py
"""
URL routing for PhotoFish Enhanced Queue System
Defines all API endpoints for queue management and monitoring
"""

from django.urls import path, include
from . import views

app_name = 'queue_system'

urlpatterns = [
    # ==================== JOB MANAGEMENT ENDPOINTS ====================
    
    # Job submission and management
    path('jobs/submit/', views.submit_job, name='submit_job'),
    path('jobs/bulk/', views.submit_bulk_jobs, name='submit_bulk_jobs'),
    path('jobs/<uuid:job_id>/', views.get_job_status, name='get_job_status'),
    path('jobs/<uuid:job_id>/update/', views.update_job, name='update_job'),
    path('jobs/<uuid:job_id>/cancel/', views.cancel_job, name='cancel_job'),
    
    # User job listings
    path('jobs/my/', views.UserJobsListView.as_view(), name='user_jobs'),
    
    # ==================== QUEUE STATUS AND MONITORING ====================
    
    # Queue status and statistics
    path('status/', views.get_queue_status, name='queue_status'),
    path('health/', views.health_check, name='health_check'),
    
    # ==================== ADMIN ENDPOINTS ====================
    
    # Administrative job management
    path('admin/jobs/', views.AdminJobsListView.as_view(), name='admin_jobs'),
    
    # Worker management and monitoring
    path('admin/workers/', views.get_worker_status, name='worker_status'),
    
    # System metrics and monitoring
    path('admin/metrics/', views.get_system_metrics, name='system_metrics'),
    path('admin/errors/', views.get_error_logs, name='error_logs'),
    
    # ==================== FUTURE ENDPOINTS (Placeholders) ====================
    # These would be implemented as the system grows
    
    # Advanced monitoring endpoints
    # path('metrics/performance/', views.get_performance_report, name='performance_report'),
    # path('metrics/dashboard/', views.get_dashboard_data, name='dashboard_data'),
    
    # Configuration management
    # path('admin/config/', views.get_configuration, name='get_configuration'),
    # path('admin/config/<str:config_key>/', views.update_configuration, name='update_configuration'),
    
    # Scaling and optimization
    # path('admin/scaling/recommendations/', views.get_scaling_recommendations, name='scaling_recommendations'),
    # path('admin/scaling/execute/', views.execute_scaling, name='execute_scaling'),
    
    # Worker registration and management
    # path('workers/register/', views.register_worker, name='register_worker'),
    # path('workers/<str:worker_id>/heartbeat/', views.worker_heartbeat, name='worker_heartbeat'),
    # path('workers/<str:worker_id>/deregister/', views.deregister_worker, name='deregister_worker'),
    
    # Batch and dependency management
    # path('jobs/batch/<str:batch_name>/', views.get_batch_status, name='batch_status'),
    # path('jobs/<uuid:job_id>/dependencies/', views.get_job_dependencies, name='job_dependencies'),
    # path('jobs/dependencies/create/', views.create_job_dependency, name='create_dependency'),
    
    # Advanced search and filtering
    # path('jobs/search/', views.search_jobs, name='search_jobs'),
    # path('jobs/export/', views.export_jobs, name='export_jobs'),
    
    # Real-time updates (WebSocket endpoints would be defined separately)
    # path('ws/queue-updates/', views.queue_updates_websocket, name='queue_updates_ws'),
    # path('ws/job-progress/<uuid:job_id>/', views.job_progress_websocket, name='job_progress_ws'),
]

# Additional URL patterns for API versioning (if needed in the future)
v1_patterns = [
    path('v1/', include(urlpatterns)),
]

# If you want to support API versioning, uncomment this:
# urlpatterns = v1_patterns + urlpatterns