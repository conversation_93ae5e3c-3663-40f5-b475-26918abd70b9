# queue_system/views.py
"""
REST API views for PhotoFish Enhanced Queue System
Comprehensive queue management and monitoring endpoints
"""

from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings
from django.utils import timezone
from django.http import JsonResponse
from django.db.models import Count, Avg, Q
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import logging
import uuid

from .models import QueueJob, QueueMetrics, WorkerStatus, ErrorLog, QueueConfiguration
from .serializers import (
    QueueJobSerializer, JobSubmissionSerializer, JobUpdateSerializer,
    QueueStatusSerializer, QueueMetricsSerializer, WorkerStatusSerializer,
    ErrorLogSerializer, QueueConfigurationSerializer, BulkJobSubmissionSerializer,
    PerformanceReportSerializer, HealthCheckSerializer, ScalingRecommendationSerializer
)

# Initialize queue system components
logger = logging.getLogger(__name__)

try:
    from .queue_engine.queue_manager import QueueManager
    from .monitoring.metrics_collector import MetricsCollector
    from .monitoring.health_monitor import HealthMonitor
    from .scaling.horizontal_scaler import HorizontalScaler
    from .scaling.auto_scaler import AutoScaler
    
    queue_manager = QueueManager()
    metrics_collector = MetricsCollector()
    health_monitor = HealthMonitor()
    horizontal_scaler = HorizontalScaler()
    auto_scaler = AutoScaler()
except ImportError as e:
    logger.warning(f"Some queue system components not available: {e}")
    queue_manager = None
    metrics_collector = None
    health_monitor = None
    horizontal_scaler = None
    auto_scaler = None


# ==================== JOB MANAGEMENT ENDPOINTS ====================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_job(request):
    """
    Submit a new job to the queue
    
    Expected payload:
    {
        "job_type": "facial_recognition",
        "priority": "HIGH",
        "data": {
            "photo_ids": [1, 2, 3],
            "event_id": 123
        },
        "options": {
            "retry_count": 3,
            "timeout": 300
        }
    }
    """
    try:
        serializer = JobSubmissionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {'error': 'Invalid job data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        
        # Create job ID
        job_id = str(uuid.uuid4())
        
        # Create job record
        job = QueueJob.objects.create(
            job_id=job_id,
            job_type=validated_data['job_type'],
            priority=validated_data['priority'],
            data=validated_data['data'],
            options=validated_data.get('options', {}),
            user=request.user,
            max_retries=validated_data.get('max_retries', 3),
            estimated_duration=validated_data.get('estimated_duration'),
            status='pending'
        )
        
        # Submit to queue manager if available
        if queue_manager:
            success = queue_manager.submit_job(
                job_id=job_id,
                job_type=validated_data['job_type'],
                priority=validated_data['priority'],
                data=validated_data['data'],
                options=validated_data.get('options', {}),
                user_id=request.user.id
            )
            
            if success:
                job.status = 'queued'
                job.save()
        
        return Response({
            'job_id': job_id,
            'status': job.status,
            'message': 'Job successfully submitted to queue',
            'estimated_completion': _estimate_completion_time(validated_data['priority'])
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        logger.error(f"Error submitting job: {str(e)}")
        return Response({
            'error': 'Internal server error',
            'details': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_job_status(request, job_id):
    """
    Get status of a specific job
    """
    try:
        # Get job from database
        try:
            job = QueueJob.objects.get(job_id=job_id)
            
            # Check if user has permission to view this job
            if job.user != request.user and not request.user.is_staff:
                return Response({
                    'error': 'Permission denied'
                }, status=status.HTTP_403_FORBIDDEN)
                
        except QueueJob.DoesNotExist:
            return Response({
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Serialize job data
        serializer = QueueJobSerializer(job)
        
        # Add real-time queue position if job is pending
        response_data = serializer.data
        if job.status in ['pending', 'queued'] and queue_manager:
            queue_position = queue_manager.get_job_position(job_id)
            if queue_position:
                response_data['queue_position'] = queue_position
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_job(request, job_id):
    """
    Update job status or progress (typically used by workers)
    """
    try:
        try:
            job = QueueJob.objects.get(job_id=job_id)
        except QueueJob.DoesNotExist:
            return Response({
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Only allow updates by job owner, staff, or workers
        if (job.user != request.user and 
            not request.user.is_staff and 
            not request.user.groups.filter(name='queue_workers').exists()):
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        serializer = JobUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        
        # Update job fields
        if 'status' in validated_data:
            job.status = validated_data['status']
            if validated_data['status'] == 'processing' and not job.started_at:
                job.started_at = timezone.now()
            elif validated_data['status'] in ['completed', 'failed'] and not job.completed_at:
                job.completed_at = timezone.now()
        
        if 'progress_percentage' in validated_data:
            job.progress_percentage = validated_data['progress_percentage']
        
        if 'result' in validated_data:
            job.result = validated_data['result']
        
        if 'error_message' in validated_data:
            job.error_message = validated_data['error_message']
        
        if 'worker_id' in validated_data:
            job.worker_id = validated_data['worker_id']
        
        job.updated_at = timezone.now()
        job.save()
        
        # Update queue manager if available
        if queue_manager:
            queue_manager.update_job_status(job_id, job.status, job.progress_percentage)
        
        return Response({
            'message': 'Job updated successfully',
            'job': QueueJobSerializer(job).data
        })
        
    except Exception as e:
        logger.error(f"Error updating job {job_id}: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def cancel_job(request, job_id):
    """
    Cancel a pending or queued job
    """
    try:
        try:
            job = QueueJob.objects.get(job_id=job_id)
        except QueueJob.DoesNotExist:
            return Response({
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check permissions
        if job.user != request.user and not request.user.is_staff:
            return Response({
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Can only cancel pending or queued jobs
        if job.status not in ['pending', 'queued']:
            return Response({
                'error': f'Cannot cancel job with status: {job.status}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Update job status
        job.status = 'cancelled'
        job.completed_at = timezone.now()
        job.save()
        
        # Remove from queue manager if available
        if queue_manager:
            queue_manager.cancel_job(job_id)
        
        return Response({
            'message': 'Job cancelled successfully'
        })
        
    except Exception as e:
        logger.error(f"Error cancelling job {job_id}: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== QUEUE STATUS ENDPOINTS ====================

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_queue_status(request):
    """
    Get comprehensive queue status and statistics
    """
    try:
        # Get job counts by status
        job_counts = QueueJob.objects.values('status').annotate(count=Count('id'))
        status_dict = {item['status']: item['count'] for item in job_counts}
        
        # Get job counts by priority
        priority_counts = QueueJob.objects.values('priority').annotate(count=Count('id'))
        priority_dict = {item['priority']: item['count'] for item in priority_counts}
        
        # Get job counts by type
        type_counts = QueueJob.objects.values('job_type').annotate(count=Count('id'))
        type_dict = {item['job_type']: item['count'] for item in type_counts}
        
        # Calculate average times
        completed_jobs = QueueJob.objects.filter(
            status='completed',
            started_at__isnull=False,
            completed_at__isnull=False
        )
        
        avg_processing_time = 0
        if completed_jobs.exists():
            processing_times = []
            for job in completed_jobs:
                duration = (job.completed_at - job.started_at).total_seconds()
                processing_times.append(duration)
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        # Get worker statistics
        active_workers = WorkerStatus.objects.filter(
            status__in=['idle', 'active', 'busy'],
            last_heartbeat__gt=timezone.now() - timedelta(minutes=5)
        ).count()
        
        total_workers = WorkerStatus.objects.count()
        
        # Calculate worker utilization
        worker_utilization = 0
        if total_workers > 0:
            busy_workers = WorkerStatus.objects.filter(status='busy').count()
            worker_utilization = (busy_workers / total_workers) * 100
        
        # Determine system health
        system_health = 'healthy'
        pending_jobs = status_dict.get('pending', 0) + status_dict.get('queued', 0)
        if pending_jobs > 100:
            system_health = 'overloaded'
        elif active_workers == 0:
            system_health = 'critical'
        elif worker_utilization > 90:
            system_health = 'stressed'
        
        queue_status = {
            'total_jobs': sum(status_dict.values()),
            'pending_jobs': status_dict.get('pending', 0) + status_dict.get('queued', 0),
            'processing_jobs': status_dict.get('processing', 0),
            'completed_jobs': status_dict.get('completed', 0),
            'failed_jobs': status_dict.get('failed', 0),
            'jobs_by_priority': priority_dict,
            'jobs_by_type': type_dict,
            'average_queue_time': 0,  # Would need more complex calculation
            'average_processing_time': avg_processing_time,
            'active_workers': active_workers,
            'total_workers': total_workers,
            'worker_utilization': worker_utilization,
            'system_health': system_health,
            'last_updated': timezone.now()
        }
        
        serializer = QueueStatusSerializer(queue_status)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Error getting queue status: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== USER JOB LISTING ENDPOINTS ====================

class UserJobsListView(generics.ListAPIView):
    """
    List jobs for the authenticated user
    """
    serializer_class = QueueJobSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        user = self.request.user
        queryset = QueueJob.objects.filter(user=user)
        
        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by job type if provided
        job_type = self.request.query_params.get('job_type')
        if job_type:
            queryset = queryset.filter(job_type=job_type)
        
        # Filter by priority if provided
        priority = self.request.query_params.get('priority')
        if priority:
            queryset = queryset.filter(priority=priority)
        
        return queryset.order_by('-created_at')


# ==================== ADMIN ENDPOINTS ====================

class AdminJobsListView(generics.ListAPIView):
    """
    List all jobs (admin only)
    """
    serializer_class = QueueJobSerializer
    permission_classes = [IsAdminUser]
    queryset = QueueJob.objects.all().order_by('-created_at')


@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_worker_status(request):
    """
    Get status of all workers (admin only)
    """
    try:
        workers = WorkerStatus.objects.all().order_by('-last_heartbeat')
        serializer = WorkerStatusSerializer(workers, many=True)
        return Response(serializer.data)
        
    except Exception as e:
        logger.error(f"Error getting worker status: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_system_metrics(request):
    """
    Get system performance metrics (admin only)
    """
    try:
        # Get time range from query params
        hours = int(request.query_params.get('hours', 24))
        start_time = timezone.now() - timedelta(hours=hours)
        
        # Get metrics from database
        metrics = QueueMetrics.objects.filter(
            timestamp__gte=start_time
        ).order_by('-timestamp')
        
        # Group metrics by type
        metrics_by_type = {}
        for metric in metrics:
            if metric.metric_type not in metrics_by_type:
                metrics_by_type[metric.metric_type] = []
            metrics_by_type[metric.metric_type].append({
                'value': metric.value,
                'timestamp': metric.timestamp,
                'priority_level': metric.priority_level,
                'source': metric.source
            })
        
        return Response({
            'time_range_hours': hours,
            'metrics': metrics_by_type,
            'generated_at': timezone.now()
        })
        
    except Exception as e:
        logger.error(f"Error getting system metrics: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAdminUser])
def get_error_logs(request):
    """
    Get system error logs (admin only)
    """
    try:
        # Get time range and filters
        hours = int(request.query_params.get('hours', 24))
        severity = request.query_params.get('severity')
        error_type = request.query_params.get('error_type')
        
        start_time = timezone.now() - timedelta(hours=hours)
        
        # Build query
        queryset = ErrorLog.objects.filter(timestamp__gte=start_time)
        
        if severity:
            queryset = queryset.filter(severity=severity)
        
        if error_type:
            queryset = queryset.filter(error_type=error_type)
        
        # Get errors
        errors = queryset.order_by('-timestamp')[:100]  # Limit to 100 most recent
        serializer = ErrorLogSerializer(errors, many=True)
        
        return Response({
            'errors': serializer.data,
            'total_count': queryset.count(),
            'time_range_hours': hours,
            'filters': {
                'severity': severity,
                'error_type': error_type
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting error logs: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== HEALTH CHECK ENDPOINT ====================

@api_view(['GET'])
def health_check(request):
    """
    System health check endpoint
    """
    try:
        health_data = {
            'status': 'healthy',
            'timestamp': timezone.now(),
            'components': {}
        }
        
        # Check database
        try:
            QueueJob.objects.count()
            health_data['components']['database'] = 'healthy'
        except Exception as e:
            health_data['components']['database'] = f'error: {str(e)}'
            health_data['status'] = 'unhealthy'
        
        # Check queue manager
        if queue_manager:
            try:
                queue_status = queue_manager.get_queue_status()
                health_data['components']['queue_manager'] = 'healthy'
            except Exception as e:
                health_data['components']['queue_manager'] = f'error: {str(e)}'
                health_data['status'] = 'degraded'
        else:
            health_data['components']['queue_manager'] = 'not_available'
        
        # Check active workers
        active_workers = WorkerStatus.objects.filter(
            status__in=['idle', 'active', 'busy'],
            last_heartbeat__gt=timezone.now() - timedelta(minutes=5)
        ).count()
        
        health_data['components']['workers'] = {
            'status': 'healthy' if active_workers > 0 else 'no_workers',
            'active_count': active_workers
        }
        
        if active_workers == 0:
            health_data['status'] = 'degraded'
        
        return Response(health_data)
        
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        return Response({
            'status': 'error',
            'error': str(e),
            'timestamp': timezone.now()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== HELPER FUNCTIONS ====================

def _estimate_completion_time(priority):
    """
    Estimate job completion time based on priority and current queue
    """
    try:
        # Simple estimation logic - can be enhanced
        base_time = timezone.now()
        
        if priority == 'EMERGENCY':
            return base_time + timedelta(minutes=5)
        elif priority == 'HIGH':
            return base_time + timedelta(minutes=15)
        elif priority == 'STANDARD':
            return base_time + timedelta(minutes=30)
        elif priority == 'LOW':
            return base_time + timedelta(hours=1)
        else:
            return base_time + timedelta(hours=2)
            
    except Exception:
        return timezone.now() + timedelta(minutes=30)


# ==================== BULK OPERATIONS ====================

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def submit_bulk_jobs(request):
    """
    Submit multiple jobs at once
    """
    try:
        serializer = BulkJobSubmissionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        jobs_data = validated_data['jobs']
        batch_name = validated_data.get('batch_name', f"Batch_{timezone.now().strftime('%Y%m%d_%H%M%S')}")
        priority_override = validated_data.get('priority_override')
        
        created_jobs = []
        
        for job_data in jobs_data:
            job_id = str(uuid.uuid4())
            
            # Override priority if specified
            if priority_override:
                job_data['priority'] = priority_override
            
            # Create job record
            job = QueueJob.objects.create(
                job_id=job_id,
                job_type=job_data['job_type'],
                priority=job_data['priority'],
                data=job_data['data'],
                options=job_data.get('options', {}),
                user=request.user,
                max_retries=job_data.get('max_retries', 3),
                status='pending'
            )
            
            # Add batch information to options
            if 'batch_info' not in job.options:
                job.options['batch_info'] = {}
            job.options['batch_info']['batch_name'] = batch_name
            job.save()
            
            created_jobs.append(job)
            
            # Submit to queue manager if available
            if queue_manager:
                queue_manager.submit_job(
                    job_id=job_id,
                    job_type=job_data['job_type'],
                    priority=job_data['priority'],
                    data=job_data['data'],
                    options=job.options,
                    user_id=request.user.id
                )
        
        return Response({
            'message': f'Successfully submitted {len(created_jobs)} jobs',
            'batch_name': batch_name,
            'job_ids': [job.job_id for job in created_jobs],
            'jobs': QueueJobSerializer(created_jobs, many=True).data
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        logger.error(f"Error submitting bulk jobs: {str(e)}")
        return Response({
            'error': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)