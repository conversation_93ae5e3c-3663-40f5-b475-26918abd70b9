# Generated by Django 5.1.5 on 2025-06-30 01:18

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('FREE', 'Free'), ('LIGHT', 'Light'), ('CORE', 'Core'), ('ADVANCED', 'Advanced'), ('PROFESSIONAL', 'Professional'), ('BUSINESS', 'Business'), ('ELITE', 'Elite')], max_length=50)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('description', models.TextField()),
                ('features', models.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'subscription',
                'ordering': ['price'],
            },
        ),
    ]
