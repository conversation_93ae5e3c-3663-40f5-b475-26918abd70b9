# subscriptions/models.py
from django.db import models


class Subscription(models.Model):
    class SubscriptionType(models.TextChoices):
        FREE = 'FREE', 'Free'
        LIGHT = 'LIGHT', 'Light'
        CORE = 'CORE', 'Core'
        ADVANCED = 'ADVANCED', 'Advanced'
        PROFESSIONAL = 'PROFESSIONAL', 'Professional'
        BUSINESS = 'BUSINESS', 'Business'
        ELITE = 'ELITE', 'Elite'

    name = models.CharField(max_length=50, choices=SubscriptionType.choices)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField()
    features = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'subscription'
        ordering = ['price']

    def __str__(self):
        return f"{self.name} - ${self.price}"