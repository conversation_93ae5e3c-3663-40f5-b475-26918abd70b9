from rest_framework import serializers
from .models import Subscription


class SubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subscription
        fields = ['id', 'name', 'price', 'description', 'features',
                 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def validate_features(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError(
                "Features must be a JSON object"
            )
        return value
