from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Subscription
from .serializers import SubscriptionSerializer


class SubscriptionViewSet(viewsets.ModelViewSet):
    queryset = Subscription.objects.all()
    serializer_class = SubscriptionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            return [permissions.IsAuthenticated()]
        return [permissions.IsAdminUser()]

    @action(detail=True, methods=['post'])
    def subscribe(self, request, pk=None):
        subscription = self.get_object()
        user = request.user

        # Add your subscription logic here
        # This is a placeholder - implement actual subscription process
        
        return Response({
            'message': f'Successfully subscribed to {subscription.name} plan'
        })