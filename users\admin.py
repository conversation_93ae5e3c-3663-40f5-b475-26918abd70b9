# users/admin.py - Enhanced Django Admin for Secure PhotoFish Admin Management

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import User, LoginHistory
import json


@admin.register(User)
class PhotofishUserAdmin(BaseUserAdmin):
    """Enhanced User admin with PhotoFish admin management capabilities"""
    
    # List display
    list_display = [
        'email', 'first_name', 'last_name', 'user_type', 'account_status',
        'is_photofish_admin', 'admin_status_display', 'is_email_verified',
        'created_at', 'last_login_at'
    ]
    
    # List filters
    list_filter = [
        'is_photofish_admin', 'user_type', 'account_status', 'is_email_verified',
        'auth_provider', 'created_at', 'last_login_at'
    ]
    
    # Search fields
    search_fields = ['email', 'first_name', 'last_name', 'username']
    
    # Ordering
    ordering = ['-created_at']
    
    # Read-only fields
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'last_login_at',
        'admin_assigned_at', 'admin_permissions_display'
    ]
    
    # Fieldsets for detailed view
    fieldsets = (
        ('Basic Information', {
            'fields': (
                'id', 'email', 'username', 'first_name', 'last_name',
                'profile_picture', 'phone_number', 'date_of_birth', 'location', 'bio'
            )
        }),
        ('Account Settings', {
            'fields': (
                'user_type', 'account_status', 'auth_provider',
                'is_active', 'is_email_verified', 'face_recognition_consent',
                'marketing_consent'
            )
        }),
        ('PhotoFish Admin Settings', {
            'fields': (
                'is_photofish_admin', 'admin_permissions', 'admin_permissions_display',
                'admin_notes', 'admin_assigned_by', 'admin_assigned_at'
            ),
            'classes': ['wide'],
            'description': 'PhotoFish admin privileges - Use carefully!'
        }),
        ('Privacy & Preferences', {
            'fields': ('privacy_settings', 'notification_preferences'),
            'classes': ['collapse']
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'last_login_at'),
            'classes': ['collapse']
        })
    )
    
    # Add fieldsets for creating new users
    add_fieldsets = (
        ('Required Information', {
            'fields': ('email', 'first_name', 'last_name', 'password1', 'password2')
        }),
        ('Account Type', {
            'fields': ('user_type', 'account_status')
        })
    )
    
    def admin_status_display(self, obj):
        """Display admin status with visual indicator"""
        if obj.is_photofish_admin:
            return format_html(
                '<span style="color: #e74c3c; font-weight: bold;">✓ ADMIN</span>'
            )
        return format_html('<span style="color: #95a5a6;">Regular User</span>')
    admin_status_display.short_description = 'Admin Status'
    
    def admin_permissions_display(self, obj):
        """Display admin permissions in readable format"""
        if not obj.is_photofish_admin or not obj.admin_permissions:
            return "No admin permissions"
        
        permissions_html = "<ul>"
        for permission, enabled in obj.admin_permissions.items():
            status = "✓" if enabled else "✗"
            color = "#27ae60" if enabled else "#e74c3c"
            permissions_html += f'<li><span style="color: {color};">{status}</span> {permission.replace("_", " ").title()}</li>'
        permissions_html += "</ul>"
        
        return format_html(permissions_html)
    admin_permissions_display.short_description = 'Admin Permissions'
    
    def save_model(self, request, obj, form, change):
        """Override save to track admin privilege assignments"""
        if change:  # Updating existing user
            original = User.objects.get(id=obj.id)
            
            # Check if admin status changed
            if not original.is_photofish_admin and obj.is_photofish_admin:
                # User is being made admin
                obj.admin_assigned_by = request.user
                obj.admin_assigned_at = timezone.now()
                
                # Set default permissions if none provided
                if not obj.admin_permissions:
                    obj.admin_permissions = {
                        'user_management': True,
                        'event_oversight': True,
                        'queue_monitoring': True,
                        'subscription_assignment': False,
                        'financial_oversight': False,
                        'system_administration': False
                    }
                
                # Add admin assignment note
                assignment_note = f"Admin privileges assigned by {request.user.email} via Django Admin on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
                if obj.admin_notes:
                    obj.admin_notes += f"\n\n{assignment_note}"
                else:
                    obj.admin_notes = assignment_note
            
            elif original.is_photofish_admin and not obj.is_photofish_admin:
                # Admin privileges being revoked
                revocation_note = f"Admin privileges revoked by {request.user.email} via Django Admin on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
                if obj.admin_notes:
                    obj.admin_notes += f"\n\n{revocation_note}"
                else:
                    obj.admin_notes = revocation_note
                
                obj.admin_permissions = {}
        
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('admin_assigned_by')
    
    actions = ['make_photofish_admin', 'revoke_admin_privileges', 'activate_accounts', 'suspend_accounts']
    
    def make_photofish_admin(self, request, queryset):
        """Bulk action to make users PhotoFish admins"""
        count = 0
        for user in queryset:
            if not user.is_photofish_admin:
                user.assign_admin_privileges(
                    assigned_by_user=request.user,
                    notes=f"Bulk assignment via Django Admin by {request.user.email}"
                )
                count += 1
        
        self.message_user(request, f"Successfully assigned admin privileges to {count} users.")
    make_photofish_admin.short_description = "Assign PhotoFish admin privileges"
    
    def revoke_admin_privileges(self, request, queryset):
        """Bulk action to revoke admin privileges"""
        count = 0
        for user in queryset:
            if user.is_photofish_admin:
                user.revoke_admin_privileges(revoked_by_user=request.user)
                count += 1
        
        self.message_user(request, f"Successfully revoked admin privileges from {count} users.")
    revoke_admin_privileges.short_description = "Revoke PhotoFish admin privileges"
    
    def activate_accounts(self, request, queryset):
        """Bulk action to activate user accounts"""
        count = queryset.update(account_status='ACTIVE', is_active=True)
        self.message_user(request, f"Successfully activated {count} accounts.")
    activate_accounts.short_description = "Activate selected accounts"
    
    def suspend_accounts(self, request, queryset):
        """Bulk action to suspend user accounts"""
        count = queryset.update(account_status='SUSPENDED')
        self.message_user(request, f"Successfully suspended {count} accounts.")
    suspend_accounts.short_description = "Suspend selected accounts"


@admin.register(LoginHistory)
class LoginHistoryAdmin(admin.ModelAdmin):
    """Admin interface for login history monitoring"""
    
    list_display = [
        'user_email', 'login_datetime', 'interface_type', 'login_method',
        'is_successful', 'ip_address', 'session_duration_display'
    ]
    
    list_filter = [
        'is_successful', 'interface_type', 'login_method', 'login_datetime'
    ]
    
    search_fields = ['user__email', 'ip_address', 'user_agent']
    
    readonly_fields = ['id', 'session_duration_display']
    
    ordering = ['-login_datetime']
    
    date_hierarchy = 'login_datetime'
    
    def user_email(self, obj):
        """Display user email with link to user"""
        if obj.user:
            url = reverse('admin:users_user_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.email)
        return "Unknown User"
    user_email.short_description = 'User Email'
    user_email.admin_order_field = 'user__email'
    
    def session_duration_display(self, obj):
        """Display session duration in readable format"""
        if obj.logout_datetime:
            duration = obj.logout_datetime - obj.login_datetime
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            return f"{hours}h {minutes}m {seconds}s"
        return "Active session"
    session_duration_display.short_description = 'Session Duration'
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('user')
    
    def has_add_permission(self, request):
        """Prevent manual creation of login history"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Prevent editing of login history"""
        return False


# Admin site customization
admin.site.site_header = "PhotoFish Administration"
admin.site.site_title = "PhotoFish Admin"
admin.site.index_title = "PhotoFish Platform Management"

# Custom admin actions message
admin.site.empty_value_display = '-empty-'