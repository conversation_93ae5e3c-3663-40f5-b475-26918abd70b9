# users/admin_views.py - PhotoFish Admin API Views

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.utils import timezone
from django.db.models import Q, Count
from django.db import transaction
from datetime import timedelta
import logging

from .models import User, LoginHistory
from events.models import Event, EventUserRole
from .serializers import (
    AdminUserSerializer, LoginHistorySerializer, AdminDashboardStatsSerializer,
    UserManagementActionSerializer, AdminPermissionSerializer
)

logger = logging.getLogger(__name__)


class AdminPermissionMixin:
    """Mixin to check admin permissions"""
    
    def check_admin_permission(self, user, required_permission=None):
        """Check if user has admin access and specific permission"""
        if not user.can_access_admin_panel():
            return False, "Admin access denied"
        
        if required_permission and not user.has_admin_permission(required_permission):
            return False, f"Missing permission: {required_permission}"
        
        return True, None


class AdminPagination(PageNumberPagination):
    """Custom pagination for admin endpoints"""
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 100


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_dashboard_overview(request):
    """
    GET /api/v1/admin/dashboard/
    Admin dashboard overview with key platform metrics
    """
    try:
        user = request.user
        
        # Check admin permissions
        if not user.can_access_admin_panel():
            return Response({
                'message': 'Admin access denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Calculate time ranges
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_start = today_start - timedelta(days=1)
        last_24h = now - timedelta(hours=24)
        
        # User statistics
        total_users = User.objects.filter(is_active=True).count()
        active_users = User.objects.filter(
            account_status='ACTIVE',
            is_active=True
        ).count()
        total_admins = User.objects.filter(is_photofish_admin=True).count()
        users_last_24h = User.objects.filter(created_at__gte=last_24h).count()
        
        # Login statistics
        successful_logins_today = LoginHistory.objects.filter(
            is_successful=True,
            login_datetime__gte=today_start
        ).count()
        failed_logins_today = LoginHistory.objects.filter(
            is_successful=False,
            login_datetime__gte=today_start
        ).count()
        
        # Role statistics
        photographer_count = EventUserRole.objects.filter(
            role='PHOTOGRAPHER',
            is_active=True
        ).values('user').distinct().count()
        organizer_count = EventUserRole.objects.filter(
            role='ORGANIZER',
            is_active=True
        ).values('user').distinct().count()
        
        # System health with queue integration
        try:
            from queue_system.models import QueueJob, WorkerStatus
            
            # Queue system health check
            total_queue_jobs = QueueJob.objects.count()
            active_workers = WorkerStatus.objects.filter(status__in=['IDLE', 'BUSY']).count()
            failed_jobs_today = QueueJob.objects.filter(
                status='FAILED',
                created_at__gte=today_start
            ).count()
            
            queue_status = f"operational ({active_workers} workers, {total_queue_jobs} total jobs)"
            if failed_jobs_today > 10:
                queue_status = f"degraded ({failed_jobs_today} failures today)"
            
        except ImportError:
            queue_status = "queue system not available"
        except Exception as e:
            queue_status = f"queue system error: {str(e)}"
            logger.warning(f"Queue system health check failed: {str(e)}")
        
        system_status = "healthy"
        database_status = "connected"
        
        # Recent activity
        recent_registrations = User.objects.filter(
            created_at__gte=last_24h
        ).order_by('-created_at')[:5]
        
        recent_logins = LoginHistory.objects.filter(
            is_successful=True,
            login_datetime__gte=last_24h
        ).order_by('-login_datetime')[:10]
        
        # Prepare dashboard data with queue system integration
        dashboard_data = {
            'total_users': total_users,
            'active_users': active_users,
            'total_admins': total_admins,
            'users_last_24h': users_last_24h,
            'successful_logins_today': successful_logins_today,
            'failed_logins_today': failed_logins_today,
            'photographer_count': photographer_count,
            'organizer_count': organizer_count,
            'system_status': system_status,
            'database_status': database_status,
            'queue_status': queue_status,
            'recent_registrations': AdminUserSerializer(recent_registrations, many=True).data,
            'recent_logins': LoginHistorySerializer(recent_logins, many=True).data
        }
        
        # Add queue system metrics if available
        try:
            from queue_system.models import QueueJob, WorkerStatus
            
            # Queue system statistics
            queue_stats = {
                'total_jobs': QueueJob.objects.count(),
                'pending_jobs': QueueJob.objects.filter(status='PENDING').count(),
                'processing_jobs': QueueJob.objects.filter(status='PROCESSING').count(),
                'completed_jobs_today': QueueJob.objects.filter(
                    status='COMPLETED',
                    completed_at__gte=today_start
                ).count(),
                'failed_jobs_today': QueueJob.objects.filter(
                    status='FAILED',
                    created_at__gte=today_start
                ).count(),
                'active_workers': WorkerStatus.objects.filter(status__in=['IDLE', 'BUSY']).count(),
                'total_workers': WorkerStatus.objects.count()
            }
            dashboard_data['queue_system'] = queue_stats
            
        except ImportError:
            dashboard_data['queue_system'] = {'status': 'not_available'}
        except Exception as e:
            logger.warning(f"Could not fetch queue system stats: {str(e)}")
            dashboard_data['queue_system'] = {'status': 'error', 'message': str(e)}
        
        return Response({
            'dashboard': dashboard_data,
            'generated_at': now.isoformat(),
            'admin_user': user.email
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Admin dashboard error: {str(e)}")
        return Response({
            'message': 'Error loading dashboard data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_users_list(request):
    """
    GET /api/v1/admin/users/?search=&user_type=&account_status=&is_admin=
    List and search all platform users
    """
    try:
        user = request.user
        
        # Check admin permissions
        if not user.can_access_admin_panel() or not user.has_admin_permission('user_management'):
            return Response({
                'message': 'User management permission required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        search = request.GET.get('search', '').strip()
        user_type = request.GET.get('user_type', '')
        account_status = request.GET.get('account_status', '')
        is_admin = request.GET.get('is_admin', '')
        
        # Build queryset
        queryset = User.objects.all().order_by('-created_at')
        
        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(email__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(username__icontains=search)
            )
        
        if user_type:
            queryset = queryset.filter(user_type=user_type)
        
        if account_status:
            queryset = queryset.filter(account_status=account_status)
        
        if is_admin:
            is_admin_bool = is_admin.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_photofish_admin=is_admin_bool)
        
        # Paginate results
        paginator = AdminPagination()
        page = paginator.paginate_queryset(queryset, request)
        
        if page is not None:
            serializer = AdminUserSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)
        
        serializer = AdminUserSerializer(queryset, many=True)
        return Response({
            'users': serializer.data,
            'total_count': queryset.count()
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Admin users list error: {str(e)}")
        return Response({
            'message': 'Error loading users data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_user_detail(request, user_id):
    """
    GET /api/v1/admin/users/{user_id}/
    Get detailed information about a specific user
    """
    try:
        admin_user = request.user
        
        # Check admin permissions
        if not admin_user.can_access_admin_panel() or not admin_user.has_admin_permission('user_management'):
            return Response({
                'message': 'User management permission required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Get user
        try:
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return Response({
                'message': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get user's event roles
        event_roles = EventUserRole.objects.filter(user=target_user).select_related('event')
        
        # Get recent login history
        recent_logins = target_user.login_history.order_by('-login_datetime')[:10]
        
        # Prepare detailed user data
        user_data = AdminUserSerializer(target_user).data
        user_data['event_roles'] = [
            {
                'event_name': role.event.name,
                'role': role.role,
                'joined_at': role.joined_at,
                'is_active': role.is_active
            }
            for role in event_roles
        ]
        user_data['recent_logins'] = LoginHistorySerializer(recent_logins, many=True).data
        
        return Response({
            'user': user_data
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Admin user detail error: {str(e)}")
        return Response({
            'message': 'Error loading user details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def admin_user_actions(request):
    """
    POST /api/v1/admin/users/actions/
    Perform bulk actions on users (activate, suspend, assign admin, etc.)
    """
    try:
        admin_user = request.user
        
        # Check admin permissions
        if not admin_user.can_access_admin_panel() or not admin_user.has_admin_permission('user_management'):
            return Response({
                'message': 'User management permission required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Validate request data
        serializer = UserManagementActionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'message': 'Invalid request data',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        user_ids = serializer.validated_data['user_ids']
        action = serializer.validated_data['action']
        reason = serializer.validated_data.get('reason', '')
        admin_permissions = serializer.validated_data.get('admin_permissions', {})
        
        # Get target users
        target_users = User.objects.filter(id__in=user_ids)
        if target_users.count() != len(user_ids):
            return Response({
                'message': 'Some users not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        results = []
        
        with transaction.atomic():
            for user in target_users:
                try:
                    result = {'user_id': str(user.id), 'email': user.email}
                    
                    if action == 'activate':
                        user.account_status = 'ACTIVE'
                        user.is_active = True
                        user.save()
                        result['status'] = 'activated'
                    
                    elif action == 'suspend':
                        user.account_status = 'SUSPENDED'
                        user.save()
                        result['status'] = 'suspended'
                    
                    elif action == 'verify_email':
                        user.is_email_verified = True
                        user.save()
                        result['status'] = 'email_verified'
                    
                    elif action == 'assign_admin':
                        user.assign_admin_privileges(
                            assigned_by_user=admin_user,
                            permissions=admin_permissions,
                            notes=reason
                        )
                        result['status'] = 'admin_assigned'
                    
                    elif action == 'revoke_admin':
                        user.revoke_admin_privileges(revoked_by_user=admin_user)
                        result['status'] = 'admin_revoked'
                    
                    else:
                        result['status'] = 'unsupported_action'
                    
                    results.append(result)
                    
                except Exception as e:
                    results.append({
                        'user_id': str(user.id),
                        'email': user.email,
                        'status': 'error',
                        'error': str(e)
                    })
        
        return Response({
            'message': f'Bulk action {action} completed',
            'results': results,
            'performed_by': admin_user.email,
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Admin user actions error: {str(e)}")
        return Response({
            'message': 'Error performing user actions'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_events_overview(request):
    """
    GET /api/v1/admin/events/
    Overview of all events on the platform
    """
    try:
        admin_user = request.user
        
        # Check admin permissions
        if not admin_user.can_access_admin_panel() or not admin_user.has_admin_permission('event_oversight'):
            return Response({
                'message': 'Event oversight permission required'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Get events with statistics
        events = Event.objects.all().annotate(
            participant_count=Count('user_roles', distinct=True),
            photographer_count=Count('user_roles', filter=Q(user_roles__role='PHOTOGRAPHER')),
            attendee_count=Count('user_roles', filter=Q(user_roles__role='ATTENDEE'))
        ).order_by('-created_at')
        
        # Paginate
        paginator = AdminPagination()
        page = paginator.paginate_queryset(events, request)
        
        events_data = []
        event_list = page if page is not None else events
        
        for event in event_list:
            events_data.append({
                'id': str(event.id),
                'name': event.name,
                'creator_email': event.creator.email,
                'location': event.location,
                'start_date': event.start_date,
                'end_date': event.end_date,
                'is_public': event.is_public,
                'pricing_mode': event.pricing_mode,
                'participant_count': event.participant_count,
                'photographer_count': event.photographer_count,
                'attendee_count': event.attendee_count,
                'created_at': event.created_at
            })
        
        if page is not None:
            return paginator.get_paginated_response(events_data)
        
        return Response({
            'events': events_data,
            'total_count': events.count()
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Admin events overview error: {str(e)}")
        return Response({
            'message': 'Error loading events data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_login_monitoring(request):
    """
    GET /api/v1/admin/login-monitoring/
    Monitor login activity and security
    """
    try:
        admin_user = request.user
        
        # Check admin permissions
        if not admin_user.can_access_admin_panel():
            return Response({
                'message': 'Admin access denied'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Time range for analysis
        hours_back = int(request.GET.get('hours', 24))
        cutoff_time = timezone.now() - timedelta(hours=hours_back)
        
        # Login statistics
        recent_logins = LoginHistory.objects.filter(
            login_datetime__gte=cutoff_time
        ).order_by('-login_datetime')
        
        # Failed login analysis
        failed_logins = recent_logins.filter(is_successful=False)
        suspicious_ips = failed_logins.values('ip_address').annotate(
            attempt_count=Count('id')
        ).filter(attempt_count__gte=5).order_by('-attempt_count')
        
        # Interface usage
        interface_stats = recent_logins.filter(is_successful=True).values(
            'interface_type'
        ).annotate(count=Count('id'))
        
        # Paginate recent logins
        paginator = AdminPagination()
        page = paginator.paginate_queryset(recent_logins, request)
        
        login_data = LoginHistorySerializer(
            page if page is not None else recent_logins[:50], 
            many=True
        ).data
        
        response_data = {
            'monitoring_period_hours': hours_back,
            'total_logins': recent_logins.count(),
            'successful_logins': recent_logins.filter(is_successful=True).count(),
            'failed_logins': failed_logins.count(),
            'suspicious_ips': list(suspicious_ips),
            'interface_usage': list(interface_stats),
            'recent_logins': login_data
        }
        
        if page is not None:
            return paginator.get_paginated_response(response_data)
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Admin login monitoring error: {str(e)}")
        return Response({
            'message': 'Error loading login monitoring data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)