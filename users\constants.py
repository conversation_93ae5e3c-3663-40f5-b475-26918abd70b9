"""
Constant values used throughout the users app.
"""
from datetime import timedelta

# OTP Settings
OTP_LENGTH = 6
OTP_EXPIRY_MINUTES = 10
OTP_EXPIRY = timedelta(minutes=OTP_EXPIRY_MINUTES)
OTP_MAX_ATTEMPTS = 3
OTP_PURPOSE_EMAIL_VERIFICATION = 'email_verification'
OTP_PURPOSE_PHONE_VERIFICATION = 'phone_verification'
OTP_PURPOSE_PASSWORD_RESET = 'password_reset'

# Email Settings
EMAIL_SUBJECT_OTP = 'Email Verification - Your OTP'
EMAIL_SUBJECT_PASSWORD_RESET = 'PhotoFish - Password Reset Request'

# SMS Settings
SMS_SUBJECT_OTP = 'PhotoFish Phone Verification'

# User Types and Login Modes
USER_TYPE_USER = 'USER'
USER_TYPE_PHOTOGRAPHER = 'PHOTOGRAPHER'
USER_TYPE_BOTH = 'BOTH'

# Authentication Providers (Cleaned - removed Facebook/Twitter)
AUTH_PROVIDER_EMAIL = 'email'
AUTH_PROVIDER_PHONE = 'phone'
AUTH_PROVIDER_GOOGLE = 'google'

# JWT Settings (Fixed POC security issues)
JWT_ACCESS_TOKEN_LIFETIME = timedelta(minutes=15)  # Was 60 - too long!
JWT_REFRESH_TOKEN_LIFETIME = timedelta(days=7)     # Increased from 1 day

# Security Settings
MAX_LOGIN_ATTEMPTS = 5
ACCOUNT_LOCKOUT_DURATION = timedelta(minutes=30)