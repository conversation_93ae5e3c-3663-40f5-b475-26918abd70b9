import logging
import time
import json
from typing import Optional, Any, Set

from django.utils.deprecation import MiddlewareMixin
from django.http import HttpRequest, HttpResponse

logger = logging.getLogger('users')


class RequestLoggingMiddleware(MiddlewareMixin):
    SKIP_PATHS: Set[str] = {'/media/', '/static/', '/admin/jsi18n/'}
    SENSITIVE_FIELDS: Set[str] = {
        'password', 'confirm_password', 'token', 'access_token',
        'refresh_token', 'otp', 'secret', 'key'
    }

    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        request.start_time = time.time()
        if any(path in request.path for path in self.SKIP_PATHS):
            return None
        
        try:
            log_data = {
                'method': request.method,
                'path': request.path,
                'client_ip': self._get_client_ip(request),
                'user': str(request.user) if request.user.is_authenticated else 'AnonymousUser',
            }
            
            if request.method in ['POST', 'PUT', 'PATCH'] and request.body:
                try:
                    body = json.loads(request.body)
                    log_data['body'] = self._sanitize_data(body)
                except json.JSONDecodeError:
                    log_data['body'] = '[Non-JSON body]'
                except Exception:
                    log_data['body'] = '[Error parsing request body]'
            
            logger.debug(f"Request received: {json.dumps(log_data)}")
        except Exception as e:
            logger.error(f"Error logging request: {str(e)}")
        
        return None

    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        if not hasattr(request, 'start_time') or any(path in request.path for path in self.SKIP_PATHS):
            return response
        
        try:
            processing_time = time.time() - request.start_time
            status_code = response.status_code
            log_message = f"Response {status_code} for {request.method} {request.path} in {processing_time:.2f}s"
            
            if status_code >= 500:
                logger.error(log_message)
            elif status_code >= 400:
                logger.warning(log_message)
            else:
                logger.debug(log_message)
        except Exception as e:
            logger.error(f"Error logging response: {str(e)}")
        
        return response

    def _get_client_ip(self, request: HttpRequest) -> str:
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        return x_forwarded_for.split(',')[0] if x_forwarded_for else request.META.get('REMOTE_ADDR')

    def _sanitize_data(self, data: Any) -> Any:
        if not isinstance(data, dict):
            return data
        
        sanitized = {}
        for key, value in data.items():
            if key.lower() in self.SENSITIVE_FIELDS:
                sanitized[key] = '[REDACTED]'
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_data(value)
            elif isinstance(value, list):
                sanitized[key] = [self._sanitize_data(item) if isinstance(item, dict) else item for item in value]
            else:
                sanitized[key] = value
        
        return sanitized
