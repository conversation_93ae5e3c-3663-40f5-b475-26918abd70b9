# Generated by Django 5.1.5 on 2025-06-30 01:18

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('first_name', models.CharField(blank=True, max_length=150)),
                ('last_name', models.CharField(blank=True, max_length=150)),
                ('account_status', models.CharField(choices=[('ACTIVE', 'Active'), ('SUSPENDED', 'Suspended'), ('DELETED', 'Deleted')], default='ACTIVE', max_length=20)),
                ('auth_provider', models.CharField(choices=[('EMAIL', 'Email'), ('GOOGLE', 'Google'), ('FACEBOOK', 'Facebook'), ('APPLE', 'Apple')], default='EMAIL', max_length=20)),
                ('user_type', models.CharField(choices=[('USER', 'Regular User'), ('PHOTOGRAPHER', 'Photographer'), ('ORGANIZER', 'Event Organizer'), ('BOTH', 'Both User and Photographer')], default='BOTH', max_length=20)),
                ('current_login_mode', models.CharField(blank=True, choices=[('USER', 'Regular User'), ('PHOTOGRAPHER', 'Photographer'), ('ORGANIZER', 'Event Organizer'), ('BOTH', 'Both User and Photographer')], max_length=20, null=True)),
                ('is_email_verified', models.BooleanField(default=False)),
                ('email_verification_token', models.CharField(blank=True, max_length=255, null=True)),
                ('email_verification_sent_at', models.DateTimeField(blank=True, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('is_phone_verified', models.BooleanField(default=False)),
                ('profile_picture', models.URLField(blank=True, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('bio', models.TextField(blank=True, null=True)),
                ('privacy_settings', models.JSONField(blank=True, default=dict)),
                ('notification_preferences', models.JSONField(blank=True, default=dict)),
                ('face_recognition_consent', models.BooleanField(default=False)),
                ('marketing_consent', models.BooleanField(default=False)),
                ('facial_data', models.JSONField(blank=True, null=True)),
                ('oauth_token', models.TextField(blank=True, null=True)),
                ('oauth_token_secret', models.TextField(blank=True, null=True)),
                ('profile', models.JSONField(blank=True, null=True)),
                ('failed_login_attempts', models.PositiveIntegerField(default=0)),
                ('account_locked_until', models.DateTimeField(blank=True, null=True)),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('is_photofish_admin', models.BooleanField(default=False, help_text='Designates whether the user can access PhotoFish admin panel. Only assignable via Django admin interface for security.')),
                ('admin_permissions', models.JSONField(blank=True, default=dict, help_text='Specific admin permissions and access levels')),
                ('admin_notes', models.TextField(blank=True, help_text='Internal admin notes about this user (admin-only field)', null=True)),
                ('admin_assigned_at', models.DateTimeField(blank=True, help_text='When admin privileges were assigned', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_login_at', models.DateTimeField(blank=True, null=True)),
                ('admin_assigned_by', models.ForeignKey(blank=True, help_text='Admin user who assigned admin privileges', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admin_assignments', to=settings.AUTH_USER_MODEL)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'db_table': 'user',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='LoginHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('login_datetime', models.DateTimeField(auto_now_add=True)),
                ('logout_datetime', models.DateTimeField(blank=True, null=True)),
                ('is_successful', models.BooleanField(default=True)),
                ('failure_reason', models.CharField(blank=True, max_length=255, null=True)),
                ('session_key', models.CharField(blank=True, max_length=255, null=True)),
                ('jwt_token_id', models.CharField(blank=True, max_length=255, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('device_info', models.JSONField(blank=True, default=dict)),
                ('location_data', models.JSONField(blank=True, default=dict)),
                ('login_mode', models.CharField(choices=[('USER', 'Regular User'), ('PHOTOGRAPHER', 'Photographer'), ('ORGANIZER', 'Event Organizer'), ('BOTH', 'Both User and Photographer')], default='USER', max_length=20)),
                ('interface_type', models.CharField(choices=[('USER', 'Regular User'), ('PHOTOGRAPHER', 'Photographer'), ('ADMIN', 'Admin Panel')], default='USER', help_text='Which interface the user accessed', max_length=20)),
                ('login_method', models.CharField(choices=[('EMAIL', 'Email/Password'), ('GOOGLE', 'Google OAuth'), ('ADMIN_OVERRIDE', 'Admin Override')], default='EMAIL', max_length=20)),
                ('admin_action_count', models.IntegerField(default=0, help_text='Number of admin actions performed in this session')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='login_history', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'login_history',
                'ordering': ['-login_datetime'],
            },
        ),
        migrations.CreateModel(
            name='OTPVerification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('otp', models.CharField(max_length=6, validators=[django.core.validators.MinLengthValidator(6)])),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('attempts', models.IntegerField(default=0)),
                ('max_attempts', models.IntegerField(default=3)),
                ('purpose', models.CharField(default='email_verification', max_length=50)),
                ('delivery_method', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS')], default='email', max_length=10)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'otp_verification',
            },
        ),
        migrations.CreateModel(
            name='PasswordReset',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('token', models.CharField(max_length=255, unique=True)),
                ('is_used', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'password_reset',
            },
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email'], name='user_email_7bbb4c_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['account_status', 'is_active'], name='user_account_9e3db5_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['user_type', 'is_active'], name='user_user_ty_a3358f_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['is_photofish_admin'], name='user_is_phot_2be2e5_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['created_at'], name='user_created_855a50_idx'),
        ),
        migrations.AddIndex(
            model_name='loginhistory',
            index=models.Index(fields=['user', 'login_datetime'], name='login_histo_user_id_3fbe3c_idx'),
        ),
        migrations.AddIndex(
            model_name='loginhistory',
            index=models.Index(fields=['ip_address', 'login_datetime'], name='login_histo_ip_addr_1c2a41_idx'),
        ),
        migrations.AddIndex(
            model_name='loginhistory',
            index=models.Index(fields=['is_successful', 'login_datetime'], name='login_histo_is_succ_f9d62e_idx'),
        ),
        migrations.AddIndex(
            model_name='loginhistory',
            index=models.Index(fields=['interface_type', 'login_datetime'], name='login_histo_interfa_18d3ee_idx'),
        ),
        migrations.AddIndex(
            model_name='otpverification',
            index=models.Index(fields=['user', 'is_verified', 'expires_at'], name='otp_verific_user_id_bde8db_idx'),
        ),
        migrations.AddIndex(
            model_name='passwordreset',
            index=models.Index(fields=['token', 'is_used', 'expires_at'], name='password_re_token_d4aa23_idx'),
        ),
    ]
