# users/models.py - Clean UUID-Based Models for Fresh Start

from django.contrib.auth.models import AbstractUser
from django.core.validators import MinLengthValidator
from django.db import models
from django.utils import timezone
from datetime import timedelta
import uuid

# Constants for backward compatibility
OTP_PURPOSE_EMAIL_VERIFICATION = 'email_verification'
OTP_PURPOSE_PASSWORD_RESET = 'password_reset'
OTP_MAX_ATTEMPTS = 3

# User Type Constants
USER_TYPE_USER = 'USER'
USER_TYPE_PHOTOGRAPHER = 'PHOTOGRAPHER'
USER_TYPE_BOTH = 'BOTH'


class AccountStatus(models.TextChoices):
    ACTIVE = "ACTIVE", "Active"
    SUSPENDED = "SUSPENDED", "Suspended"
    DELETED = "DELETED", "Deleted"


class AuthProvider(models.TextChoices):
    EMAIL = "EMAIL", "Email"
    GOOGLE = "GOOGLE", "Google"
    FACEBOOK = "FACEBOOK", "Facebook"
    APPLE = "APPLE", "Apple"


class UserType(models.TextChoices):
    USER = "USER", "Regular User"
    PHOTOGRAPHER = "PHOTOGRAPHER", "Photographer"
    ORGANIZER = "ORGANIZER", "Event Organizer"
    BOTH = "BOTH", "Both User and Photographer"


class User(AbstractUser):
    """Enhanced User model with PhotoFish Admin capabilities - Clean UUID Implementation"""
    
    # Core identification - UUID from the start
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    
    # Account management
    account_status = models.CharField(
        max_length=20, 
        choices=AccountStatus.choices, 
        default=AccountStatus.ACTIVE
    )
    auth_provider = models.CharField(
        max_length=20, 
        choices=AuthProvider.choices, 
        default=AuthProvider.EMAIL
    )
    user_type = models.CharField(
        max_length=20, 
        choices=UserType.choices, 
        default=UserType.BOTH
    )
    
    # Current login mode (for existing functionality)
    current_login_mode = models.CharField(
        max_length=20, 
        choices=UserType.choices, 
        null=True, 
        blank=True
    )
    
    # Email verification
    is_email_verified = models.BooleanField(default=False)
    email_verification_token = models.CharField(max_length=255, blank=True, null=True)
    email_verification_sent_at = models.DateTimeField(null=True, blank=True)
    
    # Phone verification (existing functionality)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    is_phone_verified = models.BooleanField(default=False)
    
    # Profile information
    profile_picture = models.URLField(blank=True, null=True)
    date_of_birth = models.DateField(null=True, blank=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    
    # Privacy and preferences
    privacy_settings = models.JSONField(default=dict, blank=True)
    notification_preferences = models.JSONField(default=dict, blank=True)
    face_recognition_consent = models.BooleanField(default=False)
    marketing_consent = models.BooleanField(default=False)
    
    # Existing OAuth and facial recognition fields
    facial_data = models.JSONField(null=True, blank=True)
    oauth_token = models.TextField(null=True, blank=True)
    oauth_token_secret = models.TextField(null=True, blank=True)
    profile = models.JSONField(null=True, blank=True)
    
    # Security features (existing)
    failed_login_attempts = models.PositiveIntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    
    # PhotoFish Admin Capabilities
    is_photofish_admin = models.BooleanField(
        default=False,
        help_text="Designates whether the user can access PhotoFish admin panel. "
                  "Only assignable via Django admin interface for security."
    )
    admin_permissions = models.JSONField(
        default=dict,
        blank=True,
        help_text="Specific admin permissions and access levels"
    )
    admin_notes = models.TextField(
        blank=True,
        null=True,
        help_text="Internal admin notes about this user (admin-only field)"
    )
    admin_assigned_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='admin_assignments',
        help_text="Admin user who assigned admin privileges"
    )
    admin_assigned_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When admin privileges were assigned"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_at = models.DateTimeField(null=True, blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']
    
    class Meta:
        db_table = 'user'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['account_status', 'is_active']),
            models.Index(fields=['user_type', 'is_active']),
            models.Index(fields=['is_photofish_admin']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def display_name(self):
        return self.full_name if self.full_name else self.username
    
    # Existing methods for compatibility
    def get_full_name(self):
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username
    
    def has_role(self, role):
        """Check if user has a specific role"""
        return self.user_type == role or self.user_type == USER_TYPE_BOTH
    
    def is_account_locked(self):
        """Check if account is currently locked"""
        return (self.account_locked_until and 
                timezone.now() < self.account_locked_until)
    
    def lock_account(self):
        """Lock account for 30 minutes after failed login attempts"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:
            self.account_locked_until = timezone.now() + timedelta(minutes=30)
        self.save(update_fields=['failed_login_attempts', 'account_locked_until'])
    
    def reset_failed_login(self):
        """Reset failed login attempts on successful login"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        self.save(update_fields=['failed_login_attempts', 'account_locked_until'])
    
    # Admin methods
    def has_admin_permission(self, permission):
        """Check if user has specific admin permission"""
        if not self.is_photofish_admin:
            return False
        return self.admin_permissions.get(permission, False)
    
    def assign_admin_privileges(self, assigned_by_user, permissions=None, notes=None):
        """Assign admin privileges to user (should only be called from Django admin)"""
        self.is_photofish_admin = True
        self.admin_assigned_by = assigned_by_user
        self.admin_assigned_at = timezone.now()
        
        if permissions:
            self.admin_permissions = permissions
        else:
            # Default admin permissions
            self.admin_permissions = {
                'user_management': True,
                'event_oversight': True,
                'queue_monitoring': True,
                'subscription_assignment': True,
                'financial_oversight': False,  # Requires explicit assignment
                'system_administration': False  # Requires explicit assignment
            }
        
        if notes:
            self.admin_notes = notes
        
        self.save()
    
    def revoke_admin_privileges(self, revoked_by_user):
        """Revoke admin privileges from user"""
        self.is_photofish_admin = False
        self.admin_permissions = {}
        self.admin_notes = f"Admin privileges revoked by {revoked_by_user.email} on {timezone.now()}"
        self.save()
    
    def can_access_admin_panel(self):
        """Check if user can access PhotoFish admin panel"""
        return self.is_photofish_admin and self.account_status == AccountStatus.ACTIVE
    
    def get_interface_options(self):
        """Get available interface options for user after login"""
        options = []
        
        # Always available - Regular User Interface
        options.append({
            'type': 'user',
            'name': 'Regular User Interface',
            'description': 'Access events as attendee, view photos, make purchases'
        })
        
        # Always available - Photographer Interface  
        options.append({
            'type': 'photographer',
            'name': 'Photographer Interface',
            'description': 'Manage photography business, upload photos, track earnings'
        })
        
        # Admin-only option
        if self.can_access_admin_panel():
            options.append({
                'type': 'admin',
                'name': 'PhotoFish Admin Panel',
                'description': 'Platform administration, user management, system oversight'
            })
        
        return options


class LoginHistory(models.Model):
    """Enhanced login history with admin tracking - Clean UUID Implementation"""
    
    # UUID from the start
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='login_history')
    
    # Login details
    login_datetime = models.DateTimeField(auto_now_add=True)
    logout_datetime = models.DateTimeField(null=True, blank=True)
    is_successful = models.BooleanField(default=True)
    failure_reason = models.CharField(max_length=255, blank=True, null=True)
    
    # Session tracking
    session_key = models.CharField(max_length=255, blank=True, null=True)
    jwt_token_id = models.CharField(max_length=255, blank=True, null=True)
    
    # Device and location
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True, null=True)
    device_info = models.JSONField(default=dict, blank=True)
    location_data = models.JSONField(default=dict, blank=True)
    
    # Existing compatibility fields
    login_mode = models.CharField(
        max_length=20,
        choices=UserType.choices,
        default=USER_TYPE_USER
    )
    
    # Interface type tracking
    interface_type = models.CharField(
        max_length=20,
        choices=[
            ('USER', 'Regular User'),
            ('PHOTOGRAPHER', 'Photographer'),
            ('ADMIN', 'Admin Panel')
        ],
        default='USER',
        help_text="Which interface the user accessed"
    )
    login_method = models.CharField(
        max_length=20,
        choices=[
            ('EMAIL', 'Email/Password'),
            ('GOOGLE', 'Google OAuth'),
            ('ADMIN_OVERRIDE', 'Admin Override')
        ],
        default='EMAIL'
    )
    
    # Admin-specific tracking
    admin_action_count = models.IntegerField(
        default=0,
        help_text="Number of admin actions performed in this session"
    )
    
    class Meta:
        db_table = 'login_history'
        indexes = [
            models.Index(fields=['user', 'login_datetime']),
            models.Index(fields=['ip_address', 'login_datetime']),
            models.Index(fields=['is_successful', 'login_datetime']),
            models.Index(fields=['interface_type', 'login_datetime']),
        ]
        ordering = ['-login_datetime']
    
    def __str__(self):
        return f"{self.user.email} - {self.login_datetime} ({self.interface_type})"


class OTPVerification(models.Model):
    """OTP verification for email and phone verification"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    otp = models.CharField(max_length=6, validators=[MinLengthValidator(6)])
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    attempts = models.IntegerField(default=0)
    max_attempts = models.IntegerField(default=3)
    purpose = models.CharField(max_length=50, default=OTP_PURPOSE_EMAIL_VERIFICATION)
    
    # Enhanced fields
    delivery_method = models.CharField(
        max_length=10, 
        choices=[('email', 'Email'), ('sms', 'SMS')], 
        default='email'
    )
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        db_table = "otp_verification"
        indexes = [models.Index(fields=["user", "is_verified", "expires_at"])]

    def __str__(self):
        return f"OTP for {self.user.email} - {self.purpose}"
    
    def is_expired(self):
        """Check if OTP has expired"""
        return timezone.now() > self.expires_at
    
    def is_max_attempts_reached(self):
        """Check if maximum attempts have been reached"""
        return self.attempts >= self.max_attempts


class PasswordReset(models.Model):
    """Password reset token management"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    token = models.CharField(max_length=255, unique=True)
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        db_table = "password_reset"
        indexes = [models.Index(fields=["token", "is_used", "expires_at"])]

    def __str__(self):
        return f"Password reset for {self.user.email}"
    
    def is_expired(self):
        """Check if reset token has expired"""
        return timezone.now() > self.expires_at