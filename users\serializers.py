# users/serializers.py - Enhanced Serializers with Admin Support

from rest_framework import serializers
from .models import User, LoginHistory


class UserSerializer(serializers.ModelSerializer):
    """Basic user serializer for authentication responses"""
    
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    admin_access = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name', 'display_name',
            'user_type', 'account_status', 'is_email_verified', 'profile_picture',
            'created_at', 'last_login_at', 'admin_access'
        ]
        read_only_fields = ['id', 'created_at', 'last_login_at']
    
    def get_admin_access(self, obj):
        """Check if user has admin panel access"""
        return obj.can_access_admin_panel()


class UserProfileSerializer(serializers.ModelSerializer):
    """Detailed user profile serializer including admin information"""
    
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    admin_access = serializers.SerializerMethodField()
    admin_permissions = serializers.SerializerMethodField()
    interface_options = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name', 'display_name',
            'user_type', 'account_status', 'auth_provider', 'is_email_verified',
            'profile_picture', 'phone_number', 'date_of_birth', 'location', 'bio',
            'privacy_settings', 'notification_preferences', 'face_recognition_consent',
            'marketing_consent', 'created_at', 'updated_at', 'last_login_at',
            'admin_access', 'admin_permissions', 'interface_options'
        ]
        read_only_fields = [
            'id', 'email', 'user_type', 'account_status', 'auth_provider',
            'is_email_verified', 'created_at', 'updated_at', 'last_login_at',
            'admin_access', 'admin_permissions', 'interface_options'
        ]
    
    def get_admin_access(self, obj):
        """Check if user has admin panel access"""
        return obj.can_access_admin_panel()
    
    def get_admin_permissions(self, obj):
        """Get admin permissions if user is admin, otherwise None"""
        if obj.can_access_admin_panel():
            return obj.admin_permissions
        return None
    
    def get_interface_options(self, obj):
        """Get available interface options for admin users"""
        if obj.can_access_admin_panel():
            return obj.get_interface_options()
        return None


class AdminUserSerializer(serializers.ModelSerializer):
    """Admin-only serializer with full user details including sensitive fields"""
    
    full_name = serializers.ReadOnlyField()
    display_name = serializers.ReadOnlyField()
    admin_assigned_by_email = serializers.CharField(source='admin_assigned_by.email', read_only=True)
    total_logins = serializers.SerializerMethodField()
    last_login_info = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name', 'display_name',
            'user_type', 'account_status', 'auth_provider', 'is_active', 'is_email_verified',
            'profile_picture', 'phone_number', 'date_of_birth', 'location', 'bio',
            'privacy_settings', 'notification_preferences', 'face_recognition_consent',
            'marketing_consent', 'is_photofish_admin', 'admin_permissions', 'admin_notes',
            'admin_assigned_by_email', 'admin_assigned_at', 'created_at', 'updated_at',
            'last_login_at', 'total_logins', 'last_login_info'
        ]
        read_only_fields = [
            'id', 'username', 'created_at', 'updated_at', 'admin_assigned_by_email',
            'total_logins', 'last_login_info'
        ]
    
    def get_total_logins(self, obj):
        """Get total number of successful logins"""
        return obj.login_history.filter(is_successful=True).count()
    
    def get_last_login_info(self, obj):
        """Get detailed last login information"""
        last_login = obj.login_history.filter(is_successful=True).first()
        if last_login:
            return {
                'datetime': last_login.login_datetime,
                'ip_address': last_login.ip_address,
                'interface_type': last_login.interface_type,
                'login_method': last_login.login_method
            }
        return None


class LoginHistorySerializer(serializers.ModelSerializer):
    """Login history serializer for admin monitoring"""
    
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.full_name', read_only=True)
    session_duration = serializers.SerializerMethodField()
    
    class Meta:
        model = LoginHistory
        fields = [
            'id', 'user_email', 'user_name', 'login_datetime', 'logout_datetime',
            'is_successful', 'failure_reason', 'session_key', 'ip_address',
            'user_agent', 'interface_type', 'login_method', 'admin_action_count',
            'session_duration'
        ]
        read_only_fields = '__all__'
    
    def get_session_duration(self, obj):
        """Calculate session duration if logout time is available"""
        if obj.logout_datetime:
            duration = obj.logout_datetime - obj.login_datetime
            return str(duration)
        return None


class AdminPermissionSerializer(serializers.Serializer):
    """Serializer for admin permission management"""
    
    user_id = serializers.UUIDField()
    permissions = serializers.JSONField()
    notes = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    
    def validate_permissions(self, value):
        """Validate admin permissions structure"""
        valid_permissions = [
            'user_management',
            'event_oversight',
            'queue_monitoring',
            'subscription_assignment',
            'financial_oversight',
            'system_administration'
        ]
        
        if not isinstance(value, dict):
            raise serializers.ValidationError("Permissions must be a dictionary")
        
        for permission in value.keys():
            if permission not in valid_permissions:
                raise serializers.ValidationError(
                    f"Invalid permission: {permission}. "
                    f"Valid permissions: {', '.join(valid_permissions)}"
                )
        
        return value


class InterfaceOptionSerializer(serializers.Serializer):
    """Serializer for user interface options"""
    
    type = serializers.CharField()
    name = serializers.CharField()
    description = serializers.CharField()


class AdminDashboardStatsSerializer(serializers.Serializer):
    """Serializer for admin dashboard statistics"""
    
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    total_admins = serializers.IntegerField()
    users_last_24h = serializers.IntegerField()
    successful_logins_today = serializers.IntegerField()
    failed_logins_today = serializers.IntegerField()
    photographer_count = serializers.IntegerField()
    organizer_count = serializers.IntegerField()
    
    # System health
    system_status = serializers.CharField()
    database_status = serializers.CharField()
    queue_status = serializers.CharField()
    
    # Recent activity
    recent_registrations = AdminUserSerializer(many=True)
    recent_logins = LoginHistorySerializer(many=True)


class UserManagementActionSerializer(serializers.Serializer):
    """Serializer for user management actions"""

    ACTION_CHOICES = [
        ('activate', 'Activate Account'),
        ('suspend', 'Suspend Account'),
        ('delete', 'Delete Account'),
        ('verify_email', 'Verify Email'),
        ('reset_password', 'Reset Password'),
        ('assign_admin', 'Assign Admin Privileges'),
        ('revoke_admin', 'Revoke Admin Privileges'),
    ]

    user_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1,
        max_length=100
    )
    action = serializers.ChoiceField(choices=ACTION_CHOICES)
    reason = serializers.CharField(max_length=500, required=False, allow_blank=True)

    # For admin assignment
    admin_permissions = serializers.JSONField(required=False)

    def validate(self, data):
        """Validate action-specific requirements"""
        action = data.get('action')

        if action == 'assign_admin' and not data.get('admin_permissions'):
            raise serializers.ValidationError(
                "Admin permissions are required when assigning admin privileges"
            )

        return data


# Authentication and OTP Serializers
class OTPVerificationSerializer(serializers.Serializer):
    """Serializer for OTP verification"""

    email = serializers.EmailField()
    otp_code = serializers.CharField(max_length=6, min_length=6)

    def validate_otp_code(self, value):
        """Validate OTP code format"""
        if not value.isdigit():
            raise serializers.ValidationError("OTP code must contain only digits")
        return value


class RequestPasswordResetSerializer(serializers.Serializer):
    """Serializer for requesting password reset"""

    email = serializers.EmailField()

    def validate_email(self, value):
        """Validate that user exists with this email"""
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("No user found with this email address")
        return value


class VerifyPasswordResetTokenSerializer(serializers.Serializer):
    """Serializer for verifying password reset token"""

    email = serializers.EmailField()
    token = serializers.CharField(max_length=255)

    def validate(self, data):
        """Validate email and token combination"""
        email = data.get('email')
        token = data.get('token')

        try:
            user = User.objects.get(email=email)
            # Add token validation logic here if needed
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid email or token")

        return data


class PasswordResetSerializer(serializers.Serializer):
    """Serializer for password reset"""

    email = serializers.EmailField()
    token = serializers.CharField(max_length=255)
    new_password = serializers.CharField(min_length=8, max_length=128)
    confirm_password = serializers.CharField(min_length=8, max_length=128)

    def validate(self, data):
        """Validate password reset data"""
        new_password = data.get('new_password')
        confirm_password = data.get('confirm_password')

        if new_password != confirm_password:
            raise serializers.ValidationError("Passwords do not match")

        email = data.get('email')
        token = data.get('token')

        try:
            user = User.objects.get(email=email)
            # Add token validation logic here if needed
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid email or token")

        return data

    def validate_new_password(self, value):
        """Validate password strength"""
        if len(value) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters long")

        # Add more password validation rules as needed
        return value