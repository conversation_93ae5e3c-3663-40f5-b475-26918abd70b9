import logging
import random
import string
import uuid
import re
from datetime import datetime, timed<PERSON>ta
from typing import Op<PERSON>, <PERSON><PERSON>, Dict

from django.core.mail import EmailMultiAlternatives, send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from rest_framework_simplejwt.tokens import RefreshToken

from .models import User, OTPVerification, LoginHistory, PasswordReset
from .constants import (
    OTP_LENGTH, OTP_EXPIRY, OTP_MAX_ATTEMPTS,
    EMAIL_SUBJECT_OTP, OTP_PURPOSE_EMAIL_VERIFICATION, OTP_PURPOSE_PHONE_VERIFICATION,
    EMAIL_SUBJECT_PASSWORD_RESET, OTP_EXPIRY_MINUTES, SMS_SUBJECT_OTP
)

logger = logging.getLogger('users')


def get_client_ip(request):
    """Extract client IP address from request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """Extract user agent from request."""
    return request.META.get('HTTP_USER_AGENT', '')


def generate_and_send_otp(user: User, purpose: str = OTP_PURPOSE_EMAIL_VERIFICATION, ip_address: str = None) -> bool:
    """Enhanced OTP generation with phone support and IP tracking"""
    try:
        otp = ''.join(random.choices(string.digits, k=OTP_LENGTH))
        expiry = datetime.now() + OTP_EXPIRY
        
        # Determine delivery method based on purpose
        delivery_method = 'sms' if purpose == OTP_PURPOSE_PHONE_VERIFICATION else 'email'

        OTPVerification.objects.create(
            user=user,
            otp=otp,
            expires_at=expiry,
            purpose=purpose,
            delivery_method=delivery_method,
            ip_address=ip_address,
            max_attempts=OTP_MAX_ATTEMPTS
        )

        if delivery_method == 'email':
            return _send_email_otp(user, otp)
        else:
            return _send_sms_otp(user, otp)

    except Exception as e:
        logger.error(f"Failed to generate and send OTP to user {user.id}: {str(e)}", exc_info=True)
        raise


def _send_email_otp(user: User, otp: str) -> bool:
    """Send OTP via email using existing template"""
    try:
        context = {
            'user_name': user.first_name or user.username,
            'otp_code': otp,
            'expiry_minutes': OTP_EXPIRY_MINUTES,
            'logo_url': getattr(settings, 'PHOTOFISH_LOGO_URL', '')
        }

        html_content = render_to_string('emails/otp_verification.html', context)
        text_content = f"""
Hello {user.first_name or user.username},

Your PhotoFish email verification code is: {otp}

This OTP will expire in {OTP_EXPIRY_MINUTES} minutes.

If you didn't request this verification, please ignore this email.

Best regards,
PhotoFish Team
        """

        msg = EmailMultiAlternatives(
            subject=EMAIL_SUBJECT_OTP,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email]
        )
        msg.attach_alternative(html_content, "text/html")
        msg.send(fail_silently=False)

        logger.info(f"Email OTP sent to user: {user.id} - {user.email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email OTP to user {user.id}: {str(e)}", exc_info=True)
        return False


def _send_sms_otp(user: User, otp: str) -> bool:
    """Send OTP via SMS - Fast2SMS for India with development fallback"""
    try:
        if not user.phone_number:
            logger.error(f"No phone number for user {user.id}")
            return False

        message = f"Your PhotoFish verification code is: {otp}. Valid for {OTP_EXPIRY_MINUTES} minutes."
        
        # Try Fast2SMS first, then development mode
        if _send_sms_via_fast2sms(user.phone_number, message, otp):
            return True
        else:
            # Fallback to development mode
            logger.info(f"Fast2SMS failed for user {user.id}, using development mode")
            return _send_sms_via_development(user.phone_number, message, otp)
        
    except Exception as e:
        logger.error(f"Failed to send SMS OTP to user {user.id}: {str(e)}", exc_info=True)
        # Even if SMS fails, show OTP in development
        _send_sms_via_development(user.phone_number, message, otp)
        return True  # Return True to not block authentication flow


def _send_sms_via_fast2sms(phone_number: str, message: str, otp: str = None) -> bool:
    """Send SMS via Fast2SMS (Indian service) - Fixed version"""
    try:
        import requests
        
        # Check if Fast2SMS is configured
        api_key = getattr(settings, 'FAST2SMS_API_KEY', None)
        if not api_key or api_key == 'YOUR_API_KEY_HERE':
            logger.info("Fast2SMS API key not configured, skipping...")
            return False
        
        # Clean Indian phone number (remove +91 for Fast2SMS)
        clean_number = _clean_indian_phone_number_for_fast2sms(phone_number)
        
        # Ensure all parameters are strings
        clean_number = str(clean_number)
        message = str(message)
        api_key = str(api_key)
        
        logger.info(f"Attempting Fast2SMS to {clean_number}")
        
        # Fast2SMS API endpoint
        url = "https://www.fast2sms.com/dev/bulkV2"
        
        # Prepare headers
        headers = {
            'authorization': api_key,
            'Content-Type': "application/x-www-form-urlencoded",
            'Cache-Control': "no-cache",
        }
        
        # Prepare payload - ensure all values are strings
        payload = {
            'authorization': api_key,
            'message': message,
            'language': 'english',
            'route': str(getattr(settings, 'FAST2SMS_ROUTE', 'q')),
            'numbers': clean_number,
        }
        
        # Make API call
        response = requests.post(url, data=payload, headers=headers, timeout=15)
        
        # Check if response is valid JSON
        try:
            result = response.json()
        except ValueError:
            logger.error(f"Fast2SMS returned invalid JSON: {response.text}")
            print(f"Fast2SMS error: Invalid response format")
            return False
        
        # Check response
        if result.get('return') and result.get('request_id'):
            logger.info(f"SMS sent via Fast2SMS to {clean_number} - Request ID: {result.get('request_id')}")
            print(f"SMS SENT to {phone_number} via Fast2SMS")
            if otp:
                print(f"OTP: {otp} sent to {phone_number}")
            return True
        else:
            error_msg = result.get('message', 'Unknown error')
            logger.warning(f"Fast2SMS failed: {error_msg}")
            print(f"Fast2SMS failed: {error_msg}")
            
            # Handle common Fast2SMS errors
            if 'authentication' in error_msg.lower():
                print("Fast2SMS: Invalid API key - check your API key in settings")
            elif 'insufficient' in error_msg.lower():
                print("Fast2SMS: Insufficient balance")
            elif 'invalid' in error_msg.lower():
                print(f"Fast2SMS: Invalid phone number: {clean_number}")
            elif 'authorization' in error_msg.lower():
                print("Fast2SMS: Authorization failed - check API key format")
            
            return False
            
    except requests.exceptions.Timeout:
        logger.error("Fast2SMS request timeout")
        print("Fast2SMS timeout - network issue")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Fast2SMS request failed: {str(e)}")
        print(f"Fast2SMS network error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Fast2SMS failed: {str(e)}")
        print(f"Fast2SMS error: {str(e)}")
        return False


def _clean_indian_phone_number_for_fast2sms(phone_number: str) -> str:
    """Clean Indian phone number for Fast2SMS (removes +91)"""
    import re
    
    # Remove all non-digit characters
    clean = re.sub(r'[^\d]', '', phone_number)
    
    # Handle Indian numbers - Fast2SMS expects 10-digit numbers without country code
    if clean.startswith('91') and len(clean) >= 12:
        # Remove country code: ************ -> 9876543210
        return clean[2:]
    elif clean.startswith('0') and len(clean) == 11:
        # Remove leading 0: 09876543210 -> 9876543210
        return clean[1:]
    elif len(clean) == 10:
        # Already 10-digit: 9876543210 -> 9876543210
        return clean
    else:
        # Return last 10 digits if longer
        return clean[-10:] if len(clean) > 10 else clean


def _send_sms_via_development(phone_number: str, message: str, otp: str = None) -> bool:
    """Development SMS - display OTP prominently"""
    try:
        print(f"\n{'='*60}")
        print(f"DEVELOPMENT SMS to {phone_number}")
        print(f"Message: {message}")
        if otp:
            print(f"OTP CODE: {otp}")
        print(f"{'='*60}\n")
        
        logger.info(f"DEVELOPMENT SMS to {phone_number}: OTP={otp}")
        return True
    except Exception as e:
        logger.error(f"Development SMS logging failed: {str(e)}")
        return False


def verify_user_otp(user: User, otp: str, ip_address: str = None) -> Tuple[bool, Optional[str], OTPVerification]:
    """Enhanced OTP verification with security tracking"""
    verification = OTPVerification.objects.filter(
        user=user,
        is_verified=False,
        expires_at__gt=datetime.now()
    ).order_by('-created_at').first()

    if not verification:
        return False, "No active OTP found", None

    if verification.attempts >= verification.max_attempts:
        return False, "Maximum attempts exceeded. Please request a new OTP.", verification

    if verification.otp != otp:
        verification.attempts += 1
        verification.save()
        return False, "Invalid OTP", verification

    verification.is_verified = True
    verification.save()
    
    # Mark phone as verified if this was phone verification
    if verification.purpose == OTP_PURPOSE_PHONE_VERIFICATION:
        user.is_phone_verified = True
        user.save(update_fields=['is_phone_verified'])
    
    return True, None, verification


def record_login_history(user: User, request, login_mode: str, is_successful: bool = True) -> bool:
    """Enhanced login history with success/failure tracking"""
    try:
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        LoginHistory.objects.create(
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            login_mode=login_mode,
            is_successful=is_successful
        )
        
        # Update user's last login IP
        if is_successful:
            user.last_login_ip = ip_address
            user.save(update_fields=['last_login_ip'])
            
        return True
    except Exception as e:
        logger.error(f"Failed to record login history for user {user.id}: {str(e)}")
        return False


def increment_failed_attempts(user, request):
    """Increment failed login attempts and lock account if threshold reached."""
    user.failed_login_attempts += 1
    
    # Log the failed attempt with IP and user agent
    client_ip = get_client_ip(request)
    user_agent = get_user_agent(request)
    
    logger.warning(
        f"Failed login attempt #{user.failed_login_attempts} for user {user.id} "
        f"from IP: {client_ip} - User Agent: {user_agent[:100]}"
    )
    
    # Lock account after 5 failed attempts for 15 minutes
    if user.failed_login_attempts >= 5:
        user.account_locked_until = timezone.now() + timedelta(minutes=15)
        logger.warning(f"Account locked for user {user.id} until {user.account_locked_until}")
    
    user.save()


def reset_failed_attempts(user):
    """Reset failed login attempts after successful login."""
    if user.failed_login_attempts > 0:
        logger.info(f"Resetting failed attempts for user {user.id} (was {user.failed_login_attempts})")
        user.failed_login_attempts = 0
        user.account_locked_until = None
        user.save()


def check_account_lockout(user):
    """Check if account is currently locked."""
    if user.account_locked_until:
        if timezone.now() < user.account_locked_until:
            return True
        else:
            # Lockout period expired, clear it
            user.account_locked_until = None
            user.failed_login_attempts = 0
            user.save()
            logger.info(f"Account lockout expired for user {user.id}")
    return False


def lock_account_temporarily(user, duration_minutes=15):
    """Manually lock account for specified duration."""
    user.account_locked_until = timezone.now() + timedelta(minutes=duration_minutes)
    user.save()
    logger.warning(f"Account manually locked for user {user.id} for {duration_minutes} minutes")


def generate_sms_otp():
    """Generate a 6-digit OTP for SMS."""
    return ''.join(random.choices(string.digits, k=6))


def send_sms_otp(user):
    """Send SMS OTP to user's phone number with Fast2SMS"""
    try:
        if not user.phone_number:
            return False, "No phone number associated with account"
        
        # Generate OTP
        otp_code = generate_sms_otp()
        
        # Store OTP in cache with 5-minute expiration
        cache_key = f"sms_otp_{user.id}"
        cache.set(cache_key, otp_code, timeout=300)  # 5 minutes
        
        # Send SMS using Fast2SMS
        success = _send_sms_otp(user, otp_code)
        
        if success:
            logger.info(f"SMS OTP sent to {user.phone_number} for user {user.id}")
            return True, "SMS sent successfully"
        else:
            return False, "Failed to send SMS"
        
    except Exception as e:
        logger.error(f"Failed to send SMS OTP to {user.phone_number}: {str(e)}")
        return False, f"Failed to send SMS: {str(e)}"


def verify_phone_otp(user, otp_code):
    """Verify SMS OTP code."""
    try:
        cache_key = f"sms_otp_{user.id}"
        stored_otp = cache.get(cache_key)
        
        if not stored_otp:
            logger.warning(f"Phone OTP verification failed - no OTP found for user {user.id}")
            return False, "OTP expired or not found"
        
        if stored_otp != otp_code:
            logger.warning(f"Phone OTP verification failed - invalid OTP for user {user.id}")
            return False, "Invalid OTP"
        
        # OTP is valid, remove it from cache
        cache.delete(cache_key)
        
        logger.info(f"Phone OTP verified successfully for user {user.id}")
        return True, "OTP verified successfully"
        
    except Exception as e:
        logger.error(f"Error verifying phone OTP for user {user.id}: {str(e)}")
        return False, "Error verifying OTP"


def get_tokens_for_user(user: User) -> Dict[str, str]:
    """Generate JWT tokens with user data"""
    refresh = RefreshToken.for_user(user)
    refresh['email'] = user.email
    refresh['username'] = user.username
    refresh['user_type'] = user.user_type
    refresh['login_mode'] = user.current_login_mode

    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token)
    }


def generate_password_reset_token(user: User) -> Tuple[bool, Optional[str]]:
    """Generate password reset token and send email"""
    try:
        token = str(uuid.uuid4())
        expiry = timezone.now() + timedelta(hours=24)

        PasswordReset.objects.create(
            user=user,
            token=token,
            expires_at=expiry
        )

        # Use proper frontend URL from settings
        frontend_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
        reset_url = f"{frontend_url}/reset-password?token={token}"
        
        email_message = f"""
Hello {user.first_name or user.username},

You requested a password reset for your PhotoFish account.

Please use the following link to reset your password:
{reset_url}

This link will expire in 24 hours.

If you didn't request this reset, please ignore this email.

Best regards,
PhotoFish Team
        """

        send_mail(
            EMAIL_SUBJECT_PASSWORD_RESET,
            email_message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            fail_silently=False,
        )

        logger.info(f"Password reset token generated for user: {user.id} - {user.email}")
        return True, token
    except Exception as e:
        logger.error(f"Failed to generate password reset token for user {user.id}: {str(e)}", exc_info=True)
        return False, None


def verify_password_reset_token(token: str) -> Tuple[bool, Optional[str], Optional[PasswordReset]]:
    """Verify password reset token"""
    try:
        reset_request = PasswordReset.objects.filter(
            token=token,
            is_used=False,
            expires_at__gt=timezone.now()
        ).first()

        if not reset_request:
            return False, "Invalid or expired token", None

        return True, None, reset_request
    except Exception as e:
        logger.error(f"Error verifying password reset token: {str(e)}", exc_info=True)
        return False, "Error verifying token", None


def reset_password(reset_request: PasswordReset, new_password: str) -> Tuple[bool, Optional[str]]:
    """Reset user password and mark token as used"""
    try:
        user = reset_request.user
        user.set_password(new_password)
        user.save()

        reset_request.is_used = True
        reset_request.save()

        logger.info(f"Password reset successful for user: {user.id} - {user.email}")
        return True, None
    except Exception as e:
        logger.error(f"Error resetting password: {str(e)}", exc_info=True)
        return False, "Error resetting password"


def can_request_otp(user: User, ip_address: str) -> Tuple[bool, Optional[str]]:
    """Check if user can request a new OTP (rate limiting)"""
    try:
        # Check if user has an active OTP
        active_otp = OTPVerification.objects.filter(
            user=user,
            is_verified=False,
            expires_at__gt=timezone.now()
        ).order_by('-created_at').first()
        
        if active_otp:
            # Check if they can resend (2 minute cooldown)
            time_since_last = timezone.now() - active_otp.created_at
            if time_since_last < timedelta(minutes=2):
                remaining = 2 - time_since_last.total_seconds() / 60
                return False, f"Please wait {remaining:.0f} minute(s) before requesting a new OTP"
        
        # Check IP-based rate limiting (max 5 OTPs per hour per IP)
        recent_otps = OTPVerification.objects.filter(
            ip_address=ip_address,
            created_at__gt=timezone.now() - timedelta(hours=1)
        ).count()
        
        if recent_otps >= 5:
            return False, "Too many OTP requests from this location. Please try again later."
        
        return True, None
        
    except Exception as e:
        logger.error(f"Error checking OTP rate limit: {str(e)}")
        return False, "Error checking request limits"


def validate_phone_number_format(phone_number):
    """Validate international phone number format."""
    # Remove any spaces or special characters except +
    cleaned = re.sub(r'[^\+\d]', '', phone_number)
    
    # Check international format: +[country code][number]
    pattern = r'^\+[1-9]\d{1,14}$'
    
    if not re.match(pattern, cleaned):
        return False, "Phone number must be in international format (e.g., +1234567890)"
    
    return True, cleaned


def normalize_phone_number(phone_number):
    """Normalize phone number to standard format."""
    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\+\d]', '', phone_number)
    
    # Ensure it starts with +
    if not cleaned.startswith('+'):
        # Assume US number if no country code
        if len(cleaned) == 10:
            cleaned = '+1' + cleaned
        else:
            cleaned = '+' + cleaned
    
    return cleaned


def handle_login_attempt(user: User, request, is_successful: bool, login_mode: str) -> bool:
    """Handle login attempt with security features"""
    try:
        # Record login history
        record_login_history(user, request, login_mode, is_successful)
        
        if is_successful:
            # Reset failed attempts on successful login
            reset_failed_attempts(user)
            logger.info(f"Successful login for user {user.id}")
            return True
        else:
            # Increment failed attempts
            increment_failed_attempts(user, request)
            logger.warning(f"Failed login attempt for user {user.id}. Attempts: {user.failed_login_attempts}")
            return False
            
    except Exception as e:
        logger.error(f"Error handling login attempt for user {user.id}: {str(e)}")
        return False