import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

from .models import User, LoginHistory, OTPVerification

logger = logging.getLogger('users')


@receiver(post_save, sender=User)
def user_created_handler(sender, instance, created, **kwargs):
    if created:
        logger.info(f"New user created via signal: {instance.id} - {instance.email}")


@receiver(pre_save, sender=User)
def user_updated_handler(sender, instance, **kwargs):
    if instance.id:
        try:
            old_instance = User.objects.get(id=instance.id)
            if old_instance.current_login_mode != instance.current_login_mode:
                logger.info(f"User {instance.id} login mode changed from "
                            f"{old_instance.current_login_mode} to {instance.current_login_mode}")
            if not old_instance.is_email_verified and instance.is_email_verified:
                logger.info(f"User {instance.id} email verified")
        except User.DoesNotExist:
            pass


@receiver(post_save, sender=LoginHistory)
def login_history_created_handler(sender, instance, created, **kwargs):
    if created and instance.is_successful:
        user = instance.user
        user.last_login = timezone.now()
        user.save(update_fields=['last_login'])
        logger.debug(f"Updated last_login for user {user.id} after successful login")


@receiver(post_save, sender=OTPVerification)
def otp_created_handler(sender, instance, created, **kwargs):
    if created:
        OTPVerification.objects.filter(
            user=instance.user,
            purpose=instance.purpose,
            is_verified=False
        ).exclude(id=instance.id).update(
            expires_at=timezone.now()
        )
        logger.debug(f"Expired old OTPs for user {instance.user.id}")
