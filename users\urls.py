# users/urls.py - Updated to include Phase 4A admin endpoints

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import UserViewSet
from . import admin_views

router = DefaultRouter()
router.register(r'', UserViewSet, basename='user')

urlpatterns = [
    path('', include(router.urls)),
    
    # Enhanced Email Authentication (existing)
    path('auth/register/', UserViewSet.as_view({'post': 'create'}), name='user-register'),
    path('auth/login/', UserViewSet.as_view({'post': 'login'}), name='user-login'),
    path('auth/verify-otp/<uuid:pk>/', UserViewSet.as_view({'post': 'verify_otp'}), name='verify-otp'),
    path('auth/resend-otp/<uuid:pk>/', UserViewSet.as_view({'post': 'resend_otp'}), name='resend-otp'),
    
    # NEW: Admin interface selection for Phase 4A
    path('auth/select-interface/', UserViewSet.as_view({'post': 'select_admin_interface'}), name='select-interface'),
    
    # Phone Authentication Endpoints (existing - keeping for compatibility)
    path('auth/register-phone/', UserViewSet.as_view({'post': 'register_phone'}), name='user-register-phone'),
    path('auth/login-phone/', UserViewSet.as_view({'post': 'login_phone'}), name='user-login-phone'),
    path('auth/verify-phone-otp/<uuid:pk>/', UserViewSet.as_view({'post': 'verify_phone_otp'}), name='verify-phone-otp'),
    
    # OAuth Authentication (existing)
    path('auth/oauth/login/', UserViewSet.as_view({'post': 'oauth_login'}), name='oauth-login'),
    
    # User Management (existing)
    path('auth/check-login-eligibility/', UserViewSet.as_view({'post': 'check_login_eligibility'}), name='check-login-eligibility'),
    path('auth/switch-login-mode/<uuid:pk>/', UserViewSet.as_view({'post': 'switch_login_mode'}), name='switch-login-mode'),
    
    # Password Reset (existing)
    path('auth/forgot-password/', UserViewSet.as_view({'post': 'request_password_reset'}), name='forgot-password'),
    path('auth/verify-reset-token/', UserViewSet.as_view({'post': 'verify_reset_token'}), name='verify-reset-token'),
    path('auth/reset-password/', UserViewSet.as_view({'post': 'reset_password_confirm'}), name='reset-password'),
    
    # NEW: PhotoFish Admin API Endpoints (Phase 4A)
    path('admin/dashboard/', admin_views.admin_dashboard_overview, name='admin-dashboard'),
    path('admin/users/', admin_views.admin_users_list, name='admin-users-list'),
    path('admin/users/<uuid:user_id>/', admin_views.admin_user_detail, name='admin-user-detail'),
    path('admin/users/actions/', admin_views.admin_user_actions, name='admin-user-actions'),
    path('admin/events/', admin_views.admin_events_overview, name='admin-events-overview'),
    path('admin/login-monitoring/', admin_views.admin_login_monitoring, name='admin-login-monitoring'),
]