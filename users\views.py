# users/views.py - Updated for Phase 4A with Admin Support - OTP Permission Fix

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import authenticate
from django.db import transaction

from rest_framework import viewsets, status
from rest_framework.decorators import api_view, action, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.request import Request
from django_ratelimit.decorators import ratelimit
from django.utils.decorators import method_decorator

from social_django.utils import load_strategy, load_backend
from social_core.exceptions import MissingBackend, AuthException

from .models import User, LoginHistory
from .serializers import (
    UserSerializer, UserProfileSerializer, OTPVerificationSerializer,
    RequestPasswordResetSerializer, VerifyPasswordResetTokenSerializer,
    PasswordResetSerializer
)
from .permissions import IsOwnerOrReadOnly
from .services import (
    generate_and_send_otp, verify_user_otp,
    get_tokens_for_user, generate_password_reset_token,
    verify_password_reset_token, reset_password
)

logger = logging.getLogger('users')


def get_client_ip(request):
    """Extract client IP address from request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR', 'unknown')
    return ip


def get_user_agent(request):
    """Extract user agent from request."""
    return request.META.get('HTTP_USER_AGENT', '')


def _log_login_attempt(user=None, email=None, success=True, failure_reason=None, 
                      interface_type='USER', request=None):
    """Helper function to log login attempts with enhanced tracking"""
    try:
        # Get IP address and user agent
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
        
        # Create login record
        login_data = {
            'ip_address': ip_address,
            'user_agent': user_agent,
            'is_successful': success,
            'interface_type': interface_type,
            'login_method': 'EMAIL'
        }
        
        if user:
            login_data['user'] = user
        
        if failure_reason:
            login_data['failure_reason'] = failure_reason
        
        return LoginHistory.objects.create(**login_data)
        
    except Exception as e:
        logger.error(f"Login logging error: {str(e)}")
        return None


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def get_permissions(self) -> List:
        # ✅ FIXED: Added 'verify_otp' to the AllowAny group for signup flow
        if self.action in ['create', 'oauth_login', 'login', 'check_login_eligibility',
                          'request_password_reset', 'verify_reset_token', 'reset_password_confirm', 'verify_otp']:
            return [AllowAny()]
        # ✅ FIXED: Removed 'verify_otp' from the authenticated group
        elif self.action in ['update', 'partial_update', 'destroy', 'resend_otp']:
            return [IsOwnerOrReadOnly()]
        return [IsAuthenticated()]

    @method_decorator(ratelimit(key='ip', rate='3/m', method='POST'))
    def create(self, request: Request) -> Response:
        """
        Enhanced user registration - REMOVED photographer signup toggle
        All users register as regular users, can become photographers later
        """
        try:
            # Extract and validate data
            first_name = request.data.get('first_name', '').strip()
            last_name = request.data.get('last_name', '').strip()
            email = request.data.get('email', '').lower().strip()
            password = request.data.get('password', '')
            confirm_password = request.data.get('confirm_password', '')
            
            # Validation
            if not all([first_name, last_name, email, password, confirm_password]):
                return Response({
                    'message': 'All fields are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if password != confirm_password:
                return Response({
                    'message': 'Passwords do not match'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if len(password) < 8:
                return Response({
                    'message': 'Password must be at least 8 characters long'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if user already exists
            if User.objects.filter(email=email).exists():
                return Response({
                    'message': 'User with this email already exists'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Create user account (always as regular user initially)
            with transaction.atomic():
                user = User.objects.create_user(
                    username=email,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    password=password,
                    user_type='USER',  # All new users start as regular users
                    account_status='ACTIVE',
                    auth_provider='EMAIL'
                )
                
                # Initialize default preferences
                user.privacy_settings = {
                    'profile_visibility': 'public',
                    'show_in_search': True,
                    'allow_tagging': True
                }
                user.notification_preferences = {
                    'email_notifications': True,
                    'push_notifications': True,
                    'marketing_emails': False
                }
                user.save()

            # Record registration IP
            user.last_login_ip = get_client_ip(request)
            user.save(update_fields=['last_login_ip'])

            logger.info(f"User created: {user.id} - {user.email} - IP: {user.last_login_ip}")

            # Send email verification
            generate_and_send_otp(user)

            return Response({
                'message': 'Account created successfully',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name
                },
                'next_step': 'Please verify your email address'
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"User creation failed: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred during registration'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(ratelimit(key='ip', rate='5/m', method='POST'))
    @action(detail=False, methods=['post'])
    def login(self, request: Request) -> Response:
        """
        Enhanced login with photographer toggle and admin detection
        
        Request format:
        {
            "email": "<EMAIL>",
            "password": "password123",
            "login_as_photographer": false,  # NEW: Toggle for interface type
            "remember_me": false
        }
        """
        try:
            email = request.data.get('email', '').lower().strip()
            password = request.data.get('password', '')
            login_as_photographer = request.data.get('login_as_photographer', False)
            remember_me = request.data.get('remember_me', False)
            
            if not email or not password:
                return Response({
                    'message': 'Email and password are required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Authenticate user
            user = authenticate(username=email, password=password)
            
            if not user:
                # Log failed login attempt
                _log_login_attempt(
                    email=email,
                    success=False,
                    failure_reason="Invalid credentials",
                    request=request
                )
                return Response({
                    'message': 'Invalid email or password'
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # Check account status
            if user.account_status != 'ACTIVE':
                _log_login_attempt(
                    user=user,
                    success=False,
                    failure_reason=f"Account status: {user.account_status}",
                    request=request
                )
                return Response({
                    'message': 'Account is not active'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Determine interface type based on toggle and admin status
            interface_type = 'PHOTOGRAPHER' if login_as_photographer else 'USER'
            
            # Generate JWT tokens
            tokens = get_tokens_for_user(user)
            
            # Update last login
            user.last_login_at = timezone.now()
            user.last_login_ip = get_client_ip(request)
            user.save(update_fields=['last_login_at', 'last_login_ip'])
            
            # Log successful login
            login_record = _log_login_attempt(
                user=user,
                success=True,
                interface_type=interface_type,
                request=request
            )
            
            # Check if email is verified
            if not user.is_email_verified:
                logger.info(f"Login attempt by unverified user: {user.id} - {user.email}")
                generate_and_send_otp(user)
                return Response({
                    'message': 'Please verify your email. OTP has been sent.',
                    'user_id': str(user.id),
                    'requires_verification': True
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Prepare response data
            response_data = {
                'message': 'Login successful',
                'user': UserSerializer(user).data,
                'tokens': tokens,
                'session_info': {
                    'login_id': str(login_record.id) if login_record else None,
                    'interface_type': interface_type,
                    'login_time': user.last_login_at.isoformat()
                }
            }
            
            # Check if user is PhotoFish admin and provide interface options
            if user.can_access_admin_panel():
                response_data['admin_access'] = True
                response_data['interface_options'] = user.get_interface_options()
                response_data['message'] = 'Admin login successful - Please select your interface'
            else:
                response_data['admin_access'] = False
                response_data['selected_interface'] = interface_type
            
            logger.info(f"User logged in: {user.id} - {user.email} as {interface_type} from IP: {get_client_ip(request)}")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Login error: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred during login'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    @permission_classes([IsAuthenticated])
    def select_admin_interface(self, request: Request) -> Response:
        """
        NEW: Allow PhotoFish admins to select their interface after login
        
        Request format:
        {
            "interface_type": "admin|user|photographer"
        }
        """
        try:
            user = request.user
            interface_type = request.data.get('interface_type', '').lower()
            
            # Validate admin access
            if not user.can_access_admin_panel():
                return Response({
                    'message': 'Admin access denied'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Validate interface type
            valid_interfaces = ['admin', 'user', 'photographer']
            if interface_type not in valid_interfaces:
                return Response({
                    'message': f'Invalid interface type. Must be one of: {", ".join(valid_interfaces)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Map interface type to internal format
            interface_mapping = {
                'admin': 'ADMIN',
                'user': 'USER', 
                'photographer': 'PHOTOGRAPHER'
            }
            
            selected_interface = interface_mapping[interface_type]
            
            # Update login history with selected interface
            try:
                login_record = user.login_history.filter(
                    logout_datetime__isnull=True
                ).order_by('-login_datetime').first()
                
                if login_record:
                    login_record.interface_type = selected_interface
                    login_record.save()
            except Exception as e:
                logger.warning(f"Could not update login history: {str(e)}")
            
            # Return appropriate response based on selection
            response_data = {
                'message': f'Interface selected: {interface_type.title()}',
                'selected_interface': selected_interface,
                'user': UserSerializer(user).data
            }
            
            # Add interface-specific data
            if selected_interface == 'ADMIN':
                response_data['admin_permissions'] = user.admin_permissions
                response_data['redirect_url'] = '/admin/dashboard'
            elif selected_interface == 'PHOTOGRAPHER':
                response_data['redirect_url'] = '/photographer/dashboard'
            else:
                response_data['redirect_url'] = '/user/dashboard'
            
            logger.info(f"Admin {user.email} selected interface: {selected_interface}")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Interface selection error: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred during interface selection'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(ratelimit(key='ip', rate='3/m', method='POST'))
    @action(detail=False, methods=['post'])
    def oauth_login(self, request: Request) -> Response:
        """Enhanced OAuth login with admin detection and photographer toggle."""
        provider = request.data.get('provider')
        access_token = request.data.get('access_token')
        login_as_photographer = request.data.get('login_as_photographer', False)
        client_ip = get_client_ip(request)

        if not provider or not access_token:
            logger.warning(f"OAuth login attempt with missing data: provider={provider} from IP: {client_ip}")
            return Response({
                'message': 'Provider and access token are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Only allow Google OAuth
        if provider not in ['google-oauth2']:
            logger.warning(f"Unsupported OAuth provider attempted: {provider} from IP: {client_ip}")
            return Response({
                'message': f'Provider {provider} is not supported'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            strategy = load_strategy(request)
            backend = load_backend(strategy=strategy, name=provider, redirect_uri=None)

            user = backend.do_auth(access_token)

            if user:
                # Check account status for existing users
                if user.pk and user.account_status != 'ACTIVE':
                    logger.warning(f"OAuth login attempt for inactive account: {user.id}")
                    return Response({
                        'message': 'Account is not active. Please contact support.'
                    }, status=status.HTTP_403_FORBIDDEN)

                user.auth_provider = provider
                user.is_email_verified = True
                user.last_login_ip = client_ip
                user.last_login_at = timezone.now()

                is_new_user = user._state.adding
                if is_new_user:
                    user.user_type = 'USER'  # New users start as regular users

                user.save()

                # Determine interface type
                interface_type = 'PHOTOGRAPHER' if login_as_photographer else 'USER'
                
                # Log login
                _log_login_attempt(
                    user=user,
                    success=True,
                    interface_type=interface_type,
                    request=request
                )

                logger.info(f"OAuth login successful: {user.id} - {user.email} - Provider: {provider} as {interface_type}")
                
                tokens = get_tokens_for_user(user)
                response_data = {
                    'message': f'{provider} login successful',
                    'user': UserSerializer(user).data,
                    'tokens': tokens
                }
                
                # Check admin access
                if user.can_access_admin_panel():
                    response_data['admin_access'] = True
                    response_data['interface_options'] = user.get_interface_options()
                    response_data['message'] = 'Admin OAuth login successful - Please select your interface'
                else:
                    response_data['admin_access'] = False
                    response_data['selected_interface'] = interface_type
                
                return Response(response_data)

            logger.warning(f"OAuth authentication failed for provider: {provider} from IP: {client_ip}")
            return Response({'message': 'Authentication failed'}, status=status.HTTP_401_UNAUTHORIZED)

        except MissingBackend:
            logger.error(f"OAuth provider not supported: {provider} from IP: {client_ip}")
            return Response({
                'message': f'Provider {provider} not supported'
            }, status=status.HTTP_400_BAD_REQUEST)
        except AuthException as e:
            logger.error(f"OAuth authentication error: {str(e)} for provider: {provider}")
            return Response({
                'message': 'Authentication failed. Please check your credentials.'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            logger.error(f"OAuth login error: {str(e)} for provider: {provider}", exc_info=True)
            return Response({
                'message': 'An error occurred during authentication'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(ratelimit(key='ip', rate='5/m', method='POST'))
    @action(detail=True, methods=['post'])
    def verify_otp(self, request: Request, pk=None) -> Response:
        """Enhanced email OTP verification - NOW ALLOWS UNAUTHENTICATED ACCESS."""
        user = self.get_object()
        otp = request.data.get('otp')

        if not otp:
            logger.warning(f"OTP verification attempt without OTP: User {user.id}")
            return Response({'message': 'OTP is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            is_verified, error_message, verification = verify_user_otp(user, otp)

            if not is_verified:
                return Response({'message': error_message}, status=status.HTTP_400_BAD_REQUEST)

            # Successful verification
            user.is_email_verified = True
            user.last_login_ip = get_client_ip(request)
            user.save()

            logger.info(f"User email verified successfully: {user.id} - {user.email}")
            
            tokens = get_tokens_for_user(user)
            response_data = {
                'message': 'Email verified successfully',
                'user': UserSerializer(user).data,
                'tokens': tokens
            }
            
            # Check admin access for newly verified users
            if user.can_access_admin_panel():
                response_data['admin_access'] = True
                response_data['interface_options'] = user.get_interface_options()
            else:
                response_data['admin_access'] = False
            
            return Response(response_data)
            
        except Exception as e:
            logger.error(f"OTP verification error for user {user.id}: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred during verification'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @method_decorator(ratelimit(key='ip', rate='3/m', method='POST'))
    @action(detail=True, methods=['post'])
    def resend_otp(self, request: Request, pk=None) -> Response:
        """Enhanced OTP resend with rate limiting."""
        user = self.get_object()
        
        try:
            logger.info(f"Email OTP resend requested for user: {user.id} - {user.email}")
            generate_and_send_otp(user)
            return Response({'message': 'New OTP has been sent to your email'})
                
        except Exception as e:
            logger.error(f"Error resending OTP for user {user.id}: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred while sending OTP'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    @permission_classes([IsAuthenticated])
    def logout(self, request: Request) -> Response:
        """Enhanced logout with session cleanup"""
        try:
            user = request.user
            
            # Update login history
            try:
                login_record = user.login_history.filter(
                    logout_datetime__isnull=True
                ).order_by('-login_datetime').first()
                
                if login_record:
                    login_record.logout_datetime = timezone.now()
                    login_record.save()
            except Exception as e:
                logger.warning(f"Could not update logout time: {str(e)}")
            
            logger.info(f"User logged out: {user.id} - {user.email}")
            
            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Logout error: {str(e)}", exc_info=True)
            return Response({
                'message': 'Logout completed with warnings'
            }, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    @permission_classes([IsAuthenticated])
    def profile(self, request: Request) -> Response:
        """Get current user profile with admin status"""
        try:
            user = request.user
            return Response({
                'user': UserProfileSerializer(user).data,
                'admin_access': user.can_access_admin_panel(),
                'interface_options': user.get_interface_options() if user.can_access_admin_panel() else None
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Profile fetch error: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred while fetching profile'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # Password Reset Endpoints (keeping existing functionality)
    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def request_password_reset(self, request: Request) -> Response:
        """Password reset request."""
        serializer = RequestPasswordResetSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data.get('email')

        try:
            user = User.objects.filter(email=email).first()
            if not user:
                logger.warning(f"Password reset requested for non-existent email: {email}")
                return Response({'message': 'If your email exists in our system, you will receive a password reset link'})

            success, _ = generate_password_reset_token(user)

            if not success:
                return Response({
                    'message': 'Failed to send password reset email, please try again later'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return Response({'message': 'If your email exists in our system, you will receive a password reset link'})
        except Exception as e:
            logger.error(f"Error in password reset request: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred during password reset request'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def verify_reset_token(self, request: Request) -> Response:
        """Verify password reset token."""
        serializer = VerifyPasswordResetTokenSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        token = serializer.validated_data.get('token')

        try:
            is_valid, error_message, _ = verify_password_reset_token(token)

            if not is_valid:
                return Response({'message': error_message}, status=status.HTTP_400_BAD_REQUEST)

            return Response({'message': 'Token is valid', 'valid': True})
        except Exception as e:
            logger.error(f"Error verifying reset token: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred while verifying token'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def reset_password_confirm(self, request: Request) -> Response:
        """Reset password confirmation."""
        serializer = PasswordResetSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        token = serializer.validated_data.get('token')
        password = serializer.validated_data.get('password')

        try:
            is_valid, error_message, reset_request = verify_password_reset_token(token)

            if not is_valid:
                return Response({'message': error_message}, status=status.HTTP_400_BAD_REQUEST)

            success, error = reset_password(reset_request, password)

            if not success:
                return Response({'message': error}, status=status.HTTP_400_BAD_REQUEST)

            return Response({'message': 'Password has been reset successfully'})
        except Exception as e:
            logger.error(f"Error during password reset confirmation: {str(e)}", exc_info=True)
            return Response({
                'message': 'An error occurred during password reset'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update(self, request: Request, *args, **kwargs) -> Response:
        """Enhanced user update with security logging."""
        try:
            instance = self.get_object()
            old_email = instance.email
            
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            updated_user = serializer.save()

            # Log security-relevant changes
            if old_email != updated_user.email:
                logger.info(f"User email changed: {updated_user.id} - {old_email} -> {updated_user.email}")

            logger.info(f"User updated: {updated_user.id} - {updated_user.email}")

            return Response(UserProfileSerializer(updated_user).data)
        except Exception as e:
            logger.error(f"Error updating user: {str(e)}", exc_info=True)
            raise

    def destroy(self, request: Request, *args, **kwargs) -> Response:
        """Enhanced user deletion with security logging."""
        try:
            instance = self.get_object()
            user_id = instance.id
            user_email = instance.email

            self.perform_destroy(instance)

            logger.info(f"User deleted: {user_id} - {user_email} - Requested by: {request.user.id if request.user.is_authenticated else 'Anonymous'}")

            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logger.error(f"Error deleting user: {str(e)}", exc_info=True)
            raise


# Standalone view functions for backward compatibility
@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """Standalone login view - delegates to UserViewSet"""
    viewset = UserViewSet()
    viewset.action = 'login'
    viewset.request = request
    return viewset.login(request)

@api_view(['POST'])
@permission_classes([AllowAny])
def register_view(request):
    """Standalone register view - delegates to UserViewSet"""
    viewset = UserViewSet()
    viewset.action = 'create'
    viewset.request = request
    return viewset.create(request)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def select_admin_interface(request):
    """Standalone admin interface selection view"""
    viewset = UserViewSet()
    viewset.action = 'select_admin_interface'
    viewset.request = request
    return viewset.select_admin_interface(request)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """Standalone logout view"""
    viewset = UserViewSet()
    viewset.action = 'logout'
    viewset.request = request
    return viewset.logout(request)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_profile_view(request):
    """Standalone profile view"""
    viewset = UserViewSet()
    viewset.action = 'profile'
    viewset.request = request
    return viewset.profile(request)

@api_view(['POST'])
@permission_classes([AllowAny])
def verify_otp_view(request, user_id):
    """Standalone OTP verification view"""
    try:
        user = User.objects.get(id=user_id)
        viewset = UserViewSet()
        viewset.action = 'verify_otp'
        viewset.request = request
        return viewset.verify_otp(request, pk=user_id)
    except User.DoesNotExist:
        return Response({'message': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([AllowAny])
def resend_otp_view(request, user_id):
    """Standalone OTP resend view"""
    try:
        user = User.objects.get(id=user_id)
        viewset = UserViewSet()
        viewset.action = 'resend_otp'
        viewset.request = request
        return viewset.resend_otp(request, pk=user_id)
    except User.DoesNotExist:
        return Response({'message': 'User not found'}, status=status.HTTP_404_NOT_FOUND)